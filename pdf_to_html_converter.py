#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF批量转换为HTML脚本
支持递归扫描目录，批量转换PDF文件为HTML格式
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Tuple, Optional, Dict
import time
import base64
import hashlib
from dataclasses import dataclass

try:
    import fitz  # PyMuPDF
except ImportError:
    print("错误: 请安装PyMuPDF库")
    print("运行: pip install PyMuPDF")
    sys.exit(1)

try:
    from tqdm import tqdm
except ImportError:
    print("警告: 未安装tqdm库，将使用简单进度显示")
    print("建议运行: pip install tqdm")
    tqdm = None

try:
    from PIL import Image
    import io
    PIL_AVAILABLE = True
except ImportError:
    print("警告: 未安装Pillow库，图片处理功能可能受限")
    print("建议运行: pip install Pillow")
    PIL_AVAILABLE = False


@dataclass
class ConversionResult:
    """转换结果数据类"""
    total_files: int = 0
    successful: int = 0
    failed: int = 0
    failed_files: List[str] = None
    
    def __post_init__(self):
        if self.failed_files is None:
            self.failed_files = []


class PDFToHTMLConverter:
    """PDF转HTML转换器"""

    def __init__(self, output_dir: Optional[str] = None, recursive: bool = True,
                 extract_images: bool = True, embed_images: bool = False):
        self.output_dir = output_dir
        self.recursive = recursive
        self.extract_images = extract_images
        self.embed_images = embed_images  # True: 嵌入base64, False: 保存为文件
        self.logger = self._setup_logger()
        self.image_counter = 0
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('pdf_converter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def find_pdf_files(self, input_dir: str) -> List[Path]:
        """查找指定目录中的所有PDF文件"""
        input_path = Path(input_dir)
        
        if not input_path.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")
        
        if not input_path.is_dir():
            raise NotADirectoryError(f"输入路径不是目录: {input_dir}")
        
        pdf_files = []
        
        if self.recursive:
            # 递归查找所有PDF文件
            pdf_files = list(input_path.rglob("*.pdf"))
            pdf_files.extend(list(input_path.rglob("*.PDF")))
        else:
            # 只查找当前目录的PDF文件
            pdf_files = list(input_path.glob("*.pdf"))
            pdf_files.extend(list(input_path.glob("*.PDF")))
        
        self.logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        return pdf_files
    
    def extract_images_from_page(self, page, page_num: int, pdf_name: str,
                                output_dir: Optional[Path]) -> List[Dict]:
        """从页面提取图片"""
        images = []

        if not self.extract_images:
            return images

        try:
            # 获取页面中的图片列表
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                try:
                    # 获取图片数据
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)

                    # 跳过CMYK图片（转换复杂）
                    if pix.n - pix.alpha < 4:
                        # 生成图片文件名
                        img_name = f"{pdf_name}_page{page_num+1}_img{img_index+1}"

                        if self.embed_images:
                            # 嵌入为base64
                            if pix.n - pix.alpha == 1:  # 灰度图
                                img_data = pix.tobytes("png")
                                img_format = "png"
                            else:  # RGB图
                                img_data = pix.tobytes("png")
                                img_format = "png"

                            img_base64 = base64.b64encode(img_data).decode()
                            img_src = f"data:image/{img_format};base64,{img_base64}"

                        else:
                            # 保存为文件
                            if output_dir is None:
                                self.logger.warning("输出目录未设置，跳过图片保存")
                                continue

                            if pix.n - pix.alpha == 1:  # 灰度图
                                img_filename = f"{img_name}.png"
                                img_path = output_dir / img_filename
                                pix.save(str(img_path))
                            else:  # RGB图
                                img_filename = f"{img_name}.png"
                                img_path = output_dir / img_filename
                                pix.save(str(img_path))

                            img_src = f"images/{img_filename}"

                        # 获取图片尺寸
                        img_width = pix.width
                        img_height = pix.height

                        images.append({
                            'src': img_src,
                            'width': img_width,
                            'height': img_height,
                            'alt': f"图片 {img_index + 1}"
                        })

                        self.image_counter += 1

                    pix = None  # 释放内存

                except Exception as e:
                    self.logger.warning(f"提取图片失败 (页面{page_num+1}, 图片{img_index+1}): {str(e)}")
                    continue

        except Exception as e:
            self.logger.warning(f"页面{page_num+1}图片提取失败: {str(e)}")

        return images

    def extract_pdf_content(self, pdf_path: Path) -> str:
        """从PDF文件提取文本和图片内容"""
        try:
            doc = fitz.open(str(pdf_path))
            content = []

            # 创建图片输出目录
            if self.extract_images and not self.embed_images:
                if self.output_dir:
                    img_output_dir = Path(self.output_dir) / "images"
                else:
                    img_output_dir = pdf_path.parent / "images"
                img_output_dir.mkdir(parents=True, exist_ok=True)
            else:
                img_output_dir = None

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)

                # 添加页面标题
                content.append(f"<h2>第 {page_num + 1} 页</h2>\n")

                # 提取图片
                if self.extract_images:
                    images = self.extract_images_from_page(
                        page, page_num, pdf_path.stem, img_output_dir
                    )

                    # 添加图片到内容中
                    for img in images:
                        img_html = f'<div class="image-container">\n'
                        img_html += f'  <img src="{img["src"]}" alt="{img["alt"]}" '
                        img_html += f'style="max-width: 100%; height: auto;" />\n'
                        img_html += f'  <p class="image-caption">{img["alt"]}</p>\n'
                        img_html += f'</div>\n'
                        content.append(img_html)

                # 提取文本
                text = page.get_text()
                if text.strip():
                    # 将文本按段落分割并添加HTML标签
                    paragraphs = text.split('\n\n')
                    for para in paragraphs:
                        if para.strip():
                            # 简单的文本清理和HTML转义
                            para = para.replace('&', '&amp;')
                            para = para.replace('<', '&lt;')
                            para = para.replace('>', '&gt;')
                            para = para.replace('\n', '<br>\n')
                            content.append(f"<p>{para}</p>\n")

            doc.close()
            return '\n'.join(content)

        except Exception as e:
            raise Exception(f"PDF内容提取失败: {str(e)}")
    
    def generate_html(self, content: str, title: str) -> str:
        """生成HTML文档"""
        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }}
        p {{
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }}
        .image-container {{
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }}
        .image-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .image-caption {{
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }}
        .footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{title}</h1>
        {content}
        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>"""
        return html_template
    
    def get_output_path(self, pdf_path: Path, input_dir: str) -> Path:
        """获取输出HTML文件路径"""
        if self.output_dir:
            # 如果指定了输出目录，保持相对路径结构
            output_base = Path(self.output_dir)
            relative_path = pdf_path.relative_to(Path(input_dir))
            output_path = output_base / relative_path.with_suffix('.html')
        else:
            # 在原文件目录生成HTML文件
            output_path = pdf_path.with_suffix('.html')
        
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        return output_path
    
    def convert_single_pdf(self, pdf_path: Path, input_dir: str) -> bool:
        """转换单个PDF文件"""
        try:
            self.logger.info(f"正在转换: {pdf_path.name}")
            
            # 提取PDF内容
            content = self.extract_pdf_content(pdf_path)
            
            if not content.strip():
                raise Exception("PDF文件为空或无法提取内容")
            
            # 生成HTML
            title = pdf_path.stem
            html_content = self.generate_html(content, title)
            
            # 获取输出路径并保存
            output_path = self.get_output_path(pdf_path, input_dir)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"转换成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"转换失败 {pdf_path.name}: {str(e)}")
            return False
    
    def convert_batch(self, input_dir: str) -> ConversionResult:
        """批量转换PDF文件"""
        result = ConversionResult()
        
        try:
            # 查找所有PDF文件
            pdf_files = self.find_pdf_files(input_dir)
            result.total_files = len(pdf_files)
            
            if result.total_files == 0:
                self.logger.warning("未找到PDF文件")
                return result
            
            # 创建进度条
            if tqdm:
                progress_bar = tqdm(pdf_files, desc="转换进度", unit="文件")
            else:
                progress_bar = pdf_files
                print(f"开始转换 {result.total_files} 个文件...")
            
            # 逐个转换文件
            for i, pdf_path in enumerate(progress_bar):
                if not tqdm:
                    print(f"[{i+1}/{result.total_files}] 正在处理: {pdf_path.name}")
                
                if self.convert_single_pdf(pdf_path, input_dir):
                    result.successful += 1
                else:
                    result.failed += 1
                    result.failed_files.append(str(pdf_path))
            
            return result
            
        except Exception as e:
            self.logger.error(f"批量转换过程中发生错误: {str(e)}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="PDF批量转换为HTML工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python pdf_to_html_converter.py /path/to/pdfs
  python pdf_to_html_converter.py /path/to/pdfs -o /path/to/output
  python pdf_to_html_converter.py /path/to/pdfs --no-recursive
        """
    )
    
    parser.add_argument(
        'input_dir',
        help='包含PDF文件的输入目录路径'
    )
    
    parser.add_argument(
        '-o', '--output',
        dest='output_dir',
        help='输出目录路径（可选，默认在原文件目录生成HTML）'
    )
    
    parser.add_argument(
        '--no-recursive',
        action='store_true',
        help='不递归处理子目录'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细日志信息'
    )

    parser.add_argument(
        '--no-images',
        action='store_true',
        help='不提取PDF中的图片'
    )

    parser.add_argument(
        '--embed-images',
        action='store_true',
        help='将图片嵌入HTML中（base64编码），而不是保存为独立文件'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger('pdf_converter').setLevel(logging.DEBUG)
    
    try:
        # 创建转换器
        converter = PDFToHTMLConverter(
            output_dir=args.output_dir,
            recursive=not args.no_recursive,
            extract_images=not args.no_images,
            embed_images=args.embed_images
        )
        
        # 执行批量转换
        print("=" * 60)
        print("PDF批量转换为HTML工具")
        print("=" * 60)
        
        start_time = time.time()
        result = converter.convert_batch(args.input_dir)
        end_time = time.time()
        
        # 显示结果摘要
        print("\n" + "=" * 60)
        print("转换完成！结果摘要:")
        print("=" * 60)
        print(f"总文件数: {result.total_files}")
        print(f"成功转换: {result.successful}")
        print(f"转换失败: {result.failed}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        if result.failed_files:
            print("\n失败的文件:")
            for failed_file in result.failed_files:
                print(f"  - {failed_file}")
        
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
