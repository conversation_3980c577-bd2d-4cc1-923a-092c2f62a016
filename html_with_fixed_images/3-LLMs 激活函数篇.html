<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-LLMs 激活函数篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>3-LLMs 激活函数篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img6.png" alt="图片 6" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 6</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img7.png" alt="图片 7" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 7</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img8.png" alt="图片 8" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 8</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page1_img9.png" alt="图片 9" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 9</p>
</div>

<p>LLMs 激活函数篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:41<br>
1 介绍一下 FFN 块 计算公式？<br>
2 介绍一下 GeLU 计算公式？<br>
3 介绍一下 Swish 计算公式？<br>
2个可训练权重矩阵，中间维度为 4h<br>
4 介绍一下 使用 GLU 线性门控单元的 FFN 块 计算公式？<br>
5 介绍一下 使用 GeLU 的 GLU 块 计算公式？<br>
6 介绍一下 使用 Swish 的 GLU 块 计算公式？<br>
3个可训练权重矩阵，中间维度为 4h*2/3<br>
各LLMs 都使用哪种激活函数？<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/3-LLMs 激活函数篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>4h = 4*4096 = 16384<br>
2/3 * 4h = 10022 -&gt; 11008<br>
11008/128 = 86<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:13</p>
        </div>
    </div>
</body>
</html>