<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 14:07<br>
地址：https://siwei.io/graph-rag/<br>
一、为什么需要 Graph RAG？<br>
虽然 llamaindex 能够 利用摘要索引进行增强的方案，但这些都是利用非结构化文本在做。<br>
对于 知识图谱，是否可以将其 作为一路召回，提高检索的相关性，这个可以利用好知识图谱内部的知识。<br>
知识图谱可以减少基于嵌入的语义搜索所导致的不准确性。<br>
eg: “保温大棚”与“保温杯”，尽管在语义上两者是存在相关性的，但在大多数场景下，这种通用语<br>
义（Embedding）下的相关性很高，进而作为错误的上下文而引入“幻觉”。这时候，可以利用领<br>
域知识的知识图谱来缓解这种幻觉。<br>
二、什么是 Graph RAG？<br>
Graph RAG（Retrieval-Augmented Generation），是一种基于知识图谱的检索增强技术，通过构建图模型的知<br>
识表达，将实体和关系之间的联系用图的形式进行展示，然后利用大语言模型 LLM进行检索增强。<br>
三、Graph RAG 思路介绍？<br>
Graph RAG将知识图谱等价于一个超大规模的词汇表，而实体和关系则对应于单词。通过这种方式，Graph <br>
RAG 在检索时能够将实体和关系作为单元进行联合建模。<br>
Graph RAG 思想：对用户输入的query提取实体，然后构造子图形成上下文，最后送入大模型完成生成<br>
四、用代码 介绍 Graph RAG ？<br>
• Graph RAG（Retrieval-Augmented Generation） 面 —— 一种 基于知识图谱的大模型检索增强实现策略<br>
• 一、为什么需要 Graph RAG？<br>
• 二、什么是 Graph RAG？<br>
• 三、Graph RAG 思路介绍？<br>
• 四、用代码 介绍 Graph RAG ？<br>
• 五、用 示例 介绍 Graph RAG ？<br>
• 六、Graph RAG 排序优化方式？<br>
• 致谢<br>
def simple_graph_rag(query_str, nebulagraph_store, llm):<br>
    entities = _get_key_entities(query_str, llm)<br>
    graph_rag_context = _retrieve_subgraph_context(entities)<br>
    return _synthesize_answer(<br>
        query_str, graph_rag_context, llm)<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>这样一来，知识图谱召回可以作为一路和传统的召回进行融合。``<br>
五、用 示例 介绍 Graph RAG ？<br>
示例一：当用户输入，tell me about Peter quill时，先识别关键词quil，编写cypher语句获得二跳<br>
结果。<br>
示例二<br>
用户输入：Tell me events about NASA<br>
得到关键词：Query keywords: ['NASA', 'events']<br>
召回二度逻辑：<br>
1. 使用LLM(或其他)模型从问题中提取关键实体。<br>
def _get_key_entities(query_str, llm=None ,with_llm=True):<br>
    ...<br>
    return _expand_synonyms(entities)<br>
2. 根据这些实体检索子图，深入到一定的深度，例如可以是2度甚至更多。<br>
def _get_key_entities(query_str, llm=None ,with_llm=True):<br>
    ...<br>
    return _expand_synonyms(entities)<br>
3. 利用获得的上下文利用LLM产生答案<br>
def _synthesize_answer(query_str, graph_rag_context, llm):<br>
    return llm.predict(PROMPT_SYNTHESIZE_AND_REFINE, query_str, graph_rag_context)<br>
Extracted relationships: The following are knowledge triplets in max depth 2 in the <br>
form of `subject [predicate, object, predicate_next_hop, object_next_hop ...]<br>
nasa ['public release date', 'mid-2023']<br>
</p>

<h2>第 3 页</h2>

<p>送入LLM完成问答。<br>
nasa ['announces', 'future space telescope programs']<br>
nasa ['publishes images of', 'debris disk']<br>
nasa ['discovers', 'exoplanet lhs 475 b']<br>
INFO:llama_index.indices.knowledge_graph.retriever:&gt; Starting query: Tell me events <br>
about NASA<br>
&gt; Starting query: Tell me events about NASA<br>
&gt; Starting query: Tell me events about NASA<br>
INFO:llama_index.indices.knowledge_graph.retriever:&gt; Query keywords: ['NASA', <br>
'events']<br>
&gt; Query keywords: ['NASA', 'events']<br>
&gt; Query keywords: ['NASA', 'events']<br>
INFO:llama_index.indices.knowledge_graph.retriever:&gt; Extracted relationships: The <br>
following are knowledge triplets in max depth 2 in the form of `subject [predicate, <br>
object, predicate_next_hop, object_next_hop ...]`<br>
nasa ['public release date', 'mid-2023']<br>
nasa ['announces', 'future space telescope programs']<br>
nasa ['publishes images of', 'debris disk']<br>
nasa ['discovers', 'exoplanet lhs 475 b']<br>
&gt; Extracted relationships: The following are knowledge triplets in max depth 2 in <br>
the form of `subject [predicate, object, predicate_next_hop, object_next_hop ...]`<br>
nasa ['public release date', 'mid-2023']<br>
nasa ['announces', 'future space telescope programs']<br>
nasa ['publishes images of', 'debris disk']<br>
nasa ['discovers', 'exoplanet lhs 475 b']<br>
&gt; Extracted relationships: The following are knowledge triplets in max depth 2 in <br>
the form of `subject [predicate, object, predicate_next_hop, object_next_hop ...]`<br>
nasa ['public release date', 'mid-2023']<br>
nasa ['announces', 'future space telescope programs']<br>
nasa ['publishes images of', 'debris disk']<br>
nasa ['discovers', 'exoplanet lhs 475 b']<br>
INFO:llama_index.token_counter.token_counter:&gt; [get_response] Total LLM token <br>
usage: 159 tokens<br>
&gt; [get_response] Total LLM token usage: 159 tokens<br>
&gt; [get_response] Total LLM token usage: 159 tokens<br>
INFO:llama_index.token_counter.token_counter:&gt; [get_response] Total embedding token <br>
usage: 0 tokens<br>
&gt; [get_response] Total embedding token usage: 0 tokens<br>
&gt; [get_response] Total embedding token usage: 0 tokens<br>
INFO:llama_index.token_counter.token_counter:&gt; [get_response] Total LLM token <br>
usage: 159 tokens<br>
&gt; [get_response] Total LLM token usage: 159 tokens<br>
&gt; [get_response] Total LLM token usage: 159 tokens<br>
INFO:llama_index.token_counter.token_counter:&gt; [get_response] Total embedding token <br>
usage: 0 tokens<br>
&gt; [get_response] Total embedding token usage: 0 tokens<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="images/25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>六、Graph RAG 排序优化方式？<br>
基于知识图谱召回的方法可以和其他召回方法一起融合，但这种方式在图谱规模很大时其实是有提升空间的。<br>
因此，还可以再加入路径排序环节，可参考先粗排后精排的方式，同样走过滤逻辑。<br>
例如，<br>
在粗排阶段，根据问题query和候选路径path的特征，对候选路径进行粗排，采用LightGBM机器学习模型，保<br>
留topn条路径：<br>
在精排阶段，采用预训练语言模型，计算query和粗排阶段 的path的语义匹配度，选择得分top2-3答案路径作为<br>
答案。<br>
&gt; [get_response] Total embedding token usage: 0 tokens<br>
• 突出的缺点在于：<br>
• 子图召回的多条路径中可能会出现多种不相关的。<br>
• 实体识别阶段的精度也有限，采用关键词提取还比较暴力，方法也值得商榷。<br>
• 这种方式依赖于一个基础知识图谱库，如果数据量以及广度不够，有可能会引入噪声。<br>
• 字符重合数<br>
• 词重合数<br>
• 编辑距离<br>
• path跳数<br>
• path长度<br>
• 字符的Jaccard相似度<br>
• 词语的Jaccard相似度<br>
• path中的关系数<br>
• path中的实体个数<br>
• path中的答案个数<br>
• 判断path的字符是否全在query中<br>
• 判断query和path中是否都包含数字 • 获取数字的Jaccrad的相似度<br>
</p>

<h2>第 5 页</h2>

<div class="image-container">
  <img src="images/25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略_page5_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/25-Graph RAG 面 — 一种 基于知识图谱的大模型检索增强实现策略_page5_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:13</p>
        </div>
    </div>
</body>
</html>