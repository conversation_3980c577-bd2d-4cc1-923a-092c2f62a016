<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>27-适配器微调（Adapter-tuning）篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>27-适配器微调（Adapter-tuning）篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/27-适配器微调（Adapter-tuning）篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/27-适配器微调（Adapter-tuning）篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>适配器微调（Adapter-tuning）篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月18日 20:56<br>
一、为什么 需要 适配器微调（Adapter-tuning）？<br>
二、适配器微调（Adapter-tuning）思路？<br>
三、 适配器微调（Adapter-tuning）特点是什么？<br>
四、AdapterFusion 思路 是什么？<br>
五、AdapterDrop 思路 是什么？<br>
六、AdapterDrop 特点 是什么？<br>
七、MAM Adapter 思路 是什么？<br>
八、MAM Adapter 特点 是什么？<br>
1. 预训练模型参数量变多，在特定任务下进行全量微调即昂贵又耗时；<br>
• 设计了Adapter结构（首先是一个down-project层将高维度特征映射到低维特征，然后过一个非线形层之后，<br>
再用一个up-project结构将低维特征映射回原来的高维特征；同时也设计了skip-connection结构，确保了在最<br>
差的情况下能够退化为identity），并将其嵌入Transformer的结构里面；<br>
• 在训练时，固定住原来预训练模型的参数不变，只对新增的Adapter结构进行微调。同时为了保证训练的高效<br>
性（也就是尽可能少的引入更多参数）。<br>
• 特点：<br>
• 通过在Transformer层中嵌入Adapter结构，在推理时会额外增加推理时长。<br>
• 思路：一种融合多任务信息的Adapter的变体，在 Adapter 的基础上进行优化，通过将学习过程分为两阶段来<br>
提升下游任务表现。<br>
• 思路：在不影响任务性能的情况下，对Adapter动态高效的移除，尽可能的减少模型的参数量，提高模型在反<br>
向传播（训练）和正向传播（推理）时的效率。<br>
• 特点：<br>
• 通过从较低的 Transformer 层删除可变数量的Adaper来提升推理速度；<br>
• 当对多个任务执行推理时，动态地减少了运行时的计算开销，并在很大程度上保持了任务性能。<br>
• 思路：一种在 Adapter、Prefix Tuning 和 LoRA 之间建立联系的统一方法。最终的模型 MAM Adapter 是用于 <br>
FFN 的并行 Adapter 和 软提示的组合。<br>
• 特点：<br>
• 整体上来说，最终的模型MAM Adapter效果会优于单个高效微调方法。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/27-适配器微调（Adapter-tuning）篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:14</p>
        </div>
    </div>
</body>
</html>