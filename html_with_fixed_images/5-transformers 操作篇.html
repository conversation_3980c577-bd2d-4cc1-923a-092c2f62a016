<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5-transformers 操作篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>5-transformers 操作篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/5-transformers 操作篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/5-transformers 操作篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>transformers 操作篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月28日 13:20<br>
1. 如何 利用 transformers 加载 Bert 模型？<br>
可以看到，包括import在内的不到十行代码，我们就实现了读取一个预训练过的BERT模型，来<br>
encode我们指定的一个文本，对文本的每一个token生成768维的向量。如果是二分类任务，我们<br>
• transformers 操作篇<br>
• 1. 如何 利用 transformers 加载 Bert 模型？<br>
• 2. 如何 利用 transformers 输出 Bert 指定 hidden_state？<br>
• 3. BERT 获取最后一层或每一层网络的向量输出<br>
• 致谢<br>
import torch<br>
from transformers import BertModel, BertTokenizer<br>
# 这里我们调用bert-base模型，同时模型的词典经过小写处理<br>
model_name = 'bert-base-uncased'<br>
# 读取模型对应的tokenizer<br>
tokenizer = BertTokenizer.from_pretrained(model_name)<br>
# 载入模型<br>
model = BertModel.from_pretrained(model_name)<br>
# 输入文本<br>
input_text = "Here is some text to encode"<br>
# 通过tokenizer把文本变成 token_id<br>
input_ids = tokenizer.encode(input_text, add_special_tokens=True)<br>
# input_ids: [101, 2182, 2003, 2070, 3793, 2000, 4372, 16044, 102]<br>
input_ids = torch.tensor([input_ids])<br>
# 获得BERT模型最后一个隐层结果<br>
with torch.no_grad():<br>
    last_hidden_states = model(input_ids)[0]  # Models outputs are now tuples<br>
""" tensor([[[-0.0549,  0.1053, -0.1065,  ..., -0.3550,  0.0686,  0.6506],<br>
         [-0.5759, -0.3650, -0.1383,  ..., -0.6782,  0.2092, -0.1639],<br>
         [-0.1641, -0.5597,  0.0150,  ..., -0.1603, -0.1346,  0.6216],<br>
         ...,<br>
         [ 0.2448,  0.1254,  0.1587,  ..., -0.2749, -0.1163,  0.8809],<br>
         [ 0.0481,  0.4950, -0.2827,  ..., -0.6097, -0.1212,  0.2527],<br>
         [ 0.9046,  0.2137, -0.5897,  ...,  0.3040, -0.6172, -0.1950]]]) <br>
shape: (1, 9, 768)     <br>
"""<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>接下来就可以把第一个token也就是[CLS]的768维向量，接一个linear层，预测出分类的logits，或<br>
者根据标签进行训练。<br>
2. 如何 利用 transformers 输出 Bert 指定 hidden_state？<br>
Bert 默认是 十二层，但是有时候预训练时并不需要利用全部利用，而只需要预训练前面几层即<br>
可，此时该怎么做呢？<br>
下载到bert-base-uncased的模型目录里面包含 配置文件 config.json, 该文件中包含 <br>
output_hidden_states，可以利用该参数来设置 编码器内隐藏层层数<br>
3. BERT 获取最后一层或每一层网络的向量输出<br>
{<br>
  "architectures": [<br>
    "BertForMaskedLM"<br>
  ],<br>
  "attention_probs_dropout_prob": 0.1,<br>
  "hidden_act": "gelu",<br>
  "hidden_dropout_prob": 0.1,<br>
  "hidden_size": 768,<br>
  "initializer_range": 0.02,<br>
  "intermediate_size": 3072,<br>
  "max_position_embeddings": 512,<br>
  "num_attention_heads": 12,<br>
  "num_hidden_layers": 12,<br>
  "type_vocab_size": 2,<br>
  "vocab_size": 30522<br>
}<br>
• transformer 最后一层输出的结果<br>
last_hidden_state：shape是(batch_size, sequence_length, hidden_size)，<br>
hidden_size=768,它是模型最后一层输出的隐藏状态<br>
pooler_output：shape是(batch_size, hidden_size)，这是序列的第一个<br>
token(classification token)的最后一层的隐藏状态，它是由线性层和Tanh激活函数进一步处<br>
理的，这个输出不是对输入的语义内容的一个很好的总结，对于整个输入序列的隐藏状态序列<br>
的平均化或池化通常更好。<br>
hidden_states：这是输出的一个可选项，如果输出，需要指定<br>
config.output_hidden_states=True,它也是一个元组，它的第一个元素是embedding，其余元<br>
素是各层的输出，每个元素的形状是(batch_size, sequence_length, hidden_size)<br>
attentions：这也是输出的一个可选项，如果输出，需要指定<br>
config.output_attentions=True,它也是一个元组，它的元素是每一层的注意力权重，用于计<br>
算self-attention heads的加权平均值<br>
• 获取每一层网络的向量输出<br>
##最后一层的所有 token向量<br>
outputs.last_hidden_state<br>
## cls向量<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/5-transformers 操作篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
outputs.pooler_output<br>
## hidden_states，包括13层，第一层即索引0是输入embedding向量，后面1-12索引是每层的<br>
输出向量<br>
hidden_states = outputs.hidden_states<br>
embedding_output = hidden_states[0]<br>
attention_hidden_states = hidden_states[1:]<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:13</p>
        </div>
    </div>
</body>
</html>