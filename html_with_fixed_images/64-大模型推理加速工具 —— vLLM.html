<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>64-大模型推理加速工具 —— vLLM</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>64-大模型推理加速工具 —— vLLM</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/64-大模型推理加速工具 —— vLLM_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/64-大模型推理加速工具 —— vLLM_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型推理加速工具 —— vLLM<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:56<br>
vLLM 官网 https://vllm.ai/<br>
vLLM 官方 Documentation: https://vllm.readthedocs.io/en/latest/getting_started/installation.html<br>
Source Code: https://github.com/vllm-project/vllm<br>
一、引言<br>
1.1 前言<br>
随着大语言模型（LLM）的不断发展，这些模型在很大程度上改变了人类使用 AI 的方式。然而，实际上为这些<br>
模型提供服务仍然存在挑战，即使在昂贵的硬件上也可能慢得惊人。<br>
现在这种限制正在被打破。最近，来自加州大学伯克利分校的研究者开源了一个项目 vLLM，该项目主要用于快<br>
速 LLM 推理和服务。vLLM 的核心是 PagedAttention，这是一种新颖的注意力算法，它将在操作系统的虚拟内<br>
存中分页的经典思想引入到 LLM 服务中。<br>
配备了 PagedAttention 的 vLLM 将 LLM 服务状态重新定义：它比 HuggingFace Transformers 提供高达 24 倍的<br>
吞吐量，而无需任何模型架构更改。<br>
1.2 为什么 需要 vLLM ?<br>
简之，vLLM是一个开源的LLM推理和服务引擎。它利用了全新的注意力算法「PagedAttention」，有效地管理注<br>
意力键和值。<br>
配备全新算法的vLLM，重新定义了LLM服务的最新技术水平：<br>
与HuggingFace Transformers相比，它提供高达24倍的吞吐量，而无需进行任何模型架构更改。<br>
值得一提的是，「小羊驼」Vicuna在demo中用到的就是FastChat和vLLM的一个集成。<br>
正如研究者所称，vLLM最大的优势在于——提供易用、快速、便宜的LLM服务。<br>
这意味着，未来，即使对于像LMSYS这样计算资源有限的小型研究团队也能轻松部署自己的LLM服务。<br>
1.3 vLLM 具有哪些特点 ?<br>
1.4 vLLM 支持哪些 Huggingface 模型 ?<br>
• 最先进的服务吞吐量；<br>
• PagedAttention 可以有效的管理注意力的键和值；<br>
• 动态批处理请求；<br>
• 优化好的 CUDA 内核；<br>
• 与流行的 HuggingFace 模型无缝集成；<br>
• 高吞吐量服务与各种解码算法，包括并行采样、beam search 等等；<br>
• 张量并行以支持分布式推理；<br>
• 流输出；<br>
• 兼容 OpenAI 的 API 服务。<br>
• GPT-2 (gpt2、gpt2-xl 等)；<br>
• GPTNeoX (EleutherAI/gpt-neox-20b、databricks/dolly-v2-12b、stabilityai/stablelm-tuned-alpha-7b 等)；<br>
• LLaMA (lmsys/vicuna-13b-v1.3、young-geng/koala、openlm-research/open_llama_13b 等)<br>
• OPT (facebook/opt-66b、facebook/opt-iml-max-30b 等)。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/64-大模型推理加速工具 —— vLLM_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/64-大模型推理加速工具 —— vLLM_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/64-大模型推理加速工具 —— vLLM_page2_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>二、vLLM 性能如何？<br>
该研究将 vLLM 的吞吐量与最流行的 LLM 库 HuggingFace Transformers （HF），以及之前具有 SOTA 吞吐量<br>
的 HuggingFace Text Generation Inference（TGI）进行了比较。此外，该研究将实验设置分为两种：LLaMA-<br>
7B，硬件为 NVIDIA A10G GPU；另一种为 LLaMA-13B，硬件为 NVIDIA A100 GPU (40GB)。他们从 <br>
ShareGPT 数据集中采样输入 / 输出长度。结果表明，vLLM 的吞吐量比 HF 高 24 倍，比 TGI 高 3.5 倍。<br>
vLLM 的吞吐量比 HF 高 14 倍 - 24 倍，比 TGI 高 2.2 倍 - 2.5 倍。<br>
vLLM 的吞吐量比 HF 高 8.5 - 15 倍，比 TGI 高 3.3 - 3.5 倍。<br>
三、vLLM 依赖包<br>
• OS: Linux<br>
</p>

<h2>第 3 页</h2>

<p>四、vLLM 安装<br>
4.1 构建环境<br>
4.2 vLLM 安装<br>
4.2.1 使用 pip 安装 vLLM<br>
通过 利用 pip 安装 vllm<br>
4.2.2 使用 source 安装 vLLM<br>
通过 从 github 上面 clone vllm，并 安装<br>
五、vLLM 使用<br>
5.1 vLLM 离线推理<br>
在使用 vLLM 进行离线推理任务时，你需要导入 vLLM 并在 Python 脚本中使用 LLM 类。<br>
注：目前 LLMs 并没有支持 所有 LLMs，具体可以查看 supported-models<br>
• Python: 3.8 or higher<br>
• CUDA: 11.0 – 11.8<br>
• GPU: compute capability 7.0 or higher (e.g., V100, T4, RTX20xx, A100, L4, etc.)<br>
    $ conda create -n py310_chat python=3.10       # 创建新环境<br>
    $ source activate py310_chat                   # 激活环境<br>
    $ pip install vllm<br>
    $ git clone https://github.com/vllm-project/vllm.git<br>
    $ cd vllm<br>
    $ pip install -e .  # This may take 5-10 minutes.<br>
# 导包<br>
from vllm import LLM, SamplingParams<br>
# 定义 输入 prompt<br>
prompts = [<br>
    "Hello, my name is",<br>
    "The president of the United States is",<br>
    "The capital of France is",<br>
    "The future of AI is",<br>
]<br>
# 采样温度设置为0.8，原子核采样概率设置为0.95。<br>
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)<br>
# 初始化 vLLM engine <br>
llm = LLM(model="facebook/opt-125m")<br>
# 使用 llm.generate 生成结果<br>
outputs = llm.generate(prompts, sampling_params)<br>
# Print the outputs. 它将输入提示添加到vLLM引擎的等待队列中，并执行vLLM发动机以生成<br>
具有高吞吐量的输出。输出作为RequestOutput对象的列表返回，其中包括所有输出标记。<br>
for output in outputs:<br>
    prompt = output.prompt<br>
    generated_text = output.outputs[0].text<br>
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="images/64-大模型推理加速工具 —— vLLM_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>5.2 vLLM 在线推理<br>
vLLM可以作为LLM服务进行部署。我们提供了一个FastAPI服务器示例。检查服务器实现的<br>
vllm/entrypoints/api_server.py。服务器使用AsyncLLMEngine类来支持异步处理传入请求。<br>
启动 服务<br>
默认情况下，此命令在启动服务器http://localhost:8000OPT-125M型号。<br>
调用服务<br>
5.3 OpenAI-Compatible Server<br>
vLLM可以部署为模仿OpenAI API协议的服务器。这允许vLLM被用作使用OpenAI API的应用程序的插入式替<br>
换。<br>
启动 服务<br>
默认情况下，它在启动服务器http://localhost:8000.<br>
可以使用--host和--port参数指定地址。<br>
    $ python -m vllm.entrypoints.openai.api_server --model lmsys/vicuna-7b-v1.3<br>
curl http://localhost:8000/generate \<br>
    -d '{<br>
        "prompt": "San Francisco is a",<br>
        "use_beam_search": true,<br>
        "n": 4,<br>
        "temperature": 0<br>
    }'<br>
    python -m vllm.entrypoints.openai.api_server \<br>
        --model facebook/opt-125m<br>
</p>

<h2>第 5 页</h2>

<p>服务器当前一次承载一个模型（上面命令中的OPT-125M），并实现列表模型和创建完成端点。我们正在积极添<br>
加对更多端点的支持。<br>
此服务器可以使用与 OpenAI API 相同的格式进行查询。例如，列出 models:<br>
Query the model with input prompts:<br>
由于此服务器与OpenAI API兼容，因此可以将其用作任何使用OpenAI API的应用程序的临时替代品。例如，查<br>
询服务器的另一种方式是通过openai python包：<br>
六、vLLM 分布式推理与服务<br>
vLLM支持分布式张量并行推理和服务。目前，支持 Megatron-LM’s tensor parallel algorithm。使用Ray管理分布<br>
式运行时。要运行分布式推理，请使用以下软件安装Ray：<br>
要使用LLM类运行 multi-GPU 推理，请将 tensor_parallel_size 参数设置为要使用的 GPU 数量。例如，要在4个<br>
GPU上运行推理：<br>
要运行多GPU服务，请在启动服务器时传入--tensor并行大小参数。例如，要在4个GPU上运行API服务器：<br>
要将vLLM扩展到单机之外，请在运行vLLM之前通过CLI启动Ray运行时：<br>
    $ curl http://localhost:8000/v1/models<br>
curl http://localhost:8000/v1/completions \<br>
    -H "Content-Type: application/json" \<br>
    -d '{<br>
        "model": "facebook/opt-125m",<br>
        "prompt": "San Francisco is a",<br>
        "max_tokens": 7,<br>
        "temperature": 0<br>
    }'<br>
import openai<br>
# Modify OpenAI's API key and API base to use vLLM's API server.<br>
openai.api_key = "EMPTY"<br>
openai.api_base = "http://localhost:8000/v1"<br>
completion = openai.Completion.create(model="facebook/opt-125m",<br>
                                      prompt="San Francisco is a")<br>
print("Completion result:", completion)<br>
    $ pip install ray<br>
    from vllm import LLM<br>
    llm = LLM("facebook/opt-13b", tensor_parallel_size=4)<br>
    output = llm.generate("San Franciso is a")<br>
    python -m vllm.entrypoints.api_server \<br>
        --model facebook/opt-13b \<br>
        --tensor-parallel-size 4<br>
    # On head node<br>
    ray start --head<br>
    # On worker nodes<br>
    ray start --address=&lt;ray-head-address&gt;<br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="images/64-大模型推理加速工具 —— vLLM_page6_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>之后，可以在多台机器上运行推理和服务，方法是在head节点上启动vLLM进程，将tensor_paralle_size设置为<br>
GPU数量，即所有机器上的GPU总数。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:13</p>
        </div>
    </div>
</body>
</html>