<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>15-大模型 RAG 经验面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>15-大模型 RAG 经验面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型 RAG 经验面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月28日 10:12<br>
一、LLMs 已经具备了较强能力了，存在哪些不足点?<br>
在 LLM 已经具备了较强能力的基础上，仍然存在以下问题：<br>
二、什么是 RAG?<br>
• RAG（Retrieval-Augmented Generation）面<br>
• 一、LLMs 已经具备了较强能力了，存在哪些不足点?<br>
• 二、什么是 RAG?<br>
• 2.1 R：检索器模块<br>
• 2.1.1 如何获得准确的语义表示？<br>
• 2.1.2 如何协调查询和文档的语义空间？<br>
• 2.1.3 如何对齐检索模型的输出和大语言模型的偏好？<br>
• 2.2 G：生成器模块<br>
• 2.2.1 生成器介绍<br>
• 2.2.2 如何通过后检索处理提升检索结果？<br>
• 2.2.3 如何优化生成器应对输入数据？<br>
• 三、使用 RAG 的好处?<br>
• 四、RAG V.S. SFT<br>
• 五、介绍一下 RAG 典型实现方法？<br>
• 5.1 如何 构建 数据索引？<br>
• 5.2 如何 对数据进行 检索（Retrieval）？<br>
• 5.3 对于 检索到的文本，如果生成正确回复？<br>
• 六、介绍一下 RAG 典型案例？<br>
• 6.1 ChatPDF 及其 复刻版<br>
• 6.2 Baichuan<br>
• 6.3 Multi-modal retrieval-based LMs<br>
• 七、RAG 存在什么问题？<br>
• 致谢<br>
• 幻觉问题：LLM 文本生成的底层原理是基于概率的 token by token 的形式，因此会不可避免地<br>
产生“一本正经的胡说八道”的情况；<br>
• 时效性问题：LLM 的规模越大，大模型训练的成本越高，周期也就越长。那么具有时效性的数<br>
据也就无法参与训练，所以也就无法直接回答时效性相关的问题，例如“帮我推荐几部热映的电<br>
影？”；<br>
• 数据安全问题：通用的 LLM 没有企业内部数据和用户数据，那么企业想要在保证安全的前提下<br>
使用 LLM，最好的方式就是把数据全部放在本地，企业数据的业务计算全部在本地完成。而在<br>
线的大模型仅仅完成一个归纳的功能；<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>RAG（Retrieval Augmented Generation, 检索增强生成），即 LLM 在回答问题或生成文本时，先<br>
会从大量文档中检索出相关的信息，然后基于这些信息生成回答或文本，从而提高预测质量。<br>
RAG for LLMs<br>
2.1 R：检索器模块<br>
在 RAG技术中，“R”代表检索，其作用是从大量知识库中检索出最相关的前 k 个文档。然而，构建<br>
一个高质量的检索器是一项挑战。研究探讨了三个关键问题：<br>
2.1.1 如何获得准确的语义表示？<br>
在 RAG 中，语义空间指的是查询和文档被映射的多维空间。以下是两种构建准确语义空间的方<br>
法。<br>
处理外部文档的第一步是分块，以获得更细致的特征。接着，这些文档块被嵌入。<br>
选择分块策略时，需要考虑被索引内容的特点、使用的嵌入模型及其最适块大小、用户查询的预期<br>
长度和复杂度、以及检索结果在特定应用中的使用方式。实际上，准确的查询结果是通过灵活应用<br>
多种分块策略来实现的，并没有最佳策略，只有最适合的策略。<br>
在确定了 Chunk 的适当大小之后，我们需要通过一个嵌入模型将 Chunk 和查询嵌入到语义空间<br>
中。如今，一些出色的嵌入模型已经问世，例如 UAE、Voyage、BGE等，它们在大规模语料库上<br>
预训练过<br>
2.1.2 如何协调查询和文档的语义空间？<br>
在 RAG 应用中，有些检索器用同一个嵌入模型来处理查询和文档，而有些则使用两个不同的模<br>
型。此外，用户的原始查询可能表达不清晰或缺少必要的语义信息。因此，协调用户的查询与文档<br>
的语义空间显得尤为重要。研究介绍了两种关键技术：<br>
一种直接的方式是对查询进行重写。<br>
可以利用大语言模型的能力生成一个指导性的伪文档，然后将原始查询与这个伪文档结合，形成一<br>
个新的查询。<br>
也可以通过文本标识符来建立查询向量，利用这些标识符生成一个相关但可能并不存在的“假想”文<br>
档，它的目的是捕捉到相关的模式。<br>
1. 块优化<br>
1. 微调嵌入模型<br>
1. 查询重写<br>
</p>

<h2>第 3 页</h2>

<p>此外，多查询检索方法让大语言模型能够同时产生多个搜索查询。这些查询可以同时运行，它们的<br>
结果一起被处理，特别适用于那些需要多个小问题共同解决的复杂问题。<br>
在 Liu 于 2023 年提出的 LlamaIndex 中，研究者们通过在查询编码器后加入一个特殊的适配器，<br>
并对其进行微调，从而优化查询的嵌入表示，使之更适合特定的任务。<br>
Li 团队在 2023 年提出的 SANTA 方法，就是为了让检索系统能够理解并处理结构化的信息。他们<br>
提出了两种预训练方法：一是利用结构化与非结构化数据之间的自然对应关系进行对比学习；二是<br>
采用了一种围绕实体设计的掩码策略，让语言模型来预测和填补这些被掩盖的实体信息。<br>
2.1.3 如何对齐检索模型的输出和大语言模型的偏好？<br>
在 RAG流水线中，即使采用了上述技术来提高检索模型的命中率，仍可能无法改善 RAG 的最终<br>
效果，因为检索到的文档可能不符合大语言模型的需求。<br>
因此，研究介绍了如下方法：<br>
大语言模型的监督训练：REPLUG使用检索模型和大语言模型计算检索到的文档的概率分布，然后<br>
通过计算 KL 散度进行监督训练。<br>
这种简单而有效的训练方法利用大语言模型作为监督信号，提高了检索模型的性能，消除了特定的<br>
交叉注意力机制的需求。<br>
此外，也有一些方法选择在检索模型上外部附加适配器来实现对齐，这是因为微调嵌入模型可能面<br>
临一些挑战，比如使用 API 实现嵌入功能或计算资源不足等。因此，一些方法选择在检索模型上<br>
外部附加适配器来实现对齐。<br>
除此之外，PKG通过指令微调将知识注入到白盒模型中，并直接替换检索模块，用于根据查询直接<br>
输出相关文档。<br>
2.2 G：生成器模块<br>
2.2.1 生成器介绍<br>
正是因为输入数据的多样性，我们针对生成阶段进行了一系列的有针对性工作，以便更好地适应来<br>
自查询和文档的输入数据。<br>
2.2.2 如何通过后检索处理提升检索结果？<br>
2.2.3 如何优化生成器应对输入数据？<br>
1. 嵌入变换<br>
• 介绍：在 RAG 系统中，生成组件是核心部分之一<br>
• 作用：将检索到的信息转化为自然流畅的文本。在 RAG 中，生成组件的输入不仅包括传统的<br>
上下文信息，还有通过检索器得到的相关文本片段。这使得生成组件能够更深入地理解问题背<br>
后的上下文，并产生更加信息丰富的回答。此外，生成组件还会根据检索到的文本来指导内容<br>
的生成，确保生成的内容与检索到的信息保持一致。<br>
• 介绍：后检索处理指的是，在通过检索器从大型文档数据库中检索到相关信息后，对这些信息<br>
进行进一步的处理、过滤或优化。<br>
• 主要目的：提高检索结果的质量，更好地满足用户需求或为后续任务做准备。<br>
• 后检索处理策略：包括信息压缩和结果的重新排序。<br>
• 生成器工作：负责将检索到的信息转化为相关文本，形成模型的最终输出。<br>
• 其优化目的：在于确保生成文本既流畅又能有效利用检索文档，更好地回应用户的查询。<br>
</p>

<h2>第 4 页</h2>

<p>RAG 的输入不仅包括查询，还涵盖了检索器找到的多种文档（无论是结构化还是非结构化）。一<br>
般在将输入提供给微调过的模型之前，需要对检索器找到的文档进行后续处理。<br>
值得注意的是，RAG 中对生成器的微调方式与大语言模型的普通微调方法大体相同，包括有通用<br>
优化过程以及运用对比学习等。<br>
三、使用 RAG 的好处?<br>
RAG 方法使得开发者不必为每一个特定的任务重新训练整个大模型，只需要外挂上知识库，即可<br>
为模型提供额外的信息输入，提高其回答的准确性。RAG模型尤其适合知识密集型的任务。<br>
四、RAG V.S. SFT<br>
实际上，对于 LLM 存在的上述问题，SFT 是一个最常见最基本的解决办法，也是 LLM 实现应用<br>
的基础步骤。那么有必要在多个维度上比较一下两种方法：<br>
• 可扩展性 (Scalability)：减少模型大小和训练成本，并允许轻松扩展知识<br>
• 准确性 (Accuracy)：通过引用信息来源，用户可以核实答案的准确性，这增强了人们对模型输<br>
出结果的信任。<br>
• 可控性 (Controllability)：允许更新或定制知识<br>
• 可解释性 (Interpretability)：检索到的项目作为模型预测中来源的参考<br>
• 多功能性 (Versatility)：RAG 可以针对多种任务进行微调和定制，包括QA、文本摘要、对话系<br>
统等；<br>
• 及时性：使用检索技术能识别到最新的信息，这使 RAG 在保持回答的及时性和准确性方面，<br>
相较于只依赖训练数据的传统语言模型有明显优势。<br>
• 定制性：通过索引与特定领域相关的文本语料库，RAG 能够为不同领域提供专业的知识支持。<br>
• 安全性：RAG 通过数据库中设置的角色和安全控制，实现了对数据使用的更好控制。相比之<br>
下，经过微调的模型在管理数据访问权限方面可能不够明确。<br>
</p>

<h2>第 5 页</h2>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page5_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page5_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>当然这两种方法并非非此即彼的，合理且必要的方式是结合业务需要与两种方法的优点，合理使用<br>
两种方法。<br>
五、介绍一下 RAG 典型实现方法？<br>
RAG 的实现主要包括三个主要步骤：数据索引、检索和生成。<br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page6_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>5.1 如何 构建 数据索引？<br>
数据索引一般是一个离线的过程，主要是将私域数据向量化后构建索引并存入数据库的过程。主要<br>
包括：数据提取、文本分割、向量化（embedding）及创建索引等环节。<br>
即从原始数据到便于处理的格式化数据的过程，具体工程包括：<br>
1. step 1：数据提取<br>
• 数据获取：包括多格式数据（eg：PDF、word、markdown以及数据库和API等）加载、不同数<br>
据源获取等，根据数据自身情况，将数据处理为同一个范式；<br>
• Doc类文档：直接解析其实就能得到文本到底是什么元素，比如标题、表格、段落等<br>
等。这部分直接将文本段及其对应的属性存储下来，用于后续切分的依据；<br>
• PDF类文档：<br>
• 难点：如何完整恢复图片、表格、标题、段落等内容，形成一个文字版的文档。<br>
• 解决方法：使用了多个开源模型进行协同分析，例如版面分析使用了百度的PP-<br>
StructureV2，能够对Text、Title、Figure、Figure caption、Table、Table <br>
caption、Header、Footer、Reference、Equation10类区域进行检测，统一了<br>
OCR和文本属性分类两个任务；<br>
• PPT类文档：<br>
• 难点：如何对PPT中大量的流程图，架构图进行提取，因为这些图多以形状元素<br>
在PPT中呈现，如果光提取文字，大量潜藏的信息就完全丢失了。<br>
• 解决方法：将PPT转换成PDF形式，然后用上述处理PDF的方式来进行解析。<br>
• 数据清洗：对源数据进行去重、过滤、压缩和格式化等处理；<br>
• 信息提取：提提取数据中关键信息，包括文件名、时间、章节title、图片等信息。<br>
1. Step 2: 文本分割（Chunking）<br>
• 动机：<br>
• 由于文本可能较长，或者仅有部分内容相关的情况下，需要对文本进行分块切分<br>
• 主要考虑两个因素：<br>
• embedding模型的Tokens限制情况；<br>
• 语义完整性对整体的检索效果的影响；<br>
• 分块的方式有：<br>
• 句分割：以”句”的粒度进行切分，保留一个句子的完整语义。常见切分符包括：句号、<br>
感叹号、问号、换行符等；<br>
• 固定大小的分块方式：根据embedding模型的token长度限制，将文本分割为固定长度<br>
（例如256/512个tokens），这种切分方式会损失很多语义信息，一般通过在头尾增加一<br>
定冗余量来缓解。<br>
• 基于意图的分块方式：<br>
• 句分割：最简单的是通过句号和换行来做切分，常用的意图包有基于NLP的NLTK<br>
和spaCy；<br>
• 递归分割：通过分而治之的思想，用递归切分到最小单元的一种方式；<br>
</p>

<h2>第 7 页</h2>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page7_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>5.2 如何 对数据进行 检索（Retrieval）？<br>
• 特殊分割：用于特殊场景。<br>
• 常用的工具：<br>
• langchain.text_splitter 库中的类CharacterTextSplitter：可以指定分隔符、块大小、重叠<br>
和长度函数来拆分文本。<br>
1. Step 3: 向量化（embedding）及创建索引<br>
• 向量化（embedding）<br>
• 思路：将文本、图像、音频和视频等转化为向量矩阵的过程，也就是变成计算机可以理<br>
解的格式。<br>
• 常见的embedding模型：<br>
• ChatGPT-Embedding<br>
• ERNIE-Embedding V1<br>
• M3E<br>
• BGE<br>
• 创建索引：<br>
• 思路：数据向量化后构建索引，并写入数据库的过程可以概述为数据入库过程。<br>
• 常用的工具：FAISS、Chromadb、ES、milvus等;<br>
• 注：一般可以根据业务场景、硬件、性能需求等多因素综合考虑，选择合适的数据库。<br>
• 动机：检索环节是获取有效信息的关键环节<br>
• 思路：<br>
• 元数据过滤：当我们把索引分成许多chunks的时候，检索效率会成为问题。这时候，如<br>
果可以通过元数据先进行过滤，就会大大提升效率和相关度。<br>
• 图关系检索：即引入知识图谱，将实体变成node，把它们之间的关系变成relation，就可<br>
以利用知识之间的关系做更准确的回答。特别是针对一些多跳问题，利用图数据索引会<br>
让检索的相关度变得更高；<br>
• 检索技术：检索的主要方式还是这几种：<br>
• 向量化（embedding）相似度检索：相似度计算方式包括欧氏距离、曼哈顿距<br>
离、余弦等；<br>
• 关键词检索：这是很传统的检索方式，元数据过滤也是一种，还有一种就是先把<br>
chunk做摘要，再通过关键词检索找到可能相关的chunk，增加检索效率；<br>
• 全文检索：<br>
• SQL检索：更加传统的检索算法。<br>
</p>

<h2>第 8 页</h2>

<p>5.3 对于 检索到的文本，如果生成正确回复？<br>
文本生成就是将原始 query 和检索得到的文本组合起来输入模型得到结果的过程，本质上就是个 <br>
prompt engineer ing 过程。<br>
此外还有全流程的框架，如 Langchain 和 LlamaIndex ，都非常简单易用，如：<br>
六、介绍一下 RAG 典型案例？<br>
6.1 ChatPDF 及其 复刻版<br>
参考：https://www.chatpdf.com/<br>
ChatPDF的实现流程如下：<br>
• 重排序（Rerank）：相关度、匹配度等因素做一些重新调整，得到更符合业务场景的排<br>
序。<br>
• 查询轮换：这是查询检索的一种方式，一般会有几种方式：<br>
• 子查询：可以在不同的场景中使用各种查询策略，比如可以使用LlamaIndex等框<br>
架提供的查询器，采用树查询（从叶子结点，一步步查询，合并），采用向量查<br>
询，或者最原始的顺序查询chunks等；<br>
• HyDE：这是一种抄作业的方式，生成相似的或者更标准的 prompt 模板。<br>
from langchain.chat_models import ChatOpenAI<br>
from langchain.schema.runnable import RunnablePassthrough<br>
llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0)<br>
rag_chain = {"context": retriever, "question": RunnablePassthrough()} | rag_prompt <br>
| llm<br>
rag_chain.invoke("What is Task Decomposition?")<br>
1. ChatPDF首先读取PDF文件，将其转换为可处理的文本格式，例如txt格式；<br>
2. ChatPDF会对提取出来的文本进行清理和标准化，例如去除特殊字符、分段、分句等，以便于<br>
后续处理。这一步可以使用自然语言处理技术，如正则表达式等；<br>
3. ChatPDF使用OpenAI的Embeddings API将每个分段转换为向量，这个向量将对文本中的语义<br>
进行编码，以便于与问题的向量进行比较；<br>
4. 当用户提出问题时，ChatPDF使用OpenAI的Embeddings API将问题转换为一个向量，并与每<br>
个分段的向量进行比较，以找到最相似的分段。这个相似度计算可以使用余弦相似度等常见的<br>
方法进行；<br>
5. ChatPDF将找到的最相似的分段与问题作为prompt，调用OpenAI的Completion API，让<br>
ChatGPT学习分段内容后，再回答对应的问题；<br>
6. ChatPDF会将ChatGPT生成的答案返回给用户，完成一次查询。<br>
</p>

<h2>第 9 页</h2>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page9_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page9_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>6.2 Baichuan<br>
参考：https://www.baichuan-ai.com/home<br>
百川大模型的搜索增强系统融合了多个模块，包括:<br>
通过这一系列协同作用，大模型实现了更精确、智能的模型结果回答，通过这种方式减少了模型的<br>
幻觉。<br>
6.3 Multi-modal retrieval-based LMs<br>
• 指令意图理解:深入理解用户指令<br>
• 智能搜索:精确驱动查询词的搜索<br>
• 结果增强:结合大语言模型技术来优化模型结果生成的可靠性。<br>
</p>

<h2>第 10 页</h2>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page10_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/15-大模型 RAG 经验面_page10_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>参考：https://cs.stanford.edu/~myasu/blog/racm3/<br>
RA-CM3 是一个检索增强的多模态模型，其包含了一个信息检索框架来从外部存储库中获取知识，<br>
具体来说，作者首先使用预训练的 CLIP 模型来实现一个检索器（retriever），然后使用 CM3 <br>
Transformer 架构来构成一个生成器（generator），其中检索器用来辅助模型从外部存储库中搜索<br>
有关于当前提示文本中的精确信息，然后将该信息连同文本送入到生成器中进行图像合成，这样设<br>
计的模型的准确性就会大大提高。<br>
七、RAG 存在什么问题？<br>
知识星球<br>
• 检索效果依赖 embedding 和检索算法。目前可能检索到无关信息，反而对输出有负面影响；<br>
• 大模型如何利用检索到的信息仍是黑盒的。可能仍存在不准确（甚至生成的文本与检索信息相<br>
冲突）；<br>
• 对所有任务都无差别检索 k 个文本片段，效率不高，同时会大大增加模型输入的长度；<br>
• 无法引用来源，也因此无法精准地查证事实，检索的真实性取决于数据源及检索算法。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:10</p>
        </div>
    </div>
</body>
</html>