<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>42-大模型（LLMs）显存问题面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>42-大模型（LLMs）显存问题面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/42-大模型（LLMs）显存问题面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/42-大模型（LLMs）显存问题面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）显存问题面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月16日 21:00<br>
1. 大模型大概有多大，模型文件有多大?<br>
一般放出来的模型文件都是fp16的，假设是一个 n B的模型，那么模型文件占 2n G，fp16加载到显存里做推理也<br>
是占 2n G，对外的pr都是 10n 亿参数的模型。<br>
2. 能否用4 * v100 32G训练vicuna 65b？<br>
不能。<br>
（刚发现fastchat上可以通过调用train脚本训练vicuna而非train_mem，其实也是可以训练的）<br>
3. 如果就是想要试试65b模型，但是显存不多怎么办？<br>
最少大概50g显存，可以在llama-65b-int4（gptq）模型基础上LoRA[6]，当然各种库要安装定制版本的。<br>
4. nB模型推理需要多少显存？<br>
考虑模型参数都是fp16，2nG的显存能把模型加载。<br>
5. nB模型训练需要多少显存？<br>
基础显存：模型参数+梯度+优化器，总共16nG。<br>
activation占用显存，和max len、batch size有关<br>
解释：优化器部分必须用fp32（似乎fp16会导致训练不稳定），所以应该是2+2+12=16，参考ZeRO论文。<br>
注以上算数不够直观，举个例子？<br>
7B的vicuna在fsdp下总共160G显存勉强可以训练。（按照上面计算7*16=112G是基础显存）<br>
所以全量训练准备显存20nG大概是最低要求，除非内存充足，显存不够offload内存补。<br>
6. 如何 估算模型所需的RAM？<br>
首先，我们需要了解如何根据参数量估计模型大致所需的 RAM，这在实践中有很重要的参考意义。我们需要通<br>
过估算设置 batch_size，设置模型精度，选择微调方法和参数分布方法等。<br>
接下来，我们用LLaMA-6B 模型为例估算其大致需要的内存。<br>
首先考虑精度对所需内存的影响：<br>
其次，考虑模型需要的 RAM 大致分三个部分：<br>
• 首先，llama 65b的权重需要5* v100 32G才能完整加载到GPU。<br>
• 其次，vicuna使用flash-attention加速训练，暂不支持v100，需要turing架构之后的显卡。<br>
• fp32 精度，一个参数需要 32 bits, 4 bytes.<br>
• fp16 精度，一个参数需要 16 bits, 2 bytes.<br>
• int8 精度，一个参数需要 8 bits, 1 byte.<br>
• 模型参数<br>
• 梯度<br>
• 优化器参数<br>
• 模型参数：等于参数量*每个参数所需内存。<br>
• 对于 fp32，LLaMA-6B 需要 6B*4 bytes = 24GB内存<br>
• 对于 int8，LLaMA-6B 需要 6B*1 byte = 6GB<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>对于常用的 AdamW 来说，需要储存两倍的模型参数（用来储存一阶和二阶momentum）。<br>
除此之外，CUDA kernel 也会占据一些 RAM，大概 1.3GB 左右，查看方式如下。<br>
综上，int8 精度的 LLaMA-6B 模型部分大致需要 6GB+6GB+12GB+1.3GB = 25.3GB 左右。<br>
再根据LLaMA的架构（hidden_size = 4096, intermediate_size =11008, num_hidden_layers = 32, <br>
context_length = 2048）计算中间变量内存。<br>
每个 instance 需要：<br>
所以一张 A100（80GB RAM）大概可以在 int8 精度；batch_size = 50 的设定下进行全参数训练。<br>
查看消费级显卡的内存和算力：<br>
2023 GPU Benchmark and Graphics Card Comparison Chart<br>
https://www.gpucheck.com/gpu-benchmark-graphics-card-comparison-chart<br>
7. 如何评估你的显卡利用率<br>
zero3如果没有nvlink，多卡训练下会变慢。但是一直不知道究竟会变得多慢，下面给出几种方法来评估自己在训<br>
练时发挥了多少gpu性能，以及具体测试方法。<br>
7.1 flops比值法<br>
举例：deepspeed实测flops 100tflops，而用的是A100卡理论峰值312tflops，可以得到GPU利用率只有 32.05%<br>
7.2 throughout估计法<br>
举例：<br>
实测训练时处理样本速度为 3 example/s，一共有4卡，max length 2048，则吞吐量为 1536 token/s/gpu<br>
根据llama论文知道，他们训练7B模型的吞吐量约为 3300 token/s/gpu，那么GPU利用率只有46.54%<br>
7.3 torch profiler分析法<br>
利用torch profiler记录各个函数的时间，将结果在tensorboard上展示，在gpu kenel视图下，可以看到tensor core<br>
的利用率，比如30%<br>
• 梯度：同上，等于参数量*每个梯度参数所需内存。<br>
• 优化器参数：不同的优化器所储存的参数量不同。<br>
• fp32 的 LLaMA-6B，AdamW 需要 6B*8 bytes = 48 GB<br>
• int8 的 LLaMA-6B，AdamW 需要 6B*2 bytes = 12 GB<br>
    &gt; torch.ones((1，1)).to("cuda")<br>
    &gt; print_gpu_utilization()<br>
    &gt;&gt;&gt;<br>
    GPU memory occupied: 1343 MB<br>
    (4096 +11008)* 2048 *32 * 1byte = 990MB<br>
• 测试工具：deepspeed<br>
• 参考数据：nvidia公布的显卡fp16峰值计算速度（tensor core）<br>
    gpu利用率 = 实测的flops/显卡理论上的峰值flops<br>
• 测试工具：手动估算 或者 deepspeed<br>
• 参考数据：论文中的训练速度或者吞吐量<br>
    吞吐量 = example数量/秒/GPU * max_length<br>
    gpu利用率 = 实际吞吐量 / 论文中的吞吐量（假设利用率100%）<br>
• 测试工具：torch profiler 及 tensorboard<br>
• 参考数据：无<br>
</p>

<h2>第 3 页</h2>

<p>总结<br>
以上三种方法，在笔者的实验中能得到差不多的利用率指标。<br>
从准确性上看，方案三 &gt; 方案一 &gt; 方案二<br>
从易用性上看，方案二 &gt; 方案一 &gt; 方案三<br>
如果不想改代码就用方案二估算自己的训练速度是不是合理的，如果想精确分析训练速度的瓶颈还是建议使用方<br>
案三。<br>
8. 测试你的显卡利用率 实现细节篇<br>
8.1 如何查看多机训练时的网速？<br>
iftop命令，看网速很方便。<br>
8.2 如何查看服务器上的多卡之间的NVLINK topo？<br>
8.3 如何查看服务器上显卡的具体型号?<br>
8.4 如何查看训练时的flops？（也就是每秒的计算量）<br>
理论上，如果flops比较低，说明没有发挥出显卡的性能。<br>
如果基于deepspeed训练，可以通过配置文件很方便的测试<br>
参考：https://www.deepspeed.ai/tutorials/flops-profiler/<br>
8.5 如何查看对deepspeed的环境配置是否正确？<br>
8.6 tf32格式有多长？<br>
19位<br>
1. 大模型大概有多大，模型文件有多大?<br>
一般放出来的模型文件都是fp16的，假设是一个 n B的模型，那么模型文件占 2n G，fp16加载到显<br>
存里做推理也是占 2n G，对外的pr都是 10n 亿参数的模型。<br>
    $ nvidia-smi topo -m<br>
    cd /usr/local/cuda/samples/1_Utilities/deviceQuery<br>
    make<br>
    ./deviceQuery<br>
{<br>
  "flops_profiler": {<br>
    "enabled": true,<br>
    "profile_step": 1,<br>
    "module_depth": -1,<br>
    "top_modules": 1,<br>
    "detailed": true,<br>
    "output_file": null<br>
    }<br>
}<br>
    $ ds_report<br>
</p>

<h2>第 4 页</h2>

<p>2. 能否用4 * v100 32G训练vicuna 65b？<br>
不能。<br>
（刚发现fastchat上可以通过调用train脚本训练vicuna而非train_mem，其实也是可以训练的）<br>
3. 如果就是想要试试65b模型，但是显存不多怎么办？<br>
最少大概50g显存，可以在llama-65b-int4（gptq）模型基础上LoRA[6]，当然各种库要安装定制版<br>
本的。<br>
4. nB模型推理需要多少显存？<br>
考虑模型参数都是fp16，2nG的显存能把模型加载。<br>
5. nB模型训练需要多少显存？<br>
基础显存：模型参数+梯度+优化器，总共16nG。<br>
activation占用显存，和max len、batch size有关<br>
解释：优化器部分必须用fp32（似乎fp16会导致训练不稳定），所以应该是2+2+12=16，参考<br>
ZeRO论文。<br>
注以上算数不够直观，举个例子？<br>
7B的vicuna在fsdp下总共160G显存勉强可以训练。（按照上面计算7*16=112G是基础显存）<br>
所以全量训练准备显存20nG大概是最低要求，除非内存充足，显存不够offload内存补。<br>
6. 如何 估算模型所需的RAM？<br>
首先，我们需要了解如何根据参数量估计模型大致所需的 RAM，这在实践中有很重要的参考意<br>
义。我们需要通过估算设置 batch_size，设置模型精度，选择微调方法和参数分布方法等。<br>
接下来，我们用LLaMA-6B 模型为例估算其大致需要的内存。<br>
首先考虑精度对所需内存的影响：<br>
其次，考虑模型需要的 RAM 大致分三个部分：<br>
• 首先，llama 65b的权重需要5* v100 32G才能完整加载到GPU。<br>
• 其次，vicuna使用flash-attention加速训练，暂不支持v100，需要turing架构之后的显卡。<br>
• fp32 精度，一个参数需要 32 bits, 4 bytes.<br>
• fp16 精度，一个参数需要 16 bits, 2 bytes.<br>
• int8 精度，一个参数需要 8 bits, 1 byte.<br>
• 模型参数<br>
• 梯度<br>
• 优化器参数<br>
• 模型参数：等于参数量*每个参数所需内存。<br>
• 对于 fp32，LLaMA-6B 需要 6B*4 bytes = 24GB内存<br>
</p>

<h2>第 5 页</h2>

<p>对于常用的 AdamW 来说，需要储存两倍的模型参数（用来储存一阶和二阶momentum）。<br>
除此之外，CUDA kernel 也会占据一些 RAM，大概 1.3GB 左右，查看方式如下。<br>
综上，int8 精度的 LLaMA-6B 模型部分大致需要 6GB+6GB+12GB+1.3GB = 25.3GB 左右。<br>
再根据LLaMA的架构（hidden_size = 4096, intermediate_size =11008, num_hidden_layers = 32, <br>
context_length = 2048）计算中间变量内存。<br>
每个 instance 需要：<br>
所以一张 A100（80GB RAM）大概可以在 int8 精度；batch_size = 50 的设定下进行全参数训<br>
练。<br>
查看消费级显卡的内存和算力：<br>
2023 GPU Benchmark and Graphics Card Comparison Chart<br>
https://www.gpucheck.com/gpu-benchmark-graphics-card-comparison-chart<br>
7. 如何评估你的显卡利用率<br>
zero3如果没有nvlink，多卡训练下会变慢。但是一直不知道究竟会变得多慢，下面给出几种方法来<br>
评估自己在训练时发挥了多少gpu性能，以及具体测试方法。<br>
7.1 flops比值法<br>
举例：deepspeed实测flops 100tflops，而用的是A100卡理论峰值312tflops，可以得到GPU利用率<br>
只有 32.05%<br>
7.2 throughout估计法<br>
• 对于 int8，LLaMA-6B 需要 6B*1 byte = 6GB<br>
• 梯度：同上，等于参数量*每个梯度参数所需内存。<br>
• 优化器参数：不同的优化器所储存的参数量不同。<br>
• fp32 的 LLaMA-6B，AdamW 需要 6B*8 bytes = 48 GB<br>
• int8 的 LLaMA-6B，AdamW 需要 6B*2 bytes = 12 GB<br>
    &gt; torch.ones((1，1)).to("cuda")<br>
    &gt; print_gpu_utilization()<br>
    &gt;&gt;&gt;<br>
    GPU memory occupied: 1343 MB<br>
    (4096 +11008)* 2048 *32 * 1byte = 990MB<br>
• 测试工具：deepspeed<br>
• 参考数据：nvidia公布的显卡fp16峰值计算速度（tensor core）<br>
    gpu利用率 = 实测的flops/显卡理论上的峰值flops<br>
• 测试工具：手动估算 或者 deepspeed<br>
• 参考数据：论文中的训练速度或者吞吐量<br>
</p>

<h2>第 6 页</h2>

<p>举例：<br>
实测训练时处理样本速度为 3 example/s，一共有4卡，max length 2048，则吞吐量为 1536 <br>
token/s/gpu<br>
根据llama论文知道，他们训练7B模型的吞吐量约为 3300 token/s/gpu，那么GPU利用率只有<br>
46.54%<br>
7.3 torch profiler分析法<br>
利用torch profiler记录各个函数的时间，将结果在tensorboard上展示，在gpu kenel视图下，可以<br>
看到tensor core的利用率，比如30%<br>
总结<br>
以上三种方法，在笔者的实验中能得到差不多的利用率指标。<br>
从准确性上看，方案三 &gt; 方案一 &gt; 方案二<br>
从易用性上看，方案二 &gt; 方案一 &gt; 方案三<br>
如果不想改代码就用方案二估算自己的训练速度是不是合理的，如果想精确分析训练速度的瓶颈还<br>
是建议使用方案三。<br>
8. 测试你的显卡利用率 实现细节篇<br>
8.1 如何查看多机训练时的网速？<br>
iftop命令，看网速很方便。<br>
8.2 如何查看服务器上的多卡之间的NVLINK topo？<br>
8.3 如何查看服务器上显卡的具体型号?<br>
    吞吐量 = example数量/秒/GPU * max_length<br>
    gpu利用率 = 实际吞吐量 / 论文中的吞吐量（假设利用率100%）<br>
• 测试工具：torch profiler 及 tensorboard<br>
• 参考数据：无<br>
    $ nvidia-smi topo -m<br>
</p>

<h2>第 7 页</h2>

<p>8.4 如何查看训练时的flops？（也就是每秒的计算量）<br>
理论上，如果flops比较低，说明没有发挥出显卡的性能。<br>
如果基于deepspeed训练，可以通过配置文件很方便的测试<br>
参考：https://www.deepspeed.ai/tutorials/flops-profiler/<br>
8.5 如何查看对deepspeed的环境配置是否正确？<br>
8.6 tf32格式有多长？<br>
19位<br>
    cd /usr/local/cuda/samples/1_Utilities/deviceQuery<br>
    make<br>
    ./deviceQuery<br>
{<br>
  "flops_profiler": {<br>
    "enabled": true,<br>
    "profile_step": 1,<br>
    "module_depth": -1,<br>
    "top_modules": 1,<br>
    "detailed": true,<br>
    "output_file": null<br>
    }<br>
}<br>
    $ ds_report<br>
</p>

<h2>第 8 页</h2>

<div class="image-container">
  <img src="images/42-大模型（LLMs）显存问题面_page8_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/42-大模型（LLMs）显存问题面_page8_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>8.7 哪里看各类显卡算力比较？<br>
https://lambdalabs.com/gpu-benchmarks<br>
8.8 （torch profiler）如何查看自己的训练中通信开销？<br>
用pytorch profiler查看，下面给出基于transformers的一种快捷的修改方式。<br>
https://github.com/yqhu/profiler-<br>
workshop/blob/c8d4a7c30a61cc7b909d89f88f5fd36b70c55769/hf_training_trainer_prof.py<br>
用记录的pt.trace.json文件放到tensorboard上，可以看出tensor core的利用率。<br>
根据实践经验，使用deepspeed zero3时，pcie版本的卡很大部分时间都在通信上，AllGather和<br>
ReduceScatter的时间超过tensor core计算的时间，所以flops上不去。<br>
8.7 哪里看各类显卡算力比较？<br>
https://lambdalabs.com/gpu-benchmarks<br>
8.8 （torch profiler）如何查看自己的训练中通信开销？<br>
用pytorch profiler查看，下面给出基于transformers的一种快捷的修改方式。<br>
https://github.com/yqhu/profiler-<br>
workshop/blob/c8d4a7c30a61cc7b909d89f88f5fd36b70c55769/hf_training_trainer_prof.py<br>
用记录的pt.trace.json文件放到tensorboard上，可以看出tensor core的利用率。<br>
根据实践经验，使用deepspeed zero3时，pcie版本的卡很大部分时间都在通信上，AllGather和ReduceScatter<br>
的时间超过tensor core计算的时间，所以flops上不去。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:10</p>
        </div>
    </div>
</body>
</html>