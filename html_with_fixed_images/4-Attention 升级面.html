<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4-Attention 升级面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>4-Attention 升级面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/4-Attention 升级面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/4-Attention 升级面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/4-Attention 升级面_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>Attention 升级面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 13:26<br>
1 传统 Attention 存在哪些问题？<br>
2 Attention 优化方向<br>
3 Attention 变体有哪些？<br>
4 Multi-Query Attention 篇<br>
4.1 Multi-head Attention 存在什么问题？<br>
4.2 介绍一下 Multi-Query Attention？<br>
Multi-Query Attention 在所有注意力头上 共享 key 和 value.<br>
4.3 对比一下 Multi-head Attention 和 Multi-Query Attention？<br>
1. 传统 Attention 存在 上下文长度 约束问题；<br>
2. 传统 Attention 速度慢，内存占用大；<br>
1. 提升上下文长度<br>
2. 加速、减少内存占用<br>
• 稀疏 attention。将稀疏偏差引入 attention 机制可以降低了复杂性；<br>
• 线性化 attention。解开 attention 矩阵与内核特征图，然后以相反的顺序计算 attention 以实现线性复杂度；<br>
• 原型和内存压缩。这类方法减少了查询或键值记忆对的数量，以减少注意力矩阵的大小；<br>
• 低阶 self-Attention。这一系列工作捕获了 self-Attention 的低阶属性；<br>
• Attention 与先验。该研究探索了用先验 attention 分布来补充或替代标准 attention；<br>
• 改进多头机制。该系列研究探索了不同的替代多头机制。<br>
• 训练过程：不会显著影响训练过程，训练速度不变，会引起非常细微的模型效果损失；<br>
• 推理过程：反复加载 巨大 的 KV cache , 导致 内存开销大，性能是内存受限；<br>
• Multi-head Attention：每个注意力头都有各自的query、key和value。<br>
• Multi-query Attention: 在所有的注意力头上共享key和value。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/4-Attention 升级面_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/4-Attention 升级面_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>Falcon、PaLM、ChatGLM2-6B都使用了Multi-query Attention，但有细微差别。<br>
4.4 Multi-Query Attention 这样做的好处是什么？<br>
减少 KV cache 的大小，减少显存占用，提升推理速度。<br>
4.5 有 哪些模型 是 使用 Multi-Query Attention？<br>
5 Grouped-query Attention<br>
5.1 什么是 Grouped-query Attention？<br>
Grouped query attention: 介于multi head和multi query之间，多个key和value。<br>
5.2 有哪些大模型使用 Grouped-query Attention？<br>
ChatGLM2，LLaMA2-34B/70B使用了Grouped query attention。<br>
6 FlashAttention<br>
7 并行 transformer block<br>
用并行公式替换了串行，提升了15%的训练速度。<br>
在8B参数量规模，会有轻微的模型效果损失;在62B参数量规模，就不会损失模型效果。<br>
Falcon、PaLM都使用了该技术来加速训练<br>
• 为了保持参数量一致，<br>
• Falcon: 把隐藏维度从4096增大到了4544。多余的参数量分给了Attention块和FFN块<br>
• ChatGLM2: 把FFN中间维度从11008增大到了13696。多余的参数分给了FFN块<br>
• 代表模型：PaLM、ChatGLM2、Falcon等<br>
• 核心：用分块softmax等价替代传统softmax<br>
• 优点：节约HBM，高效利用SRAM，省显存，提速度<br>
• 代表模型：Meta推出的开源大模型LLaMA，阿联酋推出的开源大模型Falcon都使用了Flash Attention来加速<br>
计算和节省显存<br>
• 关键词：HBM、SRAM、分块Softmax、重计算、Kernel融合。<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/4-Attention 升级面_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:11</p>
        </div>
    </div>
</body>
</html>