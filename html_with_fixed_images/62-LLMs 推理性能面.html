<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>62-LLMs 推理性能面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>62-LLMs 推理性能面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/62-LLMs 推理性能面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/62-LLMs 推理性能面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>LLMs 推理性能面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:44<br>
一、介绍一下 LLMs 的文本生成过程？<br>
LLMs 的文本生成过程 分为：<br>
二、如何准确衡量模型的推理速度呢？<br>
这些指标 如何评估好坏了，可以记住这句话：<br>
模型能够尽可能快地为尽可能多的用户生成文本<br>
注：需要权衡吞吐量和每个输出词元的时间：与依次运行查询相比，如果我们同时处理16个用户查询，吞吐量会<br>
更高，但会花费更长的时间为每个用户生成输出词元。<br>
三、如果对整体推理时延有具体目标，有哪些有效的启发式方法来评估模型？<br>
• LLMs 推理性能面<br>
• 一、介绍一下 LLMs 的文本生成过程？<br>
• 二、如何准确衡量模型的推理速度呢？<br>
• 三、如果对整体推理时延有具体目标，有哪些有效的启发式方法来评估模型？<br>
• 四、LLMs 推理存在哪些挑战？<br>
• 致谢<br>
1. **【预填充（prefill）】**阶段：以并行方式处理输入提示中的词元；<br>
2. 【解码（decoding）】阶段：文本会以自回归的方式逐个生成“词元”。每个生成的词元都会被添加到输入<br>
中，并被重新喂入模型，以生成下一个词元。当LLM输出了特殊的停止词元或满足用户定义的条件（例如生<br>
成了最大数量的词元）时，生成过程就会停止<br>
1. 首个词元生成时间（Time To First Token，简称TTFT）：即用户输入查询后，模型生成第一个输出所需的<br>
时间。在实时交互中，低时延获取响应非常重要，但在离线工作负载中则不太重要。此指标受处理提示信息<br>
并生成首个输出词元所需的时间所驱动；<br>
2. 单个输出词元的生成时间（Time Per Output Token，简称TPOT）：为每个查询系统的用户生成一个输出<br>
词元所需的时间。这一指标与每个用户对模型“速度”的感知相关。例如，TPOT为100毫秒/词元表示每个用户<br>
每秒可处理10个词元，或每分钟处理约450个词，这一速度远超普通人的阅读速度；<br>
3. 时延：模型为用户生成完整响应所需的总时间。整体响应时延可使用前两个指标计算得出：时延 = <br>
（TTFT）+ （TPOT）*（待生成的词元数）；<br>
4. 吞吐量：推理服务器在所有用户和请求中每秒可生成的输出词元数。<br>
• 目标：以最短的时间生成首个词元、达到最高吞吐量以及在最短的时间内生成输出词元。<br>
• 输出长度决定了整体响应时延：对于平均时延，通常只需将预期/最大的输出词元长度与模型的每个输出词元<br>
的整体平均时间相乘；<br>
• 输入长度对性能来说影响不大，但对硬件要求至关重要：在MPT模型中，添加512个输入词元增加的时延要<br>
少于生成8个额外输出词元的时延。然而，支持长输入的需求可能使模型难以部署。例如，建议使用A100-<br>
80GB（或更新版本）来为最大上下文长度为2048个词元来部署MPT-7B模型；<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/62-LLMs 推理性能面_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
• 整体时延与模型大小呈次线性关系：在相同的硬件上，较大的模型速度较慢，但速度比不一定与参数数量比<br>
相匹配。MPT-30B的时延约为MPT-7B时延的2.5倍，LLaMA2-70B的时延约为LLaMA2-13B时延的2倍。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:10</p>
        </div>
    </div>
</body>
</html>