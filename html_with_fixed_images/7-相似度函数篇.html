<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>7-相似度函数篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>7-相似度函数篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/7-相似度函数篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/7-相似度函数篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/7-相似度函数篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>相似度函数篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月12日 06:35<br>
一、除了cosin还有哪些算相似度的方法<br>
除了余弦相似度（cosine similarity）之外，常见的相似度计算方法还包括欧氏距离、曼哈顿距离、<br>
Jaccard相似度、皮尔逊相关系数等。<br>
二、了解对比学习嘛？<br>
对比学习是一种无监督学习方法，通过训练模型使得相同样本的表示更接近，不同样本的表示更远<br>
离，从而学习到更好的表示。对比学习通常使用对比损失函数，例如Siamese网络、Triplet网络<br>
等，用于学习数据之间的相似性和差异性。<br>
三、对比学习负样本是否重要？负样本构造成本过高应该怎么解<br>
决？<br>
对比学习中负样本的重要性取决于具体的任务和数据。负样本可以帮助模型学习到样本之间的区分<br>
度，从而提高模型的性能和泛化能力。然而，负样本的构造成本可能会较高，特别是在一些领域和<br>
任务中。<br>
为了解决负样本构造成本过高的问题，可以考虑以下方法：<br>
知识星球<br>
• 相似度函数篇<br>
• 一、除了cosin还有哪些算相似度的方法<br>
• 二、了解对比学习嘛？<br>
• 三、对比学习负样本是否重要？负样本构造成本过高应该怎么解决？<br>
• 降低负样本的构造成本：通过设计更高效的负样本生成算法或采样策略，减少负样本的构造成<br>
本。例如，可以利用数据增强技术生成合成的负样本，或者使用近似采样方法选择与正样本相<br>
似但不相同的负样本。<br>
• 确定关键负样本：根据具体任务的特点，可以重点关注一些关键的负样本，而不是对所有负样<br>
本进行详细的构造。这样可以降低构造成本，同时仍然能够有效训练模型。<br>
• 迁移学习和预训练模型：利用预训练模型或迁移学习的方法，可以在其他领域或任务中利用已<br>
有的负样本构造成果，减少重复的负样本构造工作。<br>
扫码加<br>
查看更多<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:14</p>
        </div>
    </div>
</body>
</html>