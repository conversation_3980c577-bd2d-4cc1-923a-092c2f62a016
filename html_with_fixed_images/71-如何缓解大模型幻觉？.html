<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>71-如何缓解大模型幻觉？</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>71-如何缓解大模型幻觉？</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/71-如何缓解大模型幻觉？_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/71-如何缓解大模型幻觉？_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>如何缓解大模型幻觉？<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 13:36<br>
一、为什么 会 出现 大模型幻觉？<br>
1.1 训练数据中存在 矛盾或者错误的表述 对LLMs训练影响<br>
大模型幻觉出现的主要原因之一 是 训练数据中存在 矛盾或者错误的表述问题。由于 用于 LLMs 训练的 标注数<br>
据 大多来自于互联网网上数据（新闻、文章、书籍、网站等）。虽然这些数据一定程度上提供了有价值的语言模<br>
式，但它也不可避免会包含一些不准确的信息（因为互联网上的信息并不是都经过审核的）。<br>
LLM的训练过程大多数是基于next token prediction的方式进行预训练，因此，它只能保证文本生成的流畅性，<br>
而无法辨别所遇到的信息是否真实或准确。<br>
因此，如果训练数据中包含一些矛盾或者错误的表述，就可能导致LLM也在学习这些错误的表达，从而一定程度<br>
导致了幻觉的产生。<br>
1.2 训练数据中存在 偏见表述 对LLMs训练影响<br>
大模型幻觉出现的主要原因之一 是 训练数据中存在 偏见表述 对LLMs训练影响。<br>
训练数据中可能存在社会偏见、文化信仰和个人观点之类相关的语料。这些偏见可能也会被LLM学会，从而导致<br>
LLM生成的文本会包含一些错误或有偏见的信息。<br>
1.3 LLMs 学习到 知识 缺乏外部验证<br>
大模型幻觉出现的主要原因之一 是 LLMs 学习到 知识 缺乏外部验证。<br>
虽然大模型拥有在训练过程中学习并获得大量内部知识的能力，但是 由于 们缺乏实时访问最新信息或根据外部<br>
参考验证事实的能力，使得 他们 无法辨别 产生的信息是准确的还是虚构的。<br>
此外，生成过程中缺乏事实核查机制使得LLM可以生成听起来合理但缺乏任何实质性证据或事实依据的文本。这<br>
也告诉我们不能太相信LLM。<br>
LLM幻觉的原因在于训练数据、文本中存在的偏见以及LLM的固有局限性。通过解决这些基本因素，我们可以努<br>
力提高LLM的准确性和可靠性，尽量保证它们生成的信息不仅连贯，而且是准确的。<br>
二、如何 缓解 大模型幻觉？<br>
要克服LLM幻觉需要从多个方面一起考虑，比如从训练层面和用户层面，以下是一些常见的策略：<br>
2.1 方法一：提高训练数据质量<br>
提高训练数据的质量对于减小LLM幻觉至关重要。 比如可以考虑剔除那些不准确、有偏见的数据，并纳入多样化<br>
和可靠（比如经过事实审查）的数据来源。<br>
用于LLM训练的数据量越大（广度的数量都要考虑），最终训练得到的LLM出现幻觉的可能性就可能越小。<br>
2.2 方法二：使用合适的训练算法<br>
LLM如何训练也关乎后续在推理时是否会产生幻觉，因此可以考虑在训练阶段融入一些有助于生成与事实一致的<br>
文本的策略。<br>
2.3 方法三：事实核查和验证机制<br>
训练好的LLM在推理阶段仍然可以进行一些事实审查或者验证，比如通过在生成过程进行一些判断或者交叉引用<br>
从而保证准确性。<br>
2.4 方法四：外部知识集成<br>
使LLM能够访问和利用外部知识来源可以显著提高它们生成准确和可靠信息的能力。将结构化数据、知识图谱或<br>
垂直领域的知识库集成到训练/推理过程中可以增强模型对事实信息的理解，并提高其生成可靠文本的能力。<br>
2.5 方法五：Human-in-the-Loop<br>
通过在训练和测试阶段融入一些人类的反馈，从而纠正和完善模型的输出。训练阶段比如RLHF就是很好的例<br>
子，推理阶段同样也可以考虑一些交互式的推理方式将人类反馈融入其中。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/71-如何缓解大模型幻觉？_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>2.6 方法六：偏见缓解<br>
一方面要在数据上做一些预处理，另一方面也要定期评估和监控模型输出的偏见，同时还可以通过一些手段减少<br>
推理过程出现的偏见。<br>
2.7 方法七：用户教育和批判性思维<br>
LLM的幻觉可以缓解，但是难以根除。因此用户也应该有批判性思维，不能依赖LLM的输出，可以通过交叉引用<br>
信息，多个来源信息综合考虑。<br>
三、总结<br>
总的来说，缓解LLM幻觉需要从多个维度进行努力。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:11</p>
        </div>
    </div>
</body>
</html>