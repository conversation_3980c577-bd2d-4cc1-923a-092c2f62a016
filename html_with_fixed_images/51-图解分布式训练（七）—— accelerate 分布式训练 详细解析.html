<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>51-图解分布式训练（七）—— accelerate 分布式训练 详细解析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>51-图解分布式训练（七）—— accelerate 分布式训练 详细解析</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/51-图解分布式训练（七）—— accelerate 分布式训练 详细解析_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/51-图解分布式训练（七）—— accelerate 分布式训练 详细解析_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图解分布式训练（七）—— accelerate 分布式训练 详细解析<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 11:58<br>
一、为什么需要 accelerate 分布式训练？<br>
PyTorch Accelerate 是一个 PyTorch 的加速工具包，旨在简化 PyTorch 训练和推断的开发过程，并提高性能。它<br>
是由 Hugging Face、NVIDIA、AWS 和 Microsoft 等公司联合开发的，是一个开源项目。<br>
二、什么是 accelerate 分布式训练?<br>
2.1 accelerate 分布式训练 介绍<br>
PyTorch Accelerate 提供了一组简单易用的 API，帮助开发者实现模型的分布式训练、混合精度训练、自动调<br>
参、数据加载优化和模型优化等功能。它还集成了 PyTorch Lightning 和 TorchElastic，使用户能够轻松地实现高<br>
性能和高可扩展性的模型训练和推断。<br>
2.2 accelerate 分布式训练 主要优势<br>
PyTorch Accelerate 的主要优势包括：<br>
三、accelerate 分布式训练 原理讲解？<br>
3.1 分布式训练<br>
分布式训练是指将一个大型深度学习模型拆分成多个小模型，在不同的计算机上并行训练，最后将结果合并，得<br>
到最终的模型。分布式训练可以显著减少模型训练的时间，因为它充分利用了多个计算机的计算资源。同时，由<br>
于每个小模型只需要处理部分数据，因此可以使用更大的批次大小，进一步提高训练速度。<br>
3.2 加速策略<br>
Accelerate提供了多种加速策略，如pipeline并行、数据并行等。<br>
3.2.1 Pipeline并行<br>
Pipeline并行是指将模型拆分成多个部分，在不同的计算机上并行训练。在每个计算机上，只需要处理模型的一<br>
部分，然后将结果传递给下一个计算机。这样可以充分利用多个计算机的计算资源，并且可以使用更大的批次大<br>
小，提高训练速度。Pipeline并行的缺点是，由于每个计算机只处理部分数据，因此每个计算机的结果都会有一<br>
些误差，最终的结果可能会有一些偏差。<br>
3.2.2 数据并行<br>
数据并行是指将数据拆分成多个部分，在不同的计算机上并行训练。在每个计算机上，都会处理全部的模型，但<br>
是每个计算机只处理部分数据。这样可以充分利用多个计算机的计算资源，并且可以使用更大的批次大小，提高<br>
训练速度。数据并行的优点是，每个计算机都会处理全部的模型，因此结果更加准确。缺点是，由于每个计算机<br>
都需要完整的模型，因此需要更多的计算资源。<br>
3.2.3 加速器<br>
加速器是指用于加速深度学习模型训练的硬件设备，如GPU、TPU等。加速器可以大幅提高模型的训练速度，因<br>
为它们可以在更短的时间内完成更多的计算。Accelerate可以自动检测并利用可用的加速器，以进一步提高训练<br>
速度。<br>
四、accelerate 分布式训练 如何实践？<br>
4.1 accelerate 分布式训练 依赖安装<br>
• 分布式训练：可以在多个 GPU 或多台机器上并行训练模型，从而缩短训练时间和提高模型性能；<br>
• 混合精度训练：可以使用半精度浮点数加速模型训练，从而减少 GPU 内存使用和提高训练速度；<br>
• 自动调参：可以使用 PyTorch Lightning Trainer 来自动调整超参数，从而提高模型性能；<br>
• 数据加载优化：可以使用 DataLoader 和 DataLoaderTransforms 来优化数据加载速度，从而减少训练时<br>
间；<br>
• 模型优化：可以使用 Apex 或 TorchScript 等工具来优化模型性能。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>4.2 accelerate 分布式训练 代码实现逻辑<br>
    $ pip install accelerate==0.17.1<br>
1. 导包<br>
    ...<br>
    from accelerate import Accelerator<br>
    ...<br>
2. Trainer 训练类 编写<br>
class Trainer:<br>
    def __init__(self,<br>
                 args,<br>
                 config,<br>
                 model_engine,<br>
                 criterion,<br>
                 optimizer,<br>
                 accelerator):<br>
        ...<br>
        self.accelerator = accelerator<br>
        ...<br>
    def train(self, train_loader, dev_loader=None):<br>
        ...<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model_engine.train()<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                self.accelerator.backward(loss)<br>
                self.optimizer.step()<br>
                self.optimizer.zero_grad()<br>
        ...<br>
3. main() 函数 编写<br>
def main():<br>
    ...<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    ...<br>
    accelerator = Accelerator()<br>
    args.local_rank = int(dist.get_rank())<br>
    print(args.local_rank)<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
</p>

<h2>第 3 页</h2>

<p>4.3 accelerate 分布式训练 示例代码<br>
    # =======================================<br>
    # 定义训练器<br>
    trainer = Trainer(args,<br>
                      config,<br>
                      model_engine,<br>
                      criterion,<br>
                      optimizer_engine,<br>
                      accelerator)<br>
    # 训练和验证<br>
    trainer.train(train_loader_engine, dev_loader_engine)<br>
    # 测试<br>
    ...<br>
    # 需要重新初始化引擎<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
    model_engine.load_state_dict(torch.load(args.ckpt_path))<br>
    report = trainer.test(model_engine, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
import json<br>
import time<br>
import random<br>
import torch<br>
import deepspeed<br>
import torch.nn as nn<br>
import numpy as np<br>
import torch.distributed as dist<br>
from sklearn.metrics import classification_report<br>
from accelerate import Accelerator<br>
from torch.utils.data import DataLoader<br>
from collections import Counter<br>
from transformers import BertForMaskedLM, BertTokenizer, <br>
BertForSequenceClassification, BertConfig, AdamW<br>
def set_seed(seed=123):<br>
    """<br>
    设置随机数种子，保证实验可重现<br>
    :param seed:<br>
    :return:<br>
</p>

<h2>第 4 页</h2>

<p>    """<br>
    random.seed(seed)<br>
    torch.manual_seed(seed)<br>
    np.random.seed(seed)<br>
    torch.cuda.manual_seed_all(seed)<br>
def get_data():<br>
    with open("data/train.json", "r", encoding="utf-8") as fp:<br>
        data = fp.read()<br>
    data = json.loads(data)<br>
    return data<br>
def load_data():<br>
    data = get_data()<br>
    return_data = []<br>
    # [(文本， 标签id)]<br>
    for d in data:<br>
        text = d[0]<br>
        label = d[1]<br>
        return_data.append(("".join(text.split(" ")).strip(), label))<br>
    return return_data<br>
class Collate:<br>
    def __init__(self,<br>
                 tokenizer,<br>
                 max_seq_len,<br>
                 ):<br>
        self.tokenizer = tokenizer<br>
        self.max_seq_len = max_seq_len<br>
    def collate_fn(self, batch):<br>
        input_ids_all = []<br>
        token_type_ids_all = []<br>
        attention_mask_all = []<br>
        label_all = []<br>
        for data in batch:<br>
            text = data[0]<br>
            label = data[1]<br>
            inputs = self.tokenizer.encode_plus(text=text,<br>
                                                max_length=self.max_seq_len,<br>
                                                padding="max_length",<br>
                                                truncation="longest_first",<br>
                                                return_attention_mask=True,<br>
                                                return_token_type_ids=True)<br>
            input_ids = inputs["input_ids"]<br>
            token_type_ids = inputs["token_type_ids"]<br>
            attention_mask = inputs["attention_mask"]<br>
</p>

<h2>第 5 页</h2>

<p>            input_ids_all.append(input_ids)<br>
            token_type_ids_all.append(token_type_ids)<br>
            attention_mask_all.append(attention_mask)<br>
            label_all.append(label)<br>
        input_ids_all = torch.tensor(input_ids_all, dtype=torch.long)<br>
        token_type_ids_all = torch.tensor(token_type_ids_all, dtype=torch.long)<br>
        attention_mask_all = torch.tensor(attention_mask_all, dtype=torch.long)<br>
        label_all = torch.tensor(label_all, dtype=torch.long)<br>
        return_data = {<br>
            "input_ids": input_ids_all,<br>
            "attention_mask": attention_mask_all,<br>
            "token_type_ids": token_type_ids_all,<br>
            "label": label_all<br>
        }<br>
        return return_data<br>
class Trainer:<br>
    def __init__(self,<br>
                 args,<br>
                 config,<br>
                 model_engine,<br>
                 criterion,<br>
                 optimizer,<br>
                 accelerator):<br>
        self.args = args<br>
        self.config = config<br>
        self.model_engine = model_engine<br>
        self.criterion = criterion<br>
        self.optimizer = optimizer<br>
        self.accelerator = accelerator<br>
    def on_step(self, batch_data):<br>
        label = batch_data["label"].cuda()<br>
        input_ids = batch_data["input_ids"].cuda()<br>
        token_type_ids = batch_data["token_type_ids"].cuda()<br>
        attention_mask = batch_data["attention_mask"].cuda()<br>
        output = self.model_engine.forward(input_ids=input_ids,<br>
                                           token_type_ids=token_type_ids,<br>
                                           attention_mask=attention_mask,<br>
                                           labels=label)<br>
        logits = output[1]<br>
        return logits, label<br>
    def loss_reduce(self, loss):<br>
        rt = loss.clone()<br>
        dist.all_reduce(rt, op=dist.ReduceOp.SUM)<br>
</p>

<h2>第 6 页</h2>

<p>        rt /= torch.cuda.device_count()<br>
        return rt<br>
    def output_reduce(self, outputs, targets):<br>
        output_gather_list = [torch.zeros_like(outputs) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(output_gather_list, outputs)<br>
        outputs = torch.cat(output_gather_list, dim=0)<br>
        target_gather_list = [torch.zeros_like(targets) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(target_gather_list, targets)<br>
        targets = torch.cat(target_gather_list, dim=0)<br>
        return outputs, targets<br>
    def train(self, train_loader, dev_loader=None):<br>
        gloabl_step = 1<br>
        best_acc = 0.<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model_engine.train()<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                self.accelerator.backward(loss)<br>
                self.optimizer.step()<br>
                self.optimizer.zero_grad()<br>
                loss = self.loss_reduce(loss)<br>
                if self.args.local_rank == 0:<br>
                    print("【train】 epoch：{}/{} step：{}/{} loss：{:.6f}".format(<br>
                        epoch, self.args.epochs, gloabl_step, self.args.total_step, <br>
loss<br>
                    ))<br>
                gloabl_step += 1<br>
                if self.args.dev:<br>
                    if gloabl_step % self.args.eval_step == 0:<br>
                        loss, accuracy = self.dev(dev_loader)<br>
                        if self.args.local_rank == 0:<br>
                            print("【dev】 loss：{:.6f} accuracy：<br>
{:.4f}".format(loss, accuracy))<br>
                            if accuracy &gt; best_acc:<br>
                                best_acc = accuracy<br>
                                print("【best accuracy】 {:.4f}".format(best_acc))<br>
</p>

<h2>第 7 页</h2>

<p>                                torch.save(self.model_engine.state_dict(), <br>
self.args.ckpt_path)<br>
        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev and self.args.local_rank == 0:<br>
            torch.save(self.model_engine.state_dict(), self.args.ckpt_path)<br>
    def dev(self, dev_loader):<br>
        self.model_engine.eval()<br>
        correct_total = 0<br>
        num_total = 0<br>
        loss_total = 0.<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(dev_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                loss = self.loss_reduce(loss)<br>
                logits, label = self.output_reduce(logits, label)<br>
                loss_total += loss<br>
                logits = logits.detach().cpu().numpy()<br>
                label = label.view(-1).detach().cpu().numpy()<br>
                num_total += len(label)<br>
                preds = np.argmax(logits, axis=1).flatten()<br>
                correct_num = (preds == label).sum()<br>
                correct_total += correct_num<br>
        return loss_total, correct_total / num_total<br>
    def test(self, model_engine, test_loader, labels):<br>
        self.model_engine = model_engine<br>
        self.model_engine.eval()<br>
        preds = []<br>
        trues = []<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(test_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                logits, label = self.output_reduce(logits, label)<br>
                label = label.view(-1).detach().cpu().numpy().tolist()<br>
                logits = logits.detach().cpu().numpy()<br>
                pred = np.argmax(logits, axis=1).flatten().tolist()<br>
                trues.extend(label)<br>
                preds.extend(pred)<br>
        # print(trues, preds, labels)<br>
        print(np.array(trues).shape, np.array(preds).shape)<br>
        report = classification_report(trues, preds, target_names=labels)<br>
</p>

<h2>第 8 页</h2>

<p>        return report<br>
def build_optimizer(model, args):<br>
    no_decay = ['bias', 'LayerNorm.weight']<br>
    optimizer_grouped_parameters = [<br>
        {'params': [p for n, p in model.named_parameters() if not any(nd in n for <br>
nd in no_decay)],<br>
         'weight_decay': args.weight_decay},<br>
        {'params': [p for n, p in model.named_parameters() if any(nd in n for nd in <br>
no_decay)],<br>
         'weight_decay': 0.0}<br>
    ]<br>
    # optimizer = AdamW(model.parameters(), lr=learning_rate)<br>
    optimizer = AdamW(optimizer_grouped_parameters, lr=args.learning_rate)<br>
    return optimizer<br>
class Args:<br>
    model_path = "model_hub/chinese-bert-wwm-ext"<br>
    ckpt_path = "output/accelerate/multi-gpu-accelerate-cls.pt"<br>
    max_seq_len = 128<br>
    ratio = 0.92<br>
    epochs = 1<br>
    eval_step = 50<br>
    dev = False<br>
    local_rank = None<br>
    train_batch_size = 32<br>
    dev_batch_size = 32<br>
    weight_decay = 0.01<br>
    learning_rate=3e-5<br>
def main():<br>
    # =======================================<br>
    # 定义相关参数<br>
    set_seed()<br>
    label2id = {<br>
        "其他": 0,<br>
        "喜好": 1,<br>
        "悲伤": 2,<br>
        "厌恶": 3,<br>
        "愤怒": 4,<br>
        "高兴": 5,<br>
    }<br>
    args = Args()<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
    # =======================================<br>
</p>

<h2>第 9 页</h2>

<p>    # =======================================<br>
    # 加载数据集<br>
    data = load_data()<br>
    # 取1万条数据出来<br>
    data = data[:10000]<br>
    random.shuffle(data)<br>
    train_num = int(len(data) * args.ratio)<br>
    train_data = data[:train_num]<br>
    dev_data = data[train_num:]<br>
    collate = Collate(tokenizer, args.max_seq_len)<br>
    train_loader = DataLoader(train_data,<br>
                              batch_size=args.train_batch_size,<br>
                              shuffle=True,<br>
                              num_workers=2,<br>
                              collate_fn=collate.collate_fn)<br>
    total_step = len(train_loader) * args.epochs //  torch.cuda.device_count()<br>
    args.total_step = total_step<br>
    dev_loader = DataLoader(dev_data,<br>
                            batch_size=args.dev_batch_size,<br>
                            shuffle=False,<br>
                            num_workers=2,<br>
                            collate_fn=collate.collate_fn)<br>
    test_loader = dev_loader<br>
    # =======================================<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
    criterion = torch.nn.CrossEntropyLoss()<br>
    optimizer = build_optimizer(model, args)<br>
    accelerator = Accelerator()<br>
    args.local_rank = int(dist.get_rank())<br>
    print(args.local_rank)<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
    # =======================================<br>
    # 定义训练器<br>
    trainer = Trainer(args,<br>
                      config,<br>
</p>

<h2>第 10 页</h2>

<p>4.3 accelerate 分布式训练 运行<br>
运行效果<br>
GPU 使用情况<br>
                      model_engine,<br>
                      criterion,<br>
                      optimizer_engine,<br>
                      accelerator)<br>
    # 训练和验证<br>
    trainer.train(train_loader_engine, dev_loader_engine)<br>
    # 测试<br>
    labels = list(label2id.keys())<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
    # 需要重新初始化引擎<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
    model_engine.load_state_dict(torch.load(args.ckpt_path))<br>
    report = trainer.test(model_engine, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
if __name__ == '__main__':<br>
    main()<br>
• 方式一：<br>
    $ accelerate launch multi-gpu-accelerate-cls.py<br>
• 方式二：<br>
    $ python -m torch.distributed.launch --nproc_per_node 2 --use_env multi-gpu-<br>
accelerate-cls.py<br>
【train】 epoch：1/1 step：1/144 loss：1.795169<br>
【train】 epoch：1/1 step：2/144 loss：1.744665<br>
【train】 epoch：1/1 step：3/144 loss：1.631625<br>
【train】 epoch：1/1 step：4/144 loss：1.543691<br>
【train】 epoch：1/1 step：5/144 loss：1.788955<br>
</p>

<h2>第 11 页</h2>

<div class="image-container">
  <img src="images/51-图解分布式训练（七）—— accelerate 分布式训练 详细解析_page11_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/51-图解分布式训练（七）—— accelerate 分布式训练 详细解析_page11_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:34:13</p>
        </div>
    </div>
</body>
</html>