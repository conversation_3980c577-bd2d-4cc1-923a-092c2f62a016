@echo off
chcp 65001 >nul
echo ============================================================
echo PDF批量转换为HTML工具 - Windows批处理脚本
echo ============================================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "pdf_to_html_converter.py" (
    echo 错误: 未找到pdf_to_html_converter.py脚本文件
    pause
    exit /b 1
)

REM 安装依赖
echo 正在检查并安装依赖...
python -m pip install PyMuPDF tqdm --quiet

REM 获取用户输入
set /p input_dir="请输入PDF文件所在目录路径（直接回车使用当前目录）: "
if "%input_dir%"=="" set input_dir=.

set /p output_dir="请输入输出目录路径（直接回车在原目录生成）: "

set /p recursive="是否递归处理子目录？(y/n，默认y): "
if "%recursive%"=="" set recursive=y

REM 构建命令
set command=python pdf_to_html_converter.py "%input_dir%"

if not "%output_dir%"=="" (
    set command=%command% -o "%output_dir%"
)

if /i "%recursive%"=="n" (
    set command=%command% --no-recursive
)

echo.
echo 执行命令: %command%
echo.

REM 执行转换
%command%

echo.
echo 转换完成！按任意键退出...
pause >nul
