#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转HTML转换器测试脚本
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pdf_to_html_converter import PDFToHTMLConverter, ConversionResult


def test_converter():
    """测试转换器功能"""
    print("开始测试PDF转HTML转换器...")
    
    # 使用当前目录作为测试目录
    current_dir = Path.cwd()
    
    # 查找PDF文件
    pdf_files = list(current_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("当前目录没有找到PDF文件，无法进行测试")
        return False
    
    print(f"找到 {len(pdf_files)} 个PDF文件用于测试")
    
    # 创建临时输出目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建转换器实例
        converter = PDFToHTMLConverter(
            output_dir=temp_dir,
            recursive=False
        )
        
        try:
            # 测试单个文件转换
            test_file = pdf_files[0]
            print(f"\n测试单个文件转换: {test_file.name}")
            
            success = converter.convert_single_pdf(test_file, str(current_dir))
            
            if success:
                # 检查输出文件是否存在
                output_file = Path(temp_dir) / test_file.with_suffix('.html').name
                if output_file.exists():
                    print(f"✅ 单文件转换成功: {output_file}")
                    
                    # 检查文件内容
                    with open(output_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if len(content) > 100 and '<html' in content:
                            print("✅ HTML内容格式正确")
                        else:
                            print("❌ HTML内容可能有问题")
                else:
                    print("❌ 输出文件未生成")
                    return False
            else:
                print("❌ 单文件转换失败")
                return False
            
            # 测试批量转换（只转换前3个文件以节省时间）
            print(f"\n测试批量转换（前3个文件）...")
            
            # 创建新的转换器实例用于批量测试
            batch_converter = PDFToHTMLConverter(
                output_dir=temp_dir,
                recursive=False
            )
            
            # 临时移动其他文件，只保留前3个
            test_files = pdf_files[:3]
            
            # 创建临时测试目录
            test_dir = Path(temp_dir) / "test_input"
            test_dir.mkdir()
            
            # 复制测试文件
            for pdf_file in test_files:
                shutil.copy2(pdf_file, test_dir)
            
            # 执行批量转换
            result = batch_converter.convert_batch(str(test_dir))
            
            print(f"批量转换结果:")
            print(f"  总文件数: {result.total_files}")
            print(f"  成功转换: {result.successful}")
            print(f"  转换失败: {result.failed}")
            
            if result.successful > 0:
                print("✅ 批量转换测试通过")
                return True
            else:
                print("❌ 批量转换测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {str(e)}")
            return False


def test_command_line():
    """测试命令行功能"""
    print("\n测试命令行功能...")
    
    # 测试帮助信息
    print("测试帮助信息显示:")
    os.system(f"{sys.executable} pdf_to_html_converter.py -h")
    
    print("✅ 命令行帮助测试完成")


def main():
    """主测试函数"""
    print("=" * 60)
    print("PDF转HTML转换器 - 功能测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import fitz
        print("✅ PyMuPDF 库已安装")
    except ImportError:
        print("❌ PyMuPDF 库未安装，请运行: pip install PyMuPDF")
        return False
    
    try:
        import tqdm
        print("✅ tqdm 库已安装")
    except ImportError:
        print("⚠️  tqdm 库未安装，进度显示将使用简单模式")
    
    # 运行功能测试
    if test_converter():
        print("\n✅ 所有功能测试通过！")
        
        # 测试命令行
        test_command_line()
        
        print("\n" + "=" * 60)
        print("测试完成！转换器可以正常使用。")
        print("=" * 60)
        return True
    else:
        print("\n❌ 功能测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
