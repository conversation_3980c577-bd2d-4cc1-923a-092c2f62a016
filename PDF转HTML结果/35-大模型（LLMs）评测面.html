<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>35-大模型（LLMs）评测面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>35-大模型（LLMs）评测面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/35-大模型（LLMs）评测面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/35-大模型（LLMs）评测面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）评测面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 10:23<br>
1 大模型怎么评测？<br>
当前superGLUE, GLUE, 包括中文的CLUE 的benchmark都在不太合适评估大模型。可能评估推理能力、多轮对<br>
话能力是核心。<br>
2 大模型的honest原则是如何实现的？模型如何判断回答的知识是训练过的已知的知识，怎么训练这种能力？<br>
大模型需要遵循的helpful，honest， harmless的原则。<br>
可以有意构造如下的训练样本，以提升模型准守honest原则，可以算trick了：<br>
微调时构造知识问答类训练集，给出不知道的不回答，加强honest原则；<br>
阅读理解题，读过的要回答，没读过的不回答，不要胡说八道。<br>
3 如何衡量大模型水平？<br>
要评估一个大型语言模型的水平，可以从以下几个维度提出具有代表性的问题。<br>
4 大模型评估方法 有哪些？<br>
• 理解能力：提出一些需要深入理解文本的问题，看模型是否能准确回答。<br>
• 语言生成能力：让模型生成一段有关特定主题的文章或故事，评估其生成的文本在结构、逻辑和语法等方面<br>
的质量。<br>
• 知识面广度：请模型回答关于不同主题的问题，以测试其对不同领域的知识掌握程度。这可以是关于科学、<br>
历史、文学、体育或其他领域的问题。一个优秀的大语言模型应该可以回答各种领域的问题，并且准确性和<br>
深度都很高。<br>
• 适应性：让模型处理各种不同类型的任务，例如：写作、翻译、编程等，看它是否能灵活应对。<br>
• 长文本理解：提出一些需要处理长文本的问题，例如：提供一篇文章，让模型总结出文章的要点，或者请模<br>
型创作一个故事或一篇文章，让其有一个完整的情节，并且不要出现明显的逻辑矛盾或故事结构上的错误。<br>
一个好的大语言模型应该能够以一个连贯的方式讲述一个故事，让读者沉浸其中。<br>
• 长文本生成：请模型创作一个故事或一篇文章，让其有一个完整的情节，并且不要出现明显的逻辑矛盾或故<br>
事结构上的错误。一个好的大语言模型应该能够以一个连贯的方式讲述一个故事，让读者沉浸其中。<br>
• 多样性：提出一个问题，让模型给出多个不同的答案或解决方案，测试模型的创造力和多样性。<br>
• 情感分析和推断：提供一段对话或文本，让模型分析其中的情感和态度，或者推断角色间的关系。<br>
• 情感表达：请模型生成带有情感色彩的文本，如描述某个场景或事件的情感、描述一个人物的情感状态等。<br>
一个优秀的大语言模型应该能够准确地捕捉情感，将其表达出来。<br>
• 逻辑推理能力：请模型回答需要进行推理或逻辑分析的问题，如概率或逻辑推理等。这可以帮助判断模型对<br>
推理和逻辑思考的能力，以及其在处理逻辑问题方面的准确性。例如：“所有的动物都会呼吸。狗是一种动<br>
物。那么狗会呼吸吗？”<br>
• 问题解决能力：提出实际问题，例如：数学题、编程问题等，看模型是否能给出正确的解答。<br>
• 道德和伦理：测试模型在处理有关道德和伦理问题时的表现，例如：“在什么情况下撒谎是可以接受的？”<br>
• 对话和聊天：请模型进行对话，以测试其对自然语言处理的掌握程度和能力。一个优秀的大语言模型应该能<br>
够准确地回答问题，并且能够理解人类的语言表达方式。<br>
• 人工评估：LIMA、Phoenix<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/35-大模型（LLMs）评测面_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>因此，Chatbot Arena 的做法是放弃benchmark，通过对抗，实时聊天，两两比对人工进行打分，采用elo分数进<br>
行评测。<br>
5 大模型评估工具 有哪些？<br>
知识星球<br>
• 使用 GPT-4 的反馈进行自动评估：Vicuna、Phoenix、Chimera、BELLE指标评估（BLEU-4、ROUGE分<br>
数）：ChatGLM-6B；对于像ROUGE-L分数的指标评估，有些地方称其为非自然指令评估（Unnatural <br>
Instruction Evaluation）。<br>
• Chatbot Arena：目前用来衡量一个模型好不好的东西基本都是基于一些学术的benchmark，比如在一个某个<br>
NLP任务上构建一个测试数据集，然后看测试数据集上准确率多少。然而，这些学术benchmark（如<br>
HELM）在大模型和聊天机器人上就不好用了。其原因在于：<br>
• 由于评判聊天机器人聊得好不好这件事是非常主观的，因此，现有的方法很难对其进行衡量。<br>
• 这些大模型在训练的时候就几乎把整个互联网的数据都扫了一个遍，因此，很难保证测试用的数据集<br>
没有被看到过。甚至更进一步，用测试集直接对模型进行「特训」，如此一来表现必然更好。<br>
• 理论上我们可以和聊天机器人聊任何事情，但很多话题或者任务在现存的benchmark里面根本就不存<br>
在。<br>
• OpenAI evals：OpenAI的自动化评估脚本，核心思路就是通过写prompt模版来自动化评估。<br>
• PandaLM：其是直接训练了一个自动化打分模型，0,1,2三分制用模型对两个候选模型进行打分。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:47</p>
        </div>
    </div>
</body>
</html>