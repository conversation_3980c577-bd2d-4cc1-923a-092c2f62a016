<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>60-怎么让英文大语言模型支持中文？（三） —— 对预训练模型进行指令微调</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>60-怎么让英文大语言模型支持中文？（三） —— 对预训练模型进行指令微调</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/60-怎么让英文大语言模型支持中文？（三） —— 对预训练模型进行指令微调_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/60-怎么让英文大语言模型支持中文？（三） —— 对预训练模型进行指令微调_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>怎么让英文大语言模型支持中文？（三） —— 对预训练模型进行指令微调<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:32<br>
一、为什么需要对预训练模型进行指令微调？<br>
在之前讲过的继续预训练之后，我们应该对数据处理到训练、预测的整个流程有所了解，其实，基本上过程是差<br>
不多的。我们在选择好一个大语言模型之后。比如chatglm、llama、bloom等，要想使用它，得了解三个方面：<br>
输入数据的格式、tokenization、模型的使用方式。<br>
二、对预训练模型进行指令微调 数据 如何处理？<br>
数据的输入的话，一般情况下我们要在模型的官方代码上找到数据输入的那部分，或者说找到其它的一些开源的<br>
项目里面关于数据预处理的部分。找一份小的数据集，将这部分单独拿出来运行一下，看一下输出是什么。返回<br>
的结果是什么。比如一般看一下input_ids里面的特殊标记，labels是怎么构造的。举个例子，cpm-bee在forward<br>
里面需要额外传入span和length，与一般的不同只需要传入input_ids和labels。<br>
这里我们看下chatglm的数据格式是怎么样的，在test_dataset.py里面：<br>
import logging<br>
import os<br>
from dataclasses import dataclass<br>
from typing import Optional, Dict, Sequence, Union, List<br>
import datasets<br>
import torch<br>
import logging<br>
from datasets import load_dataset, concatenate_datasets<br>
import copy<br>
import transformers<br>
import random<br>
IGNORE_INDEX = -100<br>
logger = logging.getLogger('__name__')<br>
PROMPT_TEMPLATE = (<br>
    "Below is an instruction that describes a task. "<br>
    "Write a response that appropriately completes the request.\n\n"<br>
    "### Instruction:\n{instruction}\n\n### Response: "<br>
)<br>
def buid_instruction_dataset(data_path: Union[List[str],str],<br>
                tokenizer: transformers.PreTrainedTokenizer,<br>
                max_seq_length: int, data_cache_dir = None,<br>
                preprocessing_num_workers = None,<br>
                ):<br>
    def tokenization(examples):<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>        sources = []<br>
        targets = []<br>
        # prompt = PROMPT_TEMPLATE<br>
        for instruction, input, output in <br>
zip(examples['instruct'],examples['query'],examples['answer']):<br>
            if input is not None and input !="":<br>
                instruction = instruction+'\n'+input<br>
            # source = prompt.format_map({'instruction': instruction})<br>
            source = instruction<br>
            target = f"{tokenizer.bos_token}{output}{tokenizer.eos_token}"<br>
            sources.append(source)<br>
            targets.append(target)<br>
        tokenized_sources = tokenizer(sources,return_attention_mask=False, <br>
add_special_tokens=False)<br>
        tokenized_targets = tokenizer(targets,return_attention_mask=False, <br>
add_special_tokens=False)<br>
        <br>
        print(tokenized_targets)<br>
        <br>
        all_input_ids = []<br>
        all_labels = []<br>
        for s,t in <br>
zip(tokenized_sources['input_ids'],tokenized_targets['input_ids']):<br>
            s = s + [tokenizer.gmask_token_id]<br>
            input_ids = torch.LongTensor(s + t)[:max_seq_length]<br>
            labels = torch.LongTensor([IGNORE_INDEX] * len(s) + t)[:max_seq_length]<br>
            assert len(input_ids) == len(labels)<br>
            all_input_ids.append(input_ids)<br>
            all_labels.append(labels)<br>
        results = {'input_ids':all_input_ids, 'labels': all_labels}<br>
        return results<br>
    logging.warning("building dataset...")<br>
    all_datasets = []<br>
    if not isinstance(data_path,(list,tuple)):<br>
        data_path = [data_path]<br>
    for file in data_path:<br>
        if data_cache_dir is None:<br>
            data_cache_dir = str(os.path.dirname(file))<br>
        cache_path = os.path.join(data_cache_dir,os.path.basename(file).split('.')<br>
[0])<br>
</p>

<h2>第 3 页</h2>

<p>        os.makedirs(cache_path, exist_ok=True)<br>
        try:<br>
            processed_dataset = datasets.load_from_disk(cache_path)<br>
            logger.info(f'training datasets-{file} has been loaded from disk')<br>
        except Exception:<br>
            print(file)<br>
            raw_dataset = load_dataset("json", data_files=file, <br>
cache_dir=cache_path)<br>
            print(raw_dataset)<br>
            tokenization_func = tokenization<br>
            tokenized_dataset = raw_dataset.map(<br>
                tokenization_func,<br>
                batched=True,<br>
                num_proc=preprocessing_num_workers,<br>
                remove_columns=["instruct","query","answer"],<br>
                keep_in_memory=False,<br>
                desc="preprocessing on dataset",<br>
            )<br>
            processed_dataset = tokenized_dataset<br>
            processed_dataset.save_to_disk(cache_path)<br>
        processed_dataset.set_format('torch')<br>
        all_datasets.append(processed_dataset['train'])<br>
    all_datasets = concatenate_datasets(all_datasets)<br>
    return all_datasets<br>
@dataclass<br>
class DataCollatorForSupervisedDataset(object):<br>
    """Collate examples for supervised fine-tuning."""<br>
    tokenizer: transformers.PreTrainedTokenizer<br>
    def __call__(self, instances: Sequence[Dict]) -&gt; Dict[str, torch.Tensor]:<br>
        input_ids = instances["input_ids"]<br>
        labels = instances["labels"]<br>
        input_ids = torch.nn.utils.rnn.pad_sequence(<br>
            input_ids, batch_first=True, padding_value=self.tokenizer.pad_token_id<br>
        )<br>
        labels = torch.nn.utils.rnn.pad_sequence(labels, batch_first=True, <br>
padding_value=-100)<br>
        return dict(<br>
            input_ids=input_ids,<br>
            labels=labels,<br>
        )<br>
if __name__ == "__main__":<br>
  from transformers import AutoModelForCausalLM, AutoTokenizer<br>
</p>

<h2>第 4 页</h2>

<p>指令数据一般由三部分组成：instruction(instruct)、input(query)、output(answer)，分别表示提示指令、文本、<br>
返回的结果。 构造的时候一般是instruction和input进行拼接，当然input可能是为空的，最终对output进行预测。<br>
需要注意的是，除了instruction之外，可能还有特殊的prompt，不同模型的prompt是不一样的，比如：<br>
我们在构造的时候最好想之前预训练模型那样构造样本。<br>
接下来再讲讲input_ids和labels。假设我们现在有样本：我爱北京天安门，你喜欢什么？，分词之后得到["我", <br>
"爱", "北京", "天安门", "你", "喜欢", "什么", "？"]，之后转换为token_id，[12, 112, 122324, 22323, 23, 2346, <br>
1233, 545]，我们有Output：我喜欢故宫，转换为token_id：[12, 2346, 654]，一般情况下，output前后会被标<br>
识，比如bos_token_id和eos_token_id，假设分别为1和2，那么我们样本的输入就是：[12, 112, 122324, 22323, <br>
23, 2346, 1233, 545] + [1] + [12, 2346, 654] + [2]。至于labels的构建，直接说明为：[-100, -100, -100, -100, <br>
-100, -100, -100, -100, 1, 12, 2346, 654, 2]，长度和input_ids保持一致。有人可能会疑惑，不是说是根据上一个<br>
字预测下一个字吗? 怎么是自己预测自己。这是因为一般的模型内部在前向计算的时候已经帮我们处理了：<br>
input_ids = input_ids[-1] labels=labels[1:]。-100是表示在计算损失的时候不考虑标签为-100的位置。如果还设置<br>
了文本最大长度，则input_ids后面用pad_token_id进行填充，需要注意可能有的模型的tokenization中pad_token<br>
为None，需要自己去设置一个，可以和eos_token_id一样。而标签需要用-100进行填充。<br>
针对于chatglm，除了上述说明的外，它还有一个额外的[gMASK]标记。而它的输入为：<br>
所以说不同模型的输入构造可能不大一样，需要注意：<br>
三、对预训练模型进行指令微调 tokenization 如何构建？<br>
tokenization也很重要，我们一般可以先探索一下，在test_tokenizer.py中：<br>
  tokenizer = AutoTokenizer.from_pretrained("model_hub/chatglm-6b", <br>
trust_remote_code=True)<br>
  all_datasets = buid_instruction_dataset(["data/msra/train.txt"], tokenizer, <br>
max_seq_length=256)<br>
  print(all_datasets[0])<br>
  data_collator = DataCollatorForSupervisedDataset(tokenizer=tokenizer)<br>
  data = data_collator(all_datasets[:2])<br>
  print(data) <br>
PROMPT_DICT = {<br>
    "chatglm_input": ("{instruction}{input}"),<br>
    "alpaca_input": (<br>
        "Below is an instruction that describes a task. "<br>
        "Write a response that appropriately completes the request.\n\n"<br>
        "### Instruction:\n{instruction}{input}\n\n### Response: "<br>
    ),<br>
    "bloom_input": ("Human: \n{instruction}{input}\n\nAssistant: \n"),<br>
}<br>
# instruction为instruction + input<br>
# [gmask]等标记转换为id，这里直接展示<br>
input_ids = instruction_ids + [gmask] + &lt;sop&gt; + output_ids + &lt;eop&gt;<br>
# +1是[gmask]<br>
-100 * len(instruction_ids + 1) + &lt;sop&gt; + output_ids + &lt;eop&gt;<br>
1. 特殊标记的使用。<br>
2. 除了input_ids和labels，是否需要额外的输入。<br>
3. 有的模型内部是帮你自动转换labels和input_ids计算损失，有的没有转换，可能需要自己手动转换，比如<br>
cpm-bee。<br>
</p>

<h2>第 5 页</h2>

<p>四、对预训练模型进行指令微调 模型 如何构建？<br>
模型加载方式的话，一般使用的是AutoTenizer和AutoModelForCausalLM，但有的模型可能这么加载会报错。比<br>
如LLaMA的加载方式就是：LlamaForCausalLM和LlamaTokenizer,。针对于chatglm的话，加载方式为：<br>
AutoTenizer和AutoModel，但需要注意的是其加载的时候设置了trust_remote_code=True，该参数会根据映射找<br>
到真正使用的模型文件，比如modeling_chatglm.py。下载好模型权重后，我们可以根据情况先看看效果，在<br>
test_model.py里面:<br>
from transformers import AutoTokenizer, AutoModel<br>
tokenizer = AutoTokenizer.from_pretrained("model_hub/chatglm-6b", <br>
trust_remote_code=True)<br>
text = "我爱北京天安门"<br>
print(tokenizer(text))<br>
print(tokenizer.convert_ids_to_tokens([18060, 12247, 14949]))<br>
print(tokenizer.decode([18060, 12247, 14949]))<br>
# 打印特殊 token<br>
print("BOS token: ", tokenizer.bos_token)<br>
print("EOS token: ", tokenizer.eos_token)<br>
print("PAD token: ", tokenizer.pad_token)<br>
print("UNK token: ", tokenizer.unk_token)<br>
# 打印特殊 token_id<br>
print("BOS token: ", tokenizer.bos_token_id)<br>
print("EOS token: ", tokenizer.eos_token_id)<br>
print("PAD token: ", tokenizer.pad_token_id)<br>
print("UNK token: ", tokenizer.unk_token_id)<br>
print(tokenizer.decode([130004,<br>
          67470,     24,  83049,      4,  76699,     24,  83049,      4,  67357,<br>
          65065,     24,  83049,      4,  64484,  68137,  63940,     24,  64539,<br>
          63972,      4,  69670,  72232,  69023,     24,  83049,      4,  64372,<br>
          64149,     24,  83049,      4,  63855,     24,  83049, 130005]))<br>
# 这个是chatglm特有的。<br>
input_ids = tokenizer.build_inputs_with_special_tokens([1], [2])<br>
print(input_ids)<br>
from transformers import AutoTokenizer, AutoModel<br>
tokenizer = AutoTokenizer.from_pretrained("model_hub/chatglm-6b", <br>
trust_remote_code=True)<br>
model = AutoModel.from_pretrained("model_hub/chatglm-6b", <br>
trust_remote_code=True).half().cuda()<br>
model = model.eval()<br>
response, history = model.chat(tokenizer, "你好", history=[])<br>
print(response)<br>
response, history = model.chat(tokenizer, "晚上睡不着应该怎么办", history=history)<br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="images/60-怎么让英文大语言模型支持中文？（三） —— 对预训练模型进行指令微调_page6_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>五、是否可以结合 其他库 使用？<br>
其它的一些就是结合一些库的使用了，比如：<br>
需要注意的是， 我们可以把数据拆分为很多小文件放在一个文件夹下，然后遍历文件夹里面的数据，用datasets<br>
加载数据并进行并行处理后保存到磁盘上。如果中间发现处理数据有问题的话要先删除掉保存的处理后的数据，<br>
再重新进行处理，否则的话就是直接加载保存的处理好的数据。<br>
在SFT之后其实应该还有对齐这部分，就是对模型的输出进行规范，比如使用奖励模型+基于人类反馈的强化学<br>
习等，这里就不作展开了。<br>
最后，接下来的话终于要开始去好好了解下langchain了，一直都在关注这个但没有好好地看下。<br>
知识星球<br>
print(response)<br>
• deepspeed<br>
• transformers<br>
• peft中使用的lora<br>
• datasets加载数据<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:46</p>
        </div>
    </div>
</body>
</html>