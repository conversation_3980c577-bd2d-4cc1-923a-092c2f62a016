<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>86-多模态常见面试篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>86-多模态常见面试篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/86-多模态常见面试篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/86-多模态常见面试篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/86-多模态常见面试篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>多模态常见面试篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月12日 06:48<br>
一、最近关注的论文，多模态视觉大模型(CLIP,DALLE)？<br>
多模态视觉大模型是指可以处理多种感知模态数据（如图像和文本）的大型深度学习模型。CLIP<br>
和DALL·E都是这方面的重要研究。<br>
CLIP（Contrastive Language-Image Pretraining）模型能够将图像和文本嵌入空间连接在一起，<br>
使得模型可以理解图像和文本之间的语义关系。<br>
DALL·E是一个生成模型，可以根据文本描述生成与之相关的图像。<br>
二、blip2的架构，优势和之前多模态模型的区别？<br>
blip2是图像-语言多模态模型的预训练方法。这个架构是2023年才提出的，也看出来面试紧跟时事<br>
了。<br>
blip2的一个常见模式是输入一张图片，输出这张图片的描述。<br>
bilp2是在冻结的图像模型（负责从图像中提取特征，比如vit）和冻结的语言模型（负责生成语言）<br>
中间放入一个Q-Former，我们的目标就是训练这个Q-Former。Q-Former包含图像Transformer和<br>
语言Transformer，图像Transformer包含CA和SA，SA和语言Transformer共享参数，CA只接受图<br>
像模型提取的图像特征，图像模型的输入是一个查询值，这个查询值将在SA中和自己交互，在CA<br>
中和图像特征交互。最后图像Transformer输出一个综合图像特征的向量，同时语言Transformer输<br>
入一个文本，进行encode，得到一个文本的向量。然后根据具体的任务选择不同的方式对这两个<br>
向量进行操作。最后，Q-former把得到的向量传给冻结的语言模型。语言Transformer训练的时候<br>
做解码器，预测的时候是解码器。<br>
训练的时候先训练Q-Former和图像模型的交互，然后把Q-Former的结果和语言模型连接（中间可<br>
以加入全连接，前缀词等操作）。如下图<br>
三、多模态融合后，怎样知道最终结果受哪种模态影响更大？<br>
• 多模态常见面试篇<br>
• 一、最近关注的论文，多模态视觉大模型(CLIP,DALLE)？<br>
• 二、blip2的架构，优势和之前多模态模型的区别？<br>
• 三、多模态融合后，怎样知道最终结果受哪种模态影响更大？<br>
• 四、多模态中常见的SOTA模型有哪些？<br>
• 五、介绍一下stable diffusion的原理？<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/86-多模态常见面试篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>在多模态融合后，了解最终结果受哪种模态影响更大可以使用特征重要性分析方法，如SHAP值、<br>
Permutation Importance等。这些方法可以帮助识别每个模态对最终结果的贡献程度。<br>
四、多模态中常见的SOTA模型有哪些？<br>
五、介绍一下stable diffusion的原理？<br>
stable diffusion是一种生成模型，其原理基于Langevin动力学和扩散过程。其核心思想是通过多次<br>
迭代，逐渐将噪声信号演化为目标分布所对应的样本。具体原理如下：<br>
stable diffusion通过合理的选择演化步长和迭代次数，可以在生成样本的过程中平衡样本质量和生<br>
成速度。<br>
知识星球<br>
• Vision Transformer (ViT): 将自注意力机制引入计算机视觉领域，通过将图像划分为图像补丁并<br>
应用Transformer模型，实现了在图像分类和目标检测等任务上的出色表现。<br>
• CLIP (Contrastive Language-Image Pretraining): 结合了图像和文本的对比学习，通过训练一<br>
个模型，使其能够根据图像和文本之间的相互关系进行推理，实现了图像与文本之间的联合理<br>
解和表示学习。<br>
• UNITER (UNiversal Image-Text Representation): 使用Transformer架构，联合学习图像和文本<br>
表示，提供了一个通用的图像和文本特征提取框架，适用于多个视觉和语言任务。<br>
• LXMERT (Cross-Modal Transformer): 结合了视觉和语言信息，通过Transformer模型对图像和<br>
文本进行交互学习，可以用于视觉问答、图像描述生成等任务。<br>
• CoCa (Contrastive Captioners)：这是一种融合了单编码器、双编码器和编码器-解码器三种结<br>
构的多模态模型，既能生成图像侧和文本侧独立的表示，又能进行更深层次的图像、文本信息<br>
融合以及文本生成。CoCa在图像分类、图文检索、看图说话、VQA等多个任务上都取得了<br>
SOTA效果。<br>
• 初始化噪声信号为服从高斯分布的随机向量。<br>
• 通过一系列的演化步骤，将噪声信号迭代地转化为目标分布的样本。每一步中，将当前噪声信<br>
号与目标分布的梯度信息结合，通过Langevin动力学方程进行更新，使噪声信号逐渐接近目标<br>
分布。<br>
• 迭代的次数越多，噪声信号越接近目标分布，并最终生成目标分布的样本。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:47</p>
        </div>
    </div>
</body>
</html>