<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>41-大模型（LLMs）LLM生成SFT数据方法面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>41-大模型（LLMs）LLM生成SFT数据方法面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/41-大模型（LLMs）LLM生成SFT数据方法面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/41-大模型（LLMs）LLM生成SFT数据方法面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/41-大模型（LLMs）LLM生成SFT数据方法面_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>大模型（LLMs）LLM生成SFT数据方法面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年12月23日 12:23<br>
一、SFT数据集如何生成？<br>
SFT数据集构建通常有两种方法：人工标注和使用LLM（比如GPT-4）来生成的，人工标注对于构<br>
建垂直领域比较合适，可以减少有偏数据，但是成本略高；使用LLM生成，可以在短时间内生成大<br>
量数据。<br>
SFT数据集构建以及SFT微调Pipeline如下图所示：<br>
二、Self-Instruct 篇<br>
2.1 什么是 Self-Instruct ？<br>
Self-Instruct（https://arxiv.org/abs/2212.10560）：一个通过预训练语言模型自己引导自己来提高<br>
的指令遵循能力的框架。<br>
2.2 Self-Instruct 处理思路？<br>
• 步骤1：作者从 175个种子任务中随机抽取 8 条自然语言指令作为示例，并提示InstructGPT生<br>
成更多的任务指令。<br>
• 步骤2：作者确定步骤1中生成的指令是否是一个分类任务。如果是，他们要求 InstructGPT 根<br>
据给定的指令为输出生成所有可能的选项，并随机选择特定的输出类别，提示 InstructGPT 生<br>
成相应的“输入”内容。对于不属于分类任务的指令，应该有无数的“输出”选项。作者提出了“输<br>
入优先”策略，首先提示 InstructGPT根据给定的“指令”生成“输入”，然后根据“指令”和生成的“输<br>
入”生成“输出”。<br>
• 步骤3：基于第 2 步的结果，作者使用 InstructGPT 生成相应指令任务的“输入”和“输出”，采用<br>
“输出优先”或“输入优先”的策略。<br>
• 步骤4：作者对生成的指令任务进行了后处理(例如，过滤类似指令，去除输入输出的重复数<br>
据)，最终得到52K条英文指令<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/41-大模型（LLMs）LLM生成SFT数据方法面_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/41-大模型（LLMs）LLM生成SFT数据方法面_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>三、Backtranslation 篇<br>
3.1 什么是 Backtranslation？<br>
回译在传统的机器学习中是一种数据增强方法，比如从中文翻译成英文，再从英文翻译会中文，这<br>
样生成的中文与原来的中文在语义上是一致的，但是文本不同；然而SFT数据生成的回译<br>
（https://arxiv.org/abs/2308.06259）则是通过输出来生成指令，具体步骤如下图所示：<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/41-大模型（LLMs）LLM生成SFT数据方法面_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:49</p>
        </div>
    </div>
</body>
</html>