<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>26-大模型（LLMs）参数高效微调(PEFT) 面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>26-大模型（LLMs）参数高效微调(PEFT) 面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/26-大模型（LLMs）参数高效微调(PEFT) 面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/26-大模型（LLMs）参数高效微调(PEFT) 面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/26-大模型（LLMs）参数高效微调(PEFT) 面_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/26-大模型（LLMs）参数高效微调(PEFT) 面_page1_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<p>大模型（LLMs）参数高效微调(PEFT) 面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月18日 20:55<br>
1. 微调方法是啥？如何微调？<br>
fine-tune，也叫全参微调，bert微调模型一直用的这种方法，全部参数权重参与更新以适配领域数据，效果好。<br>
prompt-tune, 包括p-tuning、lora、prompt-tuning、adaLoRA等delta tuning方法，部分模型参数参与微调，训练<br>
快，显存占用少，效果可能跟FT（fine-tune）比会稍有效果损失，但一般效果能打平。<br>
链家在BELLE的技术报告《A Comparative Study between Full-Parameter and LoRA-based Fine-Tuning on <br>
Chinese Instruction Data for Instruction Following Large Language Model》中实验显示：FT效果稍好于LoRA。<br>
peft的论文《ADAPTIVE BUDGET ALLOCATION FOR PARAMETER- EFFICIENT FINE-TUNING》显示的结<br>
果：AdaLoRA效果稍好于FT。<br>
2. 为什么需要 PEFT？<br>
在面对特定的下游任务时，如果进行Full FineTuning（即对预训练模型中的所有参数都进行微调），太过低效；<br>
而如果采用固定预训练模型的某些层，只微调接近下游任务的那几层参数，又难以达到较好的效果。<br>
3. 介绍一下 PEFT？<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>PEFT技术旨在通过最小化微调参数的数量和计算复杂度，来提高预训练模型在新任务上的性能，从而缓解大型<br>
预训练模型的训练成本。这样一来，即使计算资源受限，也可以利用预训练模型的知识来迅速适应新任务，实现<br>
高效的迁移学习。<br>
4. PEFT 有什么优点？<br>
PEFT技术可以在提高模型效果的同时，大大缩短模型训练时间和计算成本，让更多人能够参与到深度学习研究<br>
中来。除此之外，FEFT可以缓解全量微调带来灾难性遗忘的问题。<br>
5. 微调方法批处理大小模式GPU显存速度？<br>
微调方法批处理大小模式GPU显存速度<br>
6. Peft 和 全量微调区别？<br>
所谓的 fune-tine 只能改变风格, 不能改变知识, 是因为我们的 fine-tune, 像是 LoRA 本来就是低秩的, 没办法对模<br>
型产生决定性的改变. 要是全量微调, 还是可以改变知识的.<br>
7. 多种不同的高效微调方法对比<br>
像P-Tuning v2、LoRA等都是综合评估很不错的高效微调技术。如果显存资源有限可以考虑QLoRA；如果只是解<br>
决一些简单任务场景，可以考虑P-Tuning、Prompt Tuning也行。<br>
下表从参数高效方法类型、是否存储高效和内存高效、以及在减少反向传播成本和推理开销的计算高效五个维度<br>
比较了参数高效微调方法。<br>
 LoRA (r=8) 16 FP16 28GB 8ex/s<br>
 LoRA (r=8) 8 FP16 24GB 8ex/s<br>
 LoRA (r=8) 4 FP16 20GB 8ex/s<br>
 LoRA (r=8) 4 INT8 10GB 8ex/s<br>
 LoRA (r=8) 4 INT4 8GB 8ex/s<br>
 P-Tuning (p=16) 4 FP16 20GB 8ex/s<br>
 P-Tuning (p=16) 4 INT8 16GB 8ex/s<br>
 P-Tuning (p=16) 4 INT4 12GB 8ex/s<br>
 Freeze (l=3) 4 FP16 24GB 8ex/s<br>
 Freeze (l=3) 4 INT8 12GB 8ex/s<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/26-大模型（LLMs）参数高效微调(PEFT) 面_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/26-大模型（LLMs）参数高效微调(PEFT) 面_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>下表展示了各种参数高效方法的参与训练的参数量、最终模型与原始模型的改变参数（delta值）以及论文中参与<br>
评估的模型的范围（&lt;1B、&lt;20B、&gt;20B）。<br>
从表中可以看到，Prompt Tuning、Prefix Tuning、LoRA等少部分微调技术针对不同参数规模的模型进行过评<br>
估，同时，这几种方式也是目前应用比较多的高效微调方法。<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="images/26-大模型（LLMs）参数高效微调(PEFT) 面_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>8. 当前高效微调技术存在的一些问题<br>
当前的高效微调技术很难在类似方法之间进行直接比较并评估它们的真实性能，主要的原因如下所示：<br>
9. 高效微调技术最佳实践<br>
针对以上存在的问题，研究高效微调技术时，建议按照最佳实践进行实施：<br>
10. PEFT 存在问题？<br>
相比全参数微调，大部分的高效微调技术目前存在的两个问题：<br>
11. 能不能总结一下各种参数高效微调方法？<br>
本文针对之前介绍的几种参数高效微调方法进行了简单的概述，主要有如下几类：<br>
并比较了不同的高效微调方法之间的差异；同时，还指出当前大多数高效微调方法存在的一些问题并给出了最佳<br>
实践。<br>
知识星球<br>
• 参数计算口径不一致：参数计算可以分为三类：可训练参数的数量、微调模型与原始模型相比改变的参数的<br>
数量、微调模型和原始模型之间差异的等级。例如，DiffPruning更新0.5%的参数，但是实际参与训练的参数<br>
量是200%。这为比较带来了困难。尽管可训练的参数量是最可靠的存储高效指标，但是也不完美。 Ladder-<br>
side Tuning使用一个单独的小网络，参数量高于LoRA或BitFit，但是因为反向传播不经过主网络，其消耗的<br>
内存反而更小。<br>
• 缺乏模型大小的考虑：已有工作表明，大模型在微调中需要更新的参数量更小（无论是以百分比相对而论还<br>
是以绝对数量而论），因此（基）模型大小在比较不同PEFT方法时也要考虑到。<br>
• 缺乏测量基准和评价标准：不同方法所使用的的模型/数据集组合都不一样，评价指标也不一样，难以得到有<br>
意义的结论。<br>
• 代码实现可读性差：很多开源代码都是简单拷贝Transformer代码库，然后进行小修小补。这些拷贝也不使用<br>
git fork，难以找出改了哪里。即便是能找到，可复用性也比较差（通常指定某个Transformer版本，没有说明<br>
如何脱离已有代码库复用这些方法）。<br>
• 明确指出参数数量类型。<br>
• 使用不同大小的模型进行评估。<br>
• 和类似方法进行比较。<br>
• 标准化PEFT测量基准。<br>
• 重视代码清晰度，以最小化进行实现。<br>
1. 推理速度会变慢；<br>
2. 模型精度会变差；<br>
• 增加额外参数，如：Prefix Tuning、Prompt Tuning、Adapter Tuning及其变体。<br>
• 选取一部分参数更新，如：BitFit。<br>
• 引入重参数化，如：LoRA、AdaLoRA、QLoRA。<br>
• 混合高效微调，如：MAM Adapter、UniPELT。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:48</p>
        </div>
    </div>
</body>
</html>