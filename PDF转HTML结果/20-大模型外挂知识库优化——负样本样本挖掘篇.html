<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>20-大模型外挂知识库优化——负样本样本挖掘篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>20-大模型外挂知识库优化——负样本样本挖掘篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page1_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<p>大模型外挂知识库优化——负样本样本挖掘篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年03月19日 22:30<br>
一、为什么需要构建负难样本？<br>
在各类检索任务中，为训练好一个高质量的检索模型，往往需要从大量的候选样本集合中采样高质<br>
量的负例，配合正例一起进行训练。<br>
二、负难样本构建方法篇<br>
2.1 随机采样策略（Random Sampling）方法<br>
对于随机采样方法，由于其采样得到的负例往往过于简单，其会导致该分数接近于零<br>
进而导致其生成的梯度均值也接近于零，<br>
这样过于小的梯度均值会导致模型不易于收敛。<br>
2.2 Top-K负例采样策略（Top-K Hard Negative Sampling）方法<br>
• 大模型外挂知识库优化——负样本样本挖掘篇<br>
• 一、为什么需要构建负难样本？<br>
• 二、负难样本构建方法篇<br>
• 2.1 随机采样策略（Random Sampling）方法<br>
• 2.2 Top-K负例采样策略（Top-K Hard Negative Sampling）方法<br>
• 2.3 困惑负样本采样方法SimANS 方法<br>
• 2.4 利用 对比学习微调 方式构建负例方法<br>
• 2.5 基于批内负采样的对比学习方法<br>
• 2.6 相同文章采样方法<br>
• 2.7 LLM辅助生成软标签及蒸馏<br>
• 辅助知识<br>
• 附一：梯度计算方法<br>
• 致谢<br>
• 方法：直接基于一均匀分布从所有的候选Document中随机抽取Document作为负例；<br>
• 存在问题：由于无法保证采样得到的负例的质量，故经常会采样得到过于简单的负例，其不仅<br>
无法给模型带来有用信息，还可能导致模型过拟合，进而无法区分某些较难的负例样本<br>
• 分析随机采样策略（Random Sampling）方法挖掘负例训练时对梯度的影响：<br>
• 方法：基于一稠密检索模型对所有候选Document与Query计算匹配分数，然后直接选择其中<br>
Top-K的候选Document作为负例；<br>
• 优点：可以保证采样得到的负例是模型未能较好区分的较难负例；<br>
• 存在问题：很可能将潜在的正例也误判为负例，即假负例（False Negative）。如果训练模型<br>
去将该部分假负例与正例区分开来，反而会导致模型无法准确衡量Query-Document的语义相似<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page2_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page2_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<p>由于其很容易采样得到语义与正例一致的假负例，其会导致正负样本的右项  <br>
 值相似，<br>
但是左项符号相反，这样会导致计算得到的梯度方差很大，同样导致模型训练不稳定。<br>
2.3 困惑负样本采样方法SimANS 方法<br>
通过以上分析可得，在该采样分布中，随着Query与候选Document相关分数 s(q,di) 和与正例的相<br>
关分数 s(q, d+) 的差值的缩小，该候选Document被采样作为负例的概率应该逐渐增大，故可将该<br>
差值作为输入，配合任意一单调递减函数 f(·) 即可实现（如 e−x）。故可设计采样分布如下所示：<br>
其中为控制该分布密度的超参数，b 为控制该分布极值点的超参数，<br>
是一随机采样的正<br>
例样本，D- 是Top-K的负例。通过调节 K 的大小，我们可以控制该采样分布的计算开销。以下为<br>
该采样方法具体实现的伪代码：<br>
度。<br>
• 分析 Top-K负例采样策略（Top-K Hard Negative Sampling）方法 挖掘负例训练时对梯度的影<br>
响：<br>
• 动机：在所有负例候选中，与Query的语义相似度接近于正例的负例可以同时具有较大的梯度<br>
均值和较小的梯度方差，是更加高质量的困惑负样本<br>
• 方法：对与正例语义相似度接近的困惑负例样本进行采样<br>
• 采样方法特点：<br>
• 与Query无关的Document应被赋予较低的相关分数，因其可提供的信息量不足；<br>
• 与Query很可能相关的Document应被赋予较低的相关分数，因其可能是假负例；<br>
• 与正例语义相似度接近的Document应该被赋予较高的相关分数，因其既需要被学习，同<br>
时是假负例的概率相对较低。<br>
• 困惑样本采样分布<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>2.4 利用 对比学习微调 方式构建负例方法<br>
对比学习是优化向量化模型的常用训练方法:<br>
论文：SimCSE: Simple Contrastive Learning of Sentence Embeddings<br>
分别将B1个问题，和B2个文档片段通过向量化模型变成向量形式，然后通过矩阵乘积计算每个问<br>
题和文档的相似度，最后通过交叉熵损失进行优化。如果文档负例仅来自于同一个batch的其他样<br>
本的文档正例，那么B1=B2；如果人工的给每个样本陪k个文档负例（比如可以通过难例挖掘得<br>
到），那么B2 =(k+1)*B1。<br>
• 目的：优化向量化模型，使其向量化后的文本，相似的在向量空间距离近，不相似的在向量空<br>
间距离远。文档召回场景下，做对比学习（有监督）需要三元组（问题，文档正例，文档负<br>
例）。文档正例是和问题密切相关的文档片段，文档负例是和问题不相关的文档片段，可以是<br>
精挑细选的，也可以是随机出来的。<br>
• 构建方法：如果是随机出来的话，完全可以用同一个batch里，其他问题的文档正例当作某一<br>
个问题的文档负例，如果想要效果好，还需要有比较大的batch size。损失函数是基于批内负<br>
样本的交叉熵损失，如下公式所示,q、d分别表示问题和文档正例对应的向量，r 为温度系<br>
数,sim函数可以是cos相似度或者点积。<br>
• 实现方法：<br>
    q_reps = self.encode(query) # 问题矩阵 维度(B1, d)<br>
    d_reps = self.encode(doc) # 文档矩阵 维度(B2, d)<br>
    score = torch.matmul(q_reps, d_reps.transpose(0, 1)) #计算相似度矩阵 维度:(B1, <br>
B2)<br>
</p>

<h2>第 4 页</h2>

<p>注：bge2论文里，做基于批内负样本的对比学习时同时考虑了多任务问题。之前也介绍了，不同<br>
任务加的prompt是不同的，如果把不同任务的样本放到一个batch里，模型训练时候就容易出现<br>
偷懒的情况，有时候会根据pormpt的内容来区分正负例，降低任务难度，这是不利于对比学习效<br>
果的。因此，可以通过人为的规定，同一个batch里，只能出现同一种任务的样本缓解这个问<br>
题。（实际应用场景下，如果任务类别不是非常多的话，最好还是一个任务训练一个模型，毕竟<br>
向量化模型也不大，效果会好一些）<br>
2.5 基于批内负采样的对比学习方法<br>
参考论文：<br>
【1】Approximate nearest neighbor negative contrastive learning for dense text retrieval<br>
【2】Contrastive learning with hard negative samples<br>
【3】Hard negative mixing for contrastive learning<br>
【4】Optimizing dense retrieval model training with hard negatives<br>
【5】SimANS: Simple Ambiguous Negatives Sampling for Dense Text Retrieval<br>
2.6 相同文章采样方法<br>
2.7 LLM辅助生成软标签及蒸馏<br>
    scores = scores / self.temperature <br>
    target = torch.arange(scores.size(0), device=scores.device, dtype=torch.long) <br>
## 得交叉熵损失函数的标签<br>
    # 考虑文档负例不仅来自于batch内其他样本的文档正例，也可能人工的给每个样本构造一<br>
些文档负例。<br>
    target = target * (p_reps.size(0) // d_reps.size(0))<br>
    loss = cross_entropy(scores, target) //交叉熵损失函数<br>
• 本质：随机选取文档负例，如果能有针对性的，找到和文档正例比较像的文档负例（模型更难<br>
区分这些文档负例），加到训练里，是有助于提高对比学习效果的。就好比我们只有不断的做<br>
难题才能更好的提高考试水平。<br>
• 论文方法：在文档向量空间找到和文档正例最相近的文档片段当作文档负例，训练向量化模<br>
型。模型更新一段时间后，刷新文档向量，寻找新的文档负例，继续训练模型。<br>
• 思路：文档正例所在的文章里，其他文档片段当作难负例，毕竟至少是属于同一主题的，和随<br>
机样本比起来比较难区分。<br>
• 存在问题：实际应用场景下，如果你的数据比较脏，难例挖掘用处可能不大。<br>
• 方法：根据用户问题召回的相关文档片段最终是要为LLM回答问题服务的，因此LLM认为召回<br>
的文档是否比较好很重要，以下介绍的方法是bge2提出的。对于向量化模型的训练，可以让<br>
LLM帮忙生成样本的辅助标签，引导向量化模型训练。辅助标签的生成可用如下公式表示。在<br>
已知LLM需要输出的标准答案下，分别将问题和各个文档片段C放入LLM的prompt中，看LLM<br>
生成标准答案的概率r大小，当作辅助标签。r越大，表示起对应的文档片段对生成正确答案的<br>
贡献越大，也就越重要。<br>
• 存在问题：<br>
• 打标要求有点太高；<br>
• 很多实际应用场景，我们并没法拿到LLM回答的标准答案，同时对每个问题的候选文档<br>
片段都计算一个r，开销貌似有点大。<br>
</p>

<h2>第 5 页</h2>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page5_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page5_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page5_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page5_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="images/20-大模型外挂知识库优化——负样本样本挖掘篇_page5_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<p>利用以上LLM生成的标签以及KL散度（笔者认为论文里这个形式的公式不能叫做KL散度<br>
吧。。），对模型进行优化。P为某个问题q对应的候选文档片段p的集合，e表示向量，&lt;.,.&gt;表示<br>
相似度操作，w是对所有候选文档p对应的辅助标签值r经过softmax变换后的值。本质是，如果LLM<br>
认为某个文档片段越重要，给它的优化权重越大。为了进一步稳定蒸馏效果，还可以对候选文档片<br>
段根据r进行排序，只用排名靠后的样本进行优化。<br>
辅助知识<br>
附一：梯度计算方法<br>
以稠密检索常用的BCE loss为例，正例与采样的负例在计算完语义相似度分数后，均会被softmax<br>
归一化，之后计算得到的梯度如下所示：<br>
注：<br>
 : 经过softmax归一化后的语义相似度分数<br>
知识星球<br>
• 优化策略：<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:46</p>
        </div>
    </div>
</body>
</html>