<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>58-怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>58-怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/58-怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/58-怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:24<br>
一、为什么需要 构建中文tokenization？<br>
大语言模型 百家争鸣趋势，但是 从 根本来看，主要 以 基于llama家族的模型 和 基于glm家族的模型为主。不过<br>
目前大多数 LLMs 还是以 基于llama家族的模型 为主，然而 原始基于llama家族的模型 主要训练语料为 英文语<br>
料，中文语料占比较少，直接导致，基于llama家族的模型 对于 中文的支持不太友好。<br>
那么有什么办法 能够 解决 基于llama家族的模型 对于 中文的支持不太友好 问题呢？<br>
本文利用 《斗破苍穹》作为语料，介绍 从 扩充vocab里面的词以对中文进行token化。<br>
二、如何对 原始数据预处理？<br>
《斗破苍穹》 原始数据<br>
上文摘取 部分 《斗破苍穹》 原始数据，从 数据中可以看出，数据中包含 大量换行 和 无效内容，所以需要对 <br>
《斗破苍穹》 原始数据 进行预处理，将每一行转化为一句或多句话，同时过滤掉 换行 和 无效内容。<br>
代码讲解<br>
    《斗破苍穹》来自: <br>
===上架感言===<br>
又一次上架了，这次比上次还激动，甚至激动到了上传了章节却不知道发出来的地步。<br>
    尴尬，关于新书，上架前成绩好得有些出乎土豆的意料，对于这份厚硕的成绩，土豆心里<br>
还真有几分惶恐与忐忑，虽说曾经有人说土豆是刷出来的数据，对于这些留言，我也并未太过<br>
在意，别的我不知道，我唯一能知道的，就是人在做，天在看！<br>
    究竟刷没刷，自己心中有杆秤就能衡量，问心无愧，何惧留言？<br>
    呵呵，在这里很感谢赐予土豆这种厚硕成绩的诸位书友，真的，很感谢你们。<br>
    ...<br>
# step 1: 《斗破苍穹》 原始数据 加载<br>
with open("data/《斗破苍穹》.txt", "r", encoding="utf-8") as fp:<br>
    data = fp.read().strip().split("\n")<br>
# step 2: 将每一行转化为一句或多句话，同时过滤掉 换行 和 无效内容<br>
sentences = []<br>
for d in data:<br>
    d = d.strip()<br>
    if "===" in d or len(d) == 0 or d == "《斗破苍穹》来自:":<br>
        continue<br>
    sentences.append(d)<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>预处理之后的 corpus.txt<br>
三、如何构建中文的词库？<br>
得到语料库 corpus.txt 之后，我们需要利用该 corpus.txt 构建 中文的词库。这里采用 sentencepiece 训练中文词<br>
库。<br>
运行 上述代码之后会得到 tokenizer.model和tokenizer.vocab两个文件<br>
tokenizer.vocab 词表: 除了一些特殊符号外，还有我们自定义的foo和bar，其余的一些词是BPE训练得到<br>
# step 3: 数据写入<br>
with open("data/corpus.txt", "w", encoding="utf-8") as fp:<br>
    fp.write("\n".join(sentences))<br>
    又一次上架了，这次比上次还激动，甚至激动到了上传了章节却不知道发出来的地步。<br>
    尴尬，关于新书，上架前成绩好得有些出乎土豆的意料，对于这份厚硕的成绩，土豆心里<br>
还真有几分惶恐与忐忑，虽说曾经有人说土豆是刷出来的数据，对于这些留言，我也并未太过<br>
在意，别的我不知道，我唯一能知道的，就是人在做，天在看！<br>
    ...<br>
1. sentencepiece 安装<br>
    $ pip install sentencepiece<br>
2. 训练中文词库<br>
    import sentencepiece as spm<br>
    spm.SentencePieceTrainer.train(<br>
        input='data/corpus.txt',<br>
        model_prefix='tokenizer',<br>
        vocab_size=50000,<br>
        user_defined_symbols=['foo', 'bar'],<br>
        character_coverage=1.0,<br>
        model_type="bpe",<br>
    )<br>
• 参数介绍：<br>
• input：指定输入文本文件的路径或者是一个目录，可以指定多个输入文件或目录。其中每一行可以<br>
是一句话或者多句话；<br>
• tokenizer：保存的模型的名称前缀；<br>
• vocab_size：设置的词表大小；<br>
• user_defined_symbols：用于指定用户自定义的符号。这些符号将会被视为单独的 Token，不会被拆<br>
分成子词。这个参数的作用是将一些用户定义的特殊符号作为一个整体加入到生成的词表中，以便于<br>
后续的模型使用。这里我们简单进行了测试；<br>
• model_type: 指定模型的类型，有三种可选参数：unigram, bpe, char. word；<br>
• character_coverage：指定覆盖字符的数量，可以理解为限制字符集的大小。默认值为 1.0，即覆盖<br>
全部字符；<br>
• unk_id: 指定未登录词的 ID 号，即在词表中为未登录词分配一个整数 ID。默认值为 0；<br>
• bos_id: 指定句子开头符号的 ID 号，即在词表中为句子开头符号分配一个整数 ID。默认值为 1；<br>
• eos_id: 指定句子结束符号的 ID 号，即在词表中为句子结束符号分配一个整数 ID。默认值为 2；<br>
• pad_id: 指定填充符号的 ID 号，即在词表中为填充符号分配一个整数 ID。默认值为 -1，即不使用填<br>
充符号；<br>
&lt;unk&gt;<br>
0<br>
&lt;s&gt; 0<br>
</p>

<h2>第 3 页</h2>

<p>四、如何使用transformers库加载sentencepiece模型？<br>
chinese_bpe.py<br>
&lt;/s&gt;<br>
0<br>
foo 0<br>
bar 0<br>
萧炎<br>
-0<br>
..<br>
-1<br>
▁“<br>
-2<br>
也是<br>
-3<br>
便是<br>
-4<br>
了一<br>
-5<br>
...<br>
import os<br>
os.environ["PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"] = "python"<br>
from transformers import LlamaTokenizer<br>
from sentencepiece import sentencepiece_model_pb2 as sp_pb2_model<br>
import sentencepiece as spm<br>
from tokenization import ChineseTokenizer<br>
chinese_sp_model_file = "sentencepisece_tokenizer/tokenizer.model"<br>
# load<br>
chinese_sp_model = spm.SentencePieceProcessor()<br>
chinese_sp_model.Load(chinese_sp_model_file)<br>
chinese_spm = sp_pb2_model.ModelProto()<br>
chinese_spm.ParseFromString(chinese_sp_model.serialized_model_proto())<br>
## Save<br>
output_dir = './transformers_tokenizer/chinese/'<br>
os.makedirs(output_dir, exist_ok=True)<br>
with open(output_dir + 'chinese.model', 'wb') as f:<br>
    f.write(chinese_spm.SerializeToString())<br>
tokenizer = ChineseTokenizer(vocab_file=output_dir + 'chinese.model')<br>
tokenizer.save_pretrained(output_dir)<br>
print(f"Chinese tokenizer has been saved to {output_dir}")<br>
# Test<br>
chinese_tokenizer = ChineseTokenizer.from_pretrained(output_dir)<br>
print(tokenizer.all_special_tokens)<br>
print(tokenizer.all_special_ids)<br>
print(tokenizer.special_tokens_map)<br>
text = '''白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including'''<br>
</p>

<h2>第 4 页</h2>

<p>运行结果<br>
注：其中ChineseTokenizer这里参考了llama模型里面使用的方法，并稍微做些修改：<br>
print("Test text:\n", text)<br>
print(f"Tokenized by Chinese tokenizer:{chinese_tokenizer.tokenize(text)}")<br>
Chinese tokenizer has been saved to ./transformers_tokenizer/chinese/<br>
['&lt;s&gt;', '&lt;/s&gt;', '&lt;unk&gt;']<br>
[1, 2, 0]<br>
{'bos_token': '&lt;s&gt;', 'eos_token': '&lt;/s&gt;', 'unk_token': '&lt;unk&gt;'}<br>
Test text:<br>
 白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including<br>
Tokenized by Chinese-LLaMA tokenizer:['▁', '白日', '依', '山', '尽', ',', '黄', <br>
'河', '入', '海', '流', '。', '欲', '穷', '千里', '目', ',', '更', '上一层', '楼', <br>
'。', '▁', 'T', 'h', 'e', '▁', 'p', 'r', 'i', 'm', 'a', 'r', 'y', '▁', 'u', 's', <br>
'e', '▁', 'o', 'f', '▁', 'LL', 'a', 'MA', '▁i', 's', '▁', 'r', 'e', 's', 'e', <br>
'a', 'r', 'ch', '▁', 'o', 'n', '▁', 'l', 'a', 'r', 'g', 'e', '▁', 'l', 'an', <br>
'g', 'u', 'a', 'g', 'e', '▁', 'm', 'o', 'd', 'e', 'l', 's', ',', '▁i', 'n', 'c', <br>
'lu', 'd', 'i', 'ng']<br>
# coding=utf-8<br>
# Copyright 2022 EleutherAI and the HuggingFace Inc. team. All rights reserved.<br>
#<br>
# This code is based on EleutherAI's GPT-NeoX library and the GPT-NeoX<br>
# and OPT implementations in this library. It has been modified from its<br>
# original forms to accommodate minor architectural differences compared<br>
# to GPT-NeoX and OPT used by the Meta AI team that trained the model.<br>
#<br>
# Licensed under the Apache License, Version 2.0 (the "License");<br>
# you may not use this file except in compliance with the License.<br>
# You may obtain a copy of the License at<br>
#<br>
#     http://www.apache.org/licenses/LICENSE-2.0<br>
#<br>
# Unless required by applicable law or agreed to in writing, software<br>
# distributed under the License is distributed on an "AS IS" BASIS,<br>
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.<br>
# See the License for the specific language governing permissions and<br>
# limitations under the License.<br>
"""Tokenization classes for LLaMA."""<br>
import os<br>
from shutil import copyfile<br>
from typing import Any, Dict, List, Optional, Tuple<br>
import sentencepiece as spm<br>
</p>

<h2>第 5 页</h2>

<p>from transformers.tokenization_utils import AddedToken, PreTrainedTokenizer<br>
from transformers.utils import logging<br>
logger = logging.get_logger(__name__)<br>
VOCAB_FILES_NAMES = {"vocab_file": "tokenizer.model"}<br>
# PRETRAINED_VOCAB_FILES_MAP = {<br>
#     "vocab_file": {<br>
#         "hf-internal-testing/llama-tokenizer": "https://huggingface.co/hf-<br>
internal-testing/llama-tokenizer/resolve/main/tokenizer.model",<br>
#     },<br>
#     "tokenizer_file": {<br>
#         "hf-internal-testing/llama-tokenizer": "https://huggingface.co/hf-<br>
internal-testing/llama-tokenizer/resolve/main/tokenizer_config.json",<br>
#     },<br>
# }<br>
# PRETRAINED_POSITIONAL_EMBEDDINGS_SIZES = {<br>
#     "hf-internal-testing/llama-tokenizer": 2048,<br>
# }<br>
class ChineseTokenizer(PreTrainedTokenizer):<br>
    """<br>
    Construct a Llama tokenizer. Based on byte-level Byte-Pair-Encoding.<br>
    Args:<br>
        vocab_file (`str`):<br>
            Path to the vocabulary file.<br>
    """<br>
    vocab_files_names = VOCAB_FILES_NAMES<br>
    # pretrained_vocab_files_map = PRETRAINED_VOCAB_FILES_MAP<br>
    # max_model_input_sizes = PRETRAINED_POSITIONAL_EMBEDDINGS_SIZES<br>
    model_input_names = ["input_ids", "attention_mask"]<br>
    def __init__(<br>
        self,<br>
        vocab_file,<br>
        unk_token="&lt;unk&gt;",<br>
        bos_token="&lt;s&gt;",<br>
        eos_token="&lt;/s&gt;",<br>
        pad_token=None,<br>
        sp_model_kwargs: Optional[Dict[str, Any]] = None,<br>
        add_bos_token=True,<br>
        add_eos_token=False,<br>
</p>

<h2>第 6 页</h2>

<p>        clean_up_tokenization_spaces=False,<br>
        **kwargs,<br>
    ):<br>
        self.sp_model_kwargs = {} if sp_model_kwargs is None else sp_model_kwargs<br>
        bos_token = AddedToken(bos_token, lstrip=False, rstrip=False) if <br>
isinstance(bos_token, str) else bos_token<br>
        eos_token = AddedToken(eos_token, lstrip=False, rstrip=False) if <br>
isinstance(eos_token, str) else eos_token<br>
        unk_token = AddedToken(unk_token, lstrip=False, rstrip=False) if <br>
isinstance(unk_token, str) else unk_token<br>
        pad_token = AddedToken(pad_token, lstrip=False, rstrip=False) if <br>
isinstance(pad_token, str) else pad_token<br>
        super().__init__(<br>
            bos_token=bos_token,<br>
            eos_token=eos_token,<br>
            unk_token=unk_token,<br>
            pad_token=pad_token,<br>
            add_bos_token=add_bos_token,<br>
            add_eos_token=add_eos_token,<br>
            sp_model_kwargs=self.sp_model_kwargs,<br>
            clean_up_tokenization_spaces=clean_up_tokenization_spaces,<br>
            **kwargs,<br>
        )<br>
        self.vocab_file = vocab_file<br>
        self.add_bos_token = add_bos_token<br>
        self.add_eos_token = add_eos_token<br>
        self.sp_model = spm.SentencePieceProcessor(**self.sp_model_kwargs)<br>
        self.sp_model.Load(vocab_file)<br>
    def __getstate__(self):<br>
        state = self.__dict__.copy()<br>
        state["sp_model"] = None<br>
        return state<br>
    def __setstate__(self, d):<br>
        self.__dict__ = d<br>
        self.sp_model = spm.SentencePieceProcessor(**self.sp_model_kwargs)<br>
        self.sp_model.Load(self.vocab_file)<br>
    @property<br>
    def vocab_size(self):<br>
        """Returns vocab size"""<br>
        return self.sp_model.get_piece_size()<br>
    def get_vocab(self):<br>
        """Returns vocab as a dict"""<br>
        vocab = {self.convert_ids_to_tokens(i): i for i in range(self.vocab_size)}<br>
</p>

<h2>第 7 页</h2>

<p>        vocab.update(self.added_tokens_encoder)<br>
        return vocab<br>
    def _tokenize(self, text):<br>
        """Returns a tokenized string."""<br>
        return self.sp_model.encode(text, out_type=str)<br>
    def _convert_token_to_id(self, token):<br>
        """Converts a token (str) in an id using the vocab."""<br>
        return self.sp_model.piece_to_id(token)<br>
    def _convert_id_to_token(self, index):<br>
        """Converts an index (integer) in a token (str) using the vocab."""<br>
        token = self.sp_model.IdToPiece(index)<br>
        return token<br>
    def convert_tokens_to_string(self, tokens):<br>
        """Converts a sequence of tokens (string) in a single string."""<br>
        current_sub_tokens = []<br>
        out_string = ""<br>
        prev_is_special = False<br>
        for i, token in enumerate(tokens):<br>
            # make sure that special tokens are not decoded using sentencepiece <br>
model<br>
            if token in self.all_special_tokens:<br>
                if not prev_is_special and i != 0:<br>
                    out_string += " "<br>
                out_string += self.sp_model.decode(current_sub_tokens) + token<br>
                prev_is_special = True<br>
                current_sub_tokens = []<br>
            else:<br>
                current_sub_tokens.append(token)<br>
                prev_is_special = False<br>
        out_string += self.sp_model.decode(current_sub_tokens)<br>
        return out_string<br>
    def save_vocabulary(self, save_directory, filename_prefix: Optional[str] = <br>
None) -&gt; Tuple[str]:<br>
        """<br>
        Save the vocabulary and special tokens file to a directory.<br>
        Args:<br>
            save_directory (`str`):<br>
                The directory in which to save the vocabulary.<br>
        Returns:<br>
            `Tuple(str)`: Paths to the files saved.<br>
</p>

<h2>第 8 页</h2>

<p>        """<br>
        if not os.path.isdir(save_directory):<br>
            logger.error(f"Vocabulary path ({save_directory}) should be a <br>
directory")<br>
            return<br>
        out_vocab_file = os.path.join(<br>
            save_directory, (filename_prefix + "-" if filename_prefix else "") + <br>
VOCAB_FILES_NAMES["vocab_file"]<br>
        )<br>
        if os.path.abspath(self.vocab_file) != os.path.abspath(out_vocab_file) and <br>
os.path.isfile(self.vocab_file):<br>
            copyfile(self.vocab_file, out_vocab_file)<br>
        elif not os.path.isfile(self.vocab_file):<br>
            with open(out_vocab_file, "wb") as fi:<br>
                content_spiece_model = self.sp_model.serialized_model_proto()<br>
                fi.write(content_spiece_model)<br>
        return (out_vocab_file,)<br>
    def build_inputs_with_special_tokens(self, token_ids_0, token_ids_1=None):<br>
        bos_token_id = [self.bos_token_id] if self.add_bos_token else []<br>
        eos_token_id = [self.eos_token_id] if self.add_eos_token else []<br>
        output = bos_token_id + token_ids_0 + eos_token_id<br>
        if token_ids_1 is not None:<br>
            output = output + bos_token_id + token_ids_1 + eos_token_id<br>
        return output<br>
    def get_special_tokens_mask(<br>
        self, token_ids_0: List[int], token_ids_1: Optional[List[int]] = None, <br>
already_has_special_tokens: bool = False<br>
    ) -&gt; List[int]:<br>
        """<br>
        Retrieve sequence ids from a token list that has no special tokens added. <br>
This method is called when adding<br>
        special tokens using the tokenizer `prepare_for_model` method.<br>
        Args:<br>
            token_ids_0 (`List[int]`):<br>
                List of IDs.<br>
            token_ids_1 (`List[int]`, *optional*):<br>
                Optional second list of IDs for sequence pairs.<br>
            already_has_special_tokens (`bool`, *optional*, defaults to `False`):<br>
</p>

<h2>第 9 页</h2>

<p>                Whether or not the token list is already formatted with special <br>
tokens for the model.<br>
        Returns:<br>
            `List[int]`: A list of integers in the range [0, 1]: 1 for a special <br>
token, 0 for a sequence token.<br>
        """<br>
        if already_has_special_tokens:<br>
            return super().get_special_tokens_mask(<br>
                token_ids_0=token_ids_0, token_ids_1=token_ids_1, <br>
already_has_special_tokens=True<br>
            )<br>
        bos_token_id = [1] if self.add_bos_token else []<br>
        eos_token_id = [1] if self.add_eos_token else []<br>
        if token_ids_1 is None:<br>
            return bos_token_id + ([0] * len(token_ids_0)) + eos_token_id<br>
        return (<br>
            bos_token_id<br>
            + ([0] * len(token_ids_0))<br>
            + eos_token_id<br>
            + bos_token_id<br>
            + ([0] * len(token_ids_1))<br>
            + eos_token_id<br>
        )<br>
    def create_token_type_ids_from_sequences(<br>
        self, token_ids_0: List[int], token_ids_1: Optional[List[int]] = None<br>
    ) -&gt; List[int]:<br>
        """<br>
        Creates a mask from the two sequences passed to be used in a sequence-pair <br>
classification task. An ALBERT<br>
        sequence pair mask has the following format:<br>
        ```<br>
        0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1<br>
        | first sequence    | second sequence |<br>
        ```<br>
        if token_ids_1 is None, only returns the first portion of the mask (0s).<br>
        Args:<br>
            token_ids_0 (`List[int]`):<br>
                List of ids.<br>
            token_ids_1 (`List[int]`, *optional*):<br>
                Optional second list of IDs for sequence pairs.<br>
</p>

<h2>第 10 页</h2>

<p>不难发现其实里面使用了一些sentencepiece里面的函数。<br>
五、如何合并英文词表和中文词表？<br>
chinese_llama_bpe.py<br>
        Returns:<br>
            `List[int]`: List of [token type IDs](../glossary#token-type-ids) <br>
according to the given sequence(s).<br>
        """<br>
        bos_token_id = [self.bos_token_id] if self.add_bos_token else []<br>
        eos_token_id = [self.eos_token_id] if self.add_eos_token else []<br>
        output = [0] * len(bos_token_id + token_ids_0 + eos_token_id)<br>
        if token_ids_1 is not None:<br>
            output += [1] * len(bos_token_id + token_ids_1 + eos_token_id)<br>
        return output<br>
import os<br>
os.environ["PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"] = "python"<br>
from transformers import LlamaTokenizer<br>
from sentencepiece import sentencepiece_model_pb2 as sp_pb2_model<br>
import sentencepiece as spm<br>
llama_tokenizer_dir = "transformers_tokenizer/llama/tokenizer.model"<br>
chinese_sp_model_file = "sentencepisece_tokenizer/tokenizer.model"<br>
# load<br>
llama_tokenizer = LlamaTokenizer.from_pretrained(llama_tokenizer_dir)<br>
llama_spm = sp_pb2_model.ModelProto()<br>
llama_spm.ParseFromString(llama_tokenizer.sp_model.serialized_model_proto())<br>
chinese_spm = sp_pb2_model.ModelProto()<br>
chinese_sp_model = spm.SentencePieceProcessor()<br>
chinese_sp_model.Load(chinese_sp_model_file)<br>
chinese_spm.ParseFromString(chinese_sp_model.serialized_model_proto())<br>
# print number of tokens<br>
print(len(llama_tokenizer), len(chinese_sp_model))<br>
print(llama_tokenizer.all_special_tokens)<br>
print(llama_tokenizer.all_special_ids)<br>
print(llama_tokenizer.special_tokens_map)<br>
## Add Chinese tokens to LLaMA tokenizer<br>
llama_spm_tokens_set = set(p.piece for p in llama_spm.pieces)<br>
print(len(llama_spm_tokens_set))<br>
</p>

<h2>第 11 页</h2>

<p>代码中，最核心的是以下这一块，该代码的作用是 将 原始词表 中没有的 新词 加入 词表中<br>
运行结果<br>
print(f"Before:{len(llama_spm_tokens_set)}")<br>
for p in chinese_spm.pieces:<br>
    piece = p.piece<br>
    if piece not in llama_spm_tokens_set:<br>
        new_p = sp_pb2_model.ModelProto().SentencePiece()<br>
        new_p.piece = piece<br>
        new_p.score = 0<br>
        llama_spm.pieces.append(new_p)<br>
print(f"New model pieces: {len(llama_spm.pieces)}")<br>
## Save<br>
output_sp_dir = 'transformers_tokenizer/llama_chinese'<br>
output_hf_dir = 'transformers_tokenizer/llama_chinese'  # the path to save Chinese-<br>
LLaMA tokenizer<br>
os.makedirs(output_sp_dir, exist_ok=True)<br>
with open(output_sp_dir + '/chinese_llama.model', 'wb') as f:<br>
    f.write(llama_spm.SerializeToString())<br>
tokenizer = LlamaTokenizer(vocab_file=output_sp_dir + '/chinese_llama.model')<br>
tokenizer.save_pretrained(output_hf_dir)<br>
print(f"Chinese-LLaMA tokenizer has been saved to {output_hf_dir}")<br>
# Test<br>
llama_tokenizer = LlamaTokenizer.from_pretrained(llama_tokenizer_dir)<br>
chinese_llama_tokenizer = LlamaTokenizer.from_pretrained(output_hf_dir)<br>
print(tokenizer.all_special_tokens)<br>
print(tokenizer.all_special_ids)<br>
print(tokenizer.special_tokens_map)<br>
text = '''白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including'''<br>
print("Test text:\n", text)<br>
print(f"Tokenized by LLaMA tokenizer:{llama_tokenizer.tokenize(text)}")<br>
print(f"Tokenized by Chinese-LLaMA tokenizer:<br>
{chinese_llama_tokenizer.tokenize(text)}")<br>
for p in chinese_spm.pieces:<br>
    piece = p.piece<br>
    if piece not in llama_spm_tokens_set:<br>
        new_p = sp_pb2_model.ModelProto().SentencePiece()<br>
        new_p.piece = piece<br>
        new_p.score = 0<br>
        llama_spm.pieces.append(new_p)<br>
32000 50000<br>
['&lt;s&gt;', '&lt;/s&gt;', '&lt;unk&gt;']<br>
</p>

<h2>第 12 页</h2>

<p>注：会发现再加入了我们定义的词表后确实能够对中文进行分词了。<br>
六、怎么使用修改后的词表？<br>
如果我们重新从头开始训练，那么其实使用起来很简单：<br>
但是如果我们想要保留原始模型embedding的参数，那么我们可以这么做：<br>
[1, 2, 0]<br>
{'bos_token': '&lt;s&gt;', 'eos_token': '&lt;/s&gt;', 'unk_token': '&lt;unk&gt;'}<br>
32000<br>
Before:32000<br>
New model pieces: 81163<br>
Chinese-LLaMA tokenizer has been saved to transformers_tokenizer/llama_chinese<br>
['&lt;s&gt;', '&lt;/s&gt;', '&lt;unk&gt;']<br>
[1, 2, 0]<br>
{'bos_token': '&lt;s&gt;', 'eos_token': '&lt;/s&gt;', 'unk_token': '&lt;unk&gt;'}<br>
Test text:<br>
 白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including<br>
Tokenized by LLaMA tokenizer:['▁', '白', '日', '&lt;0xE4&gt;', '&lt;0xBE&gt;', '&lt;0x9D&gt;', '山', <br>
'&lt;0xE5&gt;', '&lt;0xB0&gt;', '&lt;0xBD&gt;', '，', '黄', '河', '入', '海', '流', '。', '&lt;0xE6&gt;', <br>
'&lt;0xAC&gt;', '&lt;0xB2&gt;', '&lt;0xE7&gt;', '&lt;0xA9&gt;', '&lt;0xB7&gt;', '千', '里', '目', '，', '更', <br>
'上', '一', '&lt;0xE5&gt;', '&lt;0xB1&gt;', '&lt;0x82&gt;', '&lt;0xE6&gt;', '&lt;0xA5&gt;', '&lt;0xBC&gt;', '。', <br>
'&lt;0x0A&gt;', 'The', '▁primary', '▁use', '▁of', '▁L', 'La', 'MA', '▁is', <br>
'▁research', '▁on', '▁large', '▁language', '▁models', ',', '▁including']<br>
Tokenized by Chinese-LLaMA tokenizer:['▁白', '日', '依', '山', '尽', '，', '黄', <br>
'河', '入', '海', '流', '。', '欲', '穷', '千里', '目', '，', '更', '上一层', '楼', <br>
'。', '&lt;0x0A&gt;', 'The', '▁primary', '▁use', '▁of', '▁L', 'La', 'MA', '▁is', <br>
'▁research', '▁on', '▁large', '▁language', '▁models', ',', '▁including']<br>
config = AutoConfig.from_pretrained(...)<br>
tokenizer = LlamaTokenizer.from_pretrained(...)<br>
model = LlamaForCausalLM.from_pretrained(..., config=config)<br>
model_vocab_size = model.get_output_embeddings().weight.size(0)<br>
model.resize_token_embeddings(len(tokenizer))<br>
1. 找到新词表和旧词表id之间的映射关系。<br>
2. 将模型里面新词表里面包含的旧词表用原始模型的embedding替换。<br>
3. 如果新词在旧词表里面没有出现就进行相应的初始化再进行赋值。比如transformers库中的llama是这么进行<br>
初始化的：<br>
 def _init_weights(self, module):<br>
        std = self.config.initializer_range<br>
        if isinstance(module, nn.Linear):<br>
            module.weight.data.normal_(mean=0.0, std=std)<br>
            if module.bias is not None:<br>
                module.bias.data.zero_()<br>
        elif isinstance(module, nn.Embedding):<br>
            module.weight.data.normal_(mean=0.0, std=std)<br>
            if module.padding_idx is not None:<br>
</p>

<h2>第 13 页</h2>

<div class="image-container">
  <img src="images/58-怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization_page13_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>注：具体怎么做可以参考一下这个：https://github.com/yangjianxin1/LLMPruner<br>
总结一下 构建中文tokenization？<br>
到这里为止，我们已经学会了：<br>
知识星球<br>
                module.weight.data[module.padding_idx].zero_()<br>
1. 使用sentencepiece训练一个中文的词表。<br>
2. 使用transformers加载sentencepiece模型。<br>
3. 怎么合并中英文的词表，并使用transformers使用合并后的词表。<br>
4. 在模型中怎么使用新词表。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:48</p>
        </div>
    </div>
</body>
</html>