<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>6-LLMs 损失函数篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>6-LLMs 损失函数篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page1_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<p>LLMs 损失函数篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月28日 13:20<br>
一、介绍一下 KL 散度？<br>
KL（Kullback-Leibler）散度衡量了两个概率分布之间的差异。其公式为：<br>
二、交叉熵损失函数写一下，物理意义是什么？<br>
交叉熵损失函数（Cross-Entropy Loss Function）是用于度量两个概率分布之间的差异的一种损失函数。在分类<br>
问题中，它通常用于衡量模型的预测分布与实际标签分布之间的差异。<br>
注：其中，p 表示真实标签，q 表示模型预测的标签，N 表示样本数量。该公式可以看作是一个<br>
基于概率分布的比较方式，即将真实标签看做一个概率分布，将模型预测的标签也看做一个概率<br>
分布，然后计算它们之间的交叉熵。<br>
物理意义：交叉熵损失函数可以用来衡量实际标签分布与模型预测分布之间的“信息差”。当两个<br>
分布完全一致时，交叉熵损失为0，表示模型的预测与实际情况完全吻合。当两个分布之间存在<br>
差异时，损失函数的值会增加，表示预测错误程度的大小。<br>
三、KL 散度与交叉熵的区别？<br>
KL散度指的是相对熵，KL散度是两个概率分布P和Q差别的非对称性的度量。KL散度越小表示两个分布越接近。<br>
也就是说KL散度是不对称的，且KL散度的值是非负数。（也就是熵和交叉熵的差）<br>
• LLMs 损失函数篇<br>
• 一、介绍一下 KL 散度？<br>
• 二、交叉熵损失函数写一下，物理意义是什么？<br>
• 三、KL 散度与交叉熵的区别？<br>
• 四、多任务学习各loss差异过大怎样处理？<br>
• 五、分类问题为什么用交叉熵损失函数不用均方误差（MSE）？<br>
• 六、什么是信息增益？<br>
• 七、多分类的分类损失函数(Softmax)？<br>
• 八、softmax和交叉熵损失怎么计算，二值交叉熵呢？<br>
• 九、如果softmax的e次方超过float的值了怎么办？<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>四、多任务学习各loss差异过大怎样处理？<br>
多任务学习中，如果各任务的损失差异过大，可以通过动态调整损失权重、使用任务特定的损失函数、改变模型<br>
架构或引入正则化等方法来处理。目标是平衡各任务的贡献，以便更好地训练模型。<br>
五、分类问题为什么用交叉熵损失函数不用均方误差（MSE）？<br>
交叉熵损失函数通常在分类问题中使用，而均方误差（MSE）损失函数通常用于回归问题。这是因为分类问题和<br>
回归问题具有不同的特点和需求。<br>
分类问题的目标是将输入样本分到不同的类别中，输出为类别的概率分布。交叉熵损失函数可以度量两个概率分<br>
布之间的差异，使得模型更好地拟合真实的类别分布。它对概率的细微差异更敏感，可以更好地区分不同的类<br>
别。此外，交叉熵损失函数在梯度计算时具有较好的数学性质，有助于更稳定地进行模型优化。<br>
相比之下，均方误差（MSE）损失函数更适用于回归问题，其中目标是预测连续数值而不是类别。MSE损失函<br>
数度量预测值与真实值之间的差异的平方，适用于连续数值的回归问题。在分类问题中使用MSE损失函数可能不<br>
太合适，因为它对概率的微小差异不够敏感，而且在分类问题中通常需要使用激活函数（如sigmoid或softmax）<br>
将输出映射到概率空间，使得MSE的数学性质不再适用。<br>
综上所述，交叉熵损失函数更适合分类问题，而MSE损失函数更适合回归问题。<br>
六、什么是信息增益？<br>
信息增益是在决策树算法中用于选择最佳特征的一种评价指标。在决策树的生成过程中，选择最佳特征来进行节<br>
点的分裂是关键步骤之一，信息增益可以帮助确定最佳特征。<br>
信息增益衡量了在特征已知的情况下，将样本集合划分成不同类别的纯度提升程度。它基于信息论的概念，使用<br>
熵来度量样本集合的不确定性。具体而言，信息增益是原始集合的熵与特定特征下的条件熵之间的差异。<br>
在决策树的生成过程中，选择具有最大信息增益的特征作为当前节点的分裂标准，可以将样本划分为更加纯净的<br>
子节点。信息增益越大，意味着使用该特征进行划分可以更好地减少样本集合的不确定性，提高分类的准确性。<br>
七、多分类的分类损失函数(Softmax)？<br>
多分类的分类损失函数采用Softmax交叉熵（Softmax Cross Entropy）损失函数。Softmax函数可以将输出值归<br>
一化为概率分布，用于多分类问题的输出层。Softmax交叉熵损失函数可以写成：<br>
注：其中，n是类别数，yi是第i类的真实标签，pi是第i类的预测概率。<br>
八、softmax和交叉熵损失怎么计算，二值交叉熵呢？<br>
softmax计算公式如下：<br>
• 交叉熵损失函数是二分类问题中最常用的损失函数，由于其定义出于信息学的角度，可以泛化到多分类问题<br>
中。<br>
• KL散度是一种用于衡量两个分布之间差异的指标，交叉熵损失函数是KL散度的一种特殊形式。在二分类问题<br>
中，交叉熵函数只有一项，而在多分类问题中有多项。<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page3_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/6-LLMs 损失函数篇_page3_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<p>多分类交叉熵：<br>
二分类交叉熵：<br>
九、如果softmax的e次方超过float的值了怎么办？<br>
将分子分母同时除以 x 中的最大值，可以解决。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:50</p>
        </div>
    </div>
</body>
</html>