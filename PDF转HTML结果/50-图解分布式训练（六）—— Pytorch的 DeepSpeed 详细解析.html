<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>50-图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>50-图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/50-图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/50-图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年12月24日 00:39<br>
• 图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析<br>
• 动机<br>
• 一、为什么需要 Deepspeed？<br>
• 二、DeepSpeed 基本概念 介绍一下？<br>
• 2.1 DeepSpeed 介绍<br>
• 2.2 DeepSpeed 基础的概念<br>
• 2.3 DeepSpeed 支持的功能<br>
• 三、DeepSpeed 通信策略 介绍一下？<br>
• 四、DeepSpeed 如何使用？<br>
• 4.1 DeepSpeed 安装<br>
• 4.2 DeepSpeed 使用<br>
• 五、DeepSpeed 全部代码<br>
• 六、优化器和调度器<br>
• 6.1 优化器<br>
• 6.2 调度器<br>
• 七、训练精度<br>
• 7.1 自动混合精度<br>
• 7.2 NCCL<br>
• 7.3 apex<br>
• 八、获取模型参数<br>
• 8.1 ZeRO-3 and Infinity Nuances<br>
• 填坑笔记<br>
• 1. ModuleNotFoundError: No module named 'torch._six<br>
• 2. 为什么单卡的情况，也可以使用deepspeed？<br>
• 3. 不同 ZeRO 如何配置<br>
• 3.1 ZeRO-2<br>
• 3.2 ZeRO-3<br>
• 3.3 ZeRO-stage-0<br>
• 3.4 ZeRO-stage-1<br>
• 4. ZeRO-3 会比 ZeRO-2 慢很多 如何优化？<br>
• 5. 如何选择不同的Zero stage和offload<br>
• 6. DeepSpeed 遇到问题，如何 确定 调参步骤？<br>
• 7. 如何估算需要的显存？<br>
• 8. 启动时，进程被杀死，并且没有打印出traceback<br>
• 9. loss是NaN<br>
• 10. 确保一致性<br>
• 11. 如何配置 配置ssh？<br>
• 12. 如何配置 安装pdsh？<br>
• 12. 如何配置 配置deepspeed文件？<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>动机<br>
最常见的深度学习框架应该是TensorFlow、Pytorch、Keras，但是这些框架在面向大规模模型的时<br>
候都不是很方便。<br>
比如Pytorch的分布式并行计算框架（Distributed Data Parallel，简称DDP），它也仅仅是能将数<br>
据并行，放到各个GPU的模型上进行训练。<br>
也就是说，DDP的应用场景在你的模型大小大于显卡显存大小时，它就很难继续使用了，除非你自<br>
己再将模型参数拆散分散到各个GPU上。<br>
今天要给大家介绍的DeepSpeed，它就能实现这个拆散功能，它通过将模型参数拆散分布到各个<br>
GPU上，以实现大型模型的计算，弥补了DDP的缺点，非常方便，这也就意味着我们能用更少的<br>
GPU训练更大的模型，而且不受限于显存。<br>
一、为什么需要 Deepspeed？<br>
大模型（LLM）在训练时往往需要大量内存来存储中间激活、权重等参数，百亿模型甚至无法在单<br>
个 GPU上进行训练，使得模型训练在某些情况下非常低效和不可能。这就需要进行多卡，或者多<br>
节点分布式训练。<br>
在大规模深度学习模型训练中有个主要范式：<br>
目前训练超大规模语言模型技术路线：GPU + PyTorch + Megatron-LM + DeepSpeed<br>
DeepSpeed是由Microsoft提供的分布式训练工具，旨在支持更大规模的模型和提供更多的优化策<br>
略和工具。与其他框架相比，DeepSpeed支持更大规模的模型和提供更多的优化策略和工具。其<br>
中，主要优势在于支持更大规模的模型、提供了更多的优化策略和工具（例如 ZeRO 和 Offload <br>
等）<br>
• 致谢<br>
• 数据并行<br>
• 模型并行<br>
1. 用 3D 并行化实现万亿参数模型训练。DeepSpeed 实现了三种并行方法的灵活组合：ZeRO 支<br>
持的数据并行，流水线并行和张量切片模型并行。3D 并行性适应了不同工作负载的需求，以支<br>
持具有万亿参数的超大型模型，同时实现了近乎完美的显存扩展性和吞吐量扩展效率。此外，<br>
其提高的通信效率使用户可以在网络带宽有限的常规群集上以 2-7 倍的速度训练有数十亿参数<br>
的模型。<br>
2. ZeRO-Offload 使 GPU 单卡能够训练 10 倍大的模型： 为了同时利用 CPU 和 GPU 内存来训练<br>
大型模型，我们扩展了 ZeRO-2。我们的用户在使用带有单张英伟达 V100 GPU 的机器时，可<br>
以在不耗尽显存的情况下运行多达 130 亿个参数的模型，模型规模扩展至现有方法的10倍，并<br>
保持有竞争力的吞吐量。此功能使数十亿参数的模型训练更加大众化，并为许多深度学习从业<br>
人员打开了一扇探索更大更好的模型的窗户。<br>
3. 通过 DeepSpeed Sparse Attention 用6倍速度执行10倍长的序列： DeepSpeed提供了稀疏 <br>
attention kernel ——一种工具性技术，可支持长序列的模型输入，包括文本输入，图像输入和<br>
语音输入。与经典的稠密 Transformer 相比，它支持的输入序列长一个数量级，并在保持相当<br>
的精度下获得最高 6 倍的执行速度提升。它还比最新的稀疏实现快 1.5–3 倍。此外，我们的稀<br>
疏 kernel 灵活支持稀疏格式，使用户能够通过自定义稀疏结构进行创新。<br>
4. 1 比特 Adam 减少 5 倍通信量： Adam 是一个在大规模深度学习模型训练场景下的有效的（也<br>
许是最广为应用的）优化器。然而，它与通信效率优化算法往往不兼容。因此，在跨设备进行<br>
分布式扩展时，通信开销可能成为瓶颈。我们推出了一种 1 比特 Adam 新算法，以及其高效实<br>
现。该算法最多可减少 5 倍通信量，同时实现了与Adam相似的收敛率。在通信受限的场景<br>
</p>

<h2>第 3 页</h2>

<p>二、DeepSpeed 基本概念 介绍一下？<br>
2.1 DeepSpeed 介绍<br>
2.2 DeepSpeed 基础的概念<br>
在分布式计算环境中，有几个非常基础的概念需要理解：<br>
2.3 DeepSpeed 支持的功能<br>
下，我们观察到分布式训练速度提升了 3.5 倍，这使得该算法可以扩展到不同类型的 GPU 群<br>
集和网络环境。<br>
• 在分布式计算环境中，需要理解几个非常基础的概念：节点编号、全局进程编号、局部进程编<br>
号、全局总进程数和主节点。其中，主节点负责协调所有其他节点和进程的工作，因此是整个<br>
系统的关键部分。<br>
• DeepSpeed 还提供了 mpi、gloo 和 nccl 等通信策略，可以根据具体情况进行选择和配置。在<br>
使用 DeepSpeed 进行分布式训练时，可以根据具体情况选择合适的通信库，例如在 CPU 集群<br>
上进行分布式训练，可以选择 mpi 和 gloo；如果是在 GPU 上进行分布式训练，可以选择 <br>
nccl。<br>
• ZeRO（Zero Redundancy Optimizer）是一种用于大规模训练优化的技术，主要是用来减少内<br>
存占用。ZeRO 将模型参数分成了三个部分：Optimizer States、Gradient 和 Model <br>
Parameter。在使用 ZeRO 进行分布式训练时，可以选择 ZeRO-Offload 和 ZeRO-Stage3 等不<br>
同的优化技术。<br>
• 混合精度训练是指在训练过程中同时使用FP16（半精度浮点数）和FP32（单精度浮点数）两<br>
种精度的技术。使用FP16可以大大减少内存占用，从而可以训练更大规模的模型。在使用混合<br>
精度训练时，需要使用一些技术来解决可能出现的梯度消失和模型不稳定的问题，例如动态精<br>
度缩放和混合精度优化器等。<br>
• 结合使用huggingface和deepspeed<br>
• 节点编号（node_rank:）：分配给系统中每个节点的唯一标识符，用于区分不同计算机之间的<br>
通信。<br>
• 全局进程编号（rank）：分配给整个系统中的每个进程的唯一标识符，用于区分不同进程之间<br>
的通信。<br>
• 局部进程编号（local_rank）：分配给单个节点内的每个进程的唯一标识符，用于区分同一节点<br>
内的不同进程之间的通信。<br>
• 全局总进程数（word_size）：在整个系统中运行的所有进程的总数，用于确定可以并行完成多<br>
少工作以及需要完成任务所需的资源数量。<br>
• 主节点（master_ip+master_port）：在分布式计算环境中，主节点负责协调所有其他节点和进<br>
程的工作，为了确定主节点，我们需要知道它的IP地址和端口号。主节点还负责监控系统状<br>
态、处理任务分配和结果汇总等任务，因此是整个系统的关键部分。<br>
• DeepSpeed目前支持的功能<br>
• Optimizer state partitioning (ZeRO stage 1)<br>
• Gradient partitioning (ZeRO stage 2)<br>
• Parameter partitioning (ZeRO stage 3)<br>
• Custom mixed precision training handling<br>
• A range of fast CUDA-extension-based optimizers<br>
• ZeRO-Offload to CPU and NVMe<br>
</p>

<h2>第 4 页</h2>

<p>三、DeepSpeed 通信策略 介绍一下？<br>
deepspeed 还提供了 mpi、gloo 和 nccl 等通信策略，可以根据具体情况进行选择和配置。<br>
在使用 DeepSpeed 进行分布式训练时，可以根据具体情况选择合适的通信库。通常情况下，如果<br>
是在 CPU 集群上进行分布式训练，可以选择 mpi 和 gloo；如果是在 GPU 上进行分布式训练，可<br>
以选择 nccl。<br>
四、DeepSpeed 如何使用？<br>
4.1 DeepSpeed 安装<br>
4.2 DeepSpeed 使用<br>
注：使用DeepSpeed其实和写一个pytorch模型只有部分区别，一开始的流程是一样的。<br>
• mpi 是一种跨节点通信库，常用于 CPU 集群上的分布式训练；<br>
• gloo 是一种高性能的分布式训练框架，支持 CPU 和 GPU 上的分布式训练；<br>
• nccl 是 NVIDIA 提供的 GPU 专用通信库，被广泛应用于 GPU 上的分布式训练。<br>
    export CUDA_LAUNCH_BLOCKING=1<br>
    $ pip install deepspeed==0.8.1<br>
    $ sudo apt-get update<br>
    $ sudo apt-get install openmpi-bin libopenmpi-dev<br>
    $ pip install mpi4py<br>
1. 导包<br>
    import json, time, random<br>
    import torch<br>
    from sklearn.metrics import classification_report<br>
    from torch.utils.data import DataLoader<br>
    from collections import Counter<br>
    from transformers import BertForMaskedLM, BertTokenizer, <br>
BertForSequenceClassification, BertConfig, AdamW<br>
    import torch.nn as nn<br>
    import numpy as np<br>
    # 导入 deepspeed 分布式训练包<br>
    import deepspeed<br>
    import torch.distributed as dist<br>
    # 定义 设置随机种子 <br>
    def set_seed(seed=123):<br>
        """<br>
        设置随机数种子，保证实验可重现<br>
        :param seed:<br>
        :return:<br>
        """<br>
</p>

<h2>第 5 页</h2>

<p>        random.seed(seed)<br>
        torch.manual_seed(seed)<br>
        np.random.seed(seed)<br>
        torch.cuda.manual_seed_all(seed)<br>
2. 定义 获取 和 加载 训练集 函数<br>
    def get_data():<br>
        with open("data/train.json", "r", encoding="utf-8") as fp:<br>
            data = fp.read()<br>
        data = json.loads(data)<br>
        return data<br>
    def load_data():<br>
        data = get_data()<br>
        return_data = []<br>
        # [(文本， 标签id)]<br>
        for d in data:<br>
            text = d[0]<br>
            label = d[1]<br>
            return_data.append(("".join(text.split(" ")).strip(), label))<br>
        return return_data<br>
3. 定义 训练集编码类<br>
class Collate:<br>
    def __init__(<br>
            self,<br>
            tokenizer,<br>
            max_seq_len,<br>
        ):<br>
        self.tokenizer = tokenizer<br>
        self.max_seq_len = max_seq_len<br>
    def collate_fn(self, batch):<br>
        input_ids_all = []<br>
        token_type_ids_all = []<br>
        attention_mask_all = []<br>
        label_all = []<br>
        for data in batch:<br>
            text = data[0]<br>
            label = data[1]<br>
            inputs = self.tokenizer.encode_plus(<br>
                text=text,<br>
                max_length=self.max_seq_len,<br>
                padding="max_length",<br>
                truncation="longest_first",<br>
                return_attention_mask=True,<br>
                return_token_type_ids=True<br>
</p>

<h2>第 6 页</h2>

<p>            )<br>
            input_ids = inputs["input_ids"]<br>
            token_type_ids = inputs["token_type_ids"]<br>
            attention_mask = inputs["attention_mask"]<br>
            input_ids_all.append(input_ids)<br>
            token_type_ids_all.append(token_type_ids)<br>
            attention_mask_all.append(attention_mask)<br>
            label_all.append(label)<br>
        input_ids_all = torch.tensor(input_ids_all, dtype=torch.long)<br>
        token_type_ids_all = torch.tensor(token_type_ids_all, dtype=torch.long)<br>
        attention_mask_all = torch.tensor(attention_mask_all, dtype=torch.long)<br>
        label_all = torch.tensor(label_all, dtype=torch.long)<br>
        return_data = {<br>
            "input_ids": input_ids_all,<br>
            "attention_mask": attention_mask_all,<br>
            "token_type_ids": token_type_ids_all,<br>
            "label": label_all<br>
        }<br>
        return return_data<br>
4. 定义 Trainer 训练类 【注：这里有部分改动】<br>
class Trainer:<br>
    def __init__(<br>
                self,<br>
                args,<br>
                config,<br>
                model_engine,<br>
                criterion,<br>
                optimizer<br>
            ):<br>
        self.args = args<br>
        self.config = config<br>
        self.model_engine = model_engine<br>
        self.criterion = criterion<br>
        self.optimizer = optimizer<br>
    def on_step(self, batch_data):<br>
        label = batch_data["label"].cuda()<br>
        input_ids = batch_data["input_ids"].cuda()<br>
        token_type_ids = batch_data["token_type_ids"].cuda()<br>
        attention_mask = batch_data["attention_mask"].cuda()<br>
        output = self.model_engine.forward(<br>
            input_ids=input_ids,<br>
            token_type_ids=token_type_ids,<br>
            attention_mask=attention_mask,<br>
            labels=label<br>
</p>

<h2>第 7 页</h2>

<p>        )<br>
        logits = output[1]<br>
        return logits, label<br>
    # loss 聚合 计算<br>
    def loss_reduce(self, loss):<br>
        rt = loss.clone()<br>
        dist.all_reduce(rt, op=dist.ReduceOp.SUM)<br>
        rt /= torch.cuda.device_count()<br>
        return rt<br>
    # output 聚合<br>
    def output_reduce(self, outputs, targets):<br>
        output_gather_list = [torch.zeros_like(outputs) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(output_gather_list, outputs)<br>
        outputs = torch.cat(output_gather_list, dim=0)<br>
        target_gather_list = [torch.zeros_like(targets) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(target_gather_list, targets)<br>
        targets = torch.cat(target_gather_list, dim=0)<br>
        return outputs, targets<br>
    def train(self, train_loader, dev_loader=None):<br>
        gloabl_step = 1<br>
        best_acc = 0.<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model_engine.train()<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                self.model_engine.backward(loss)<br>
                self.model_engine.step()<br>
                # loss 聚合 计算<br>
                loss = self.loss_reduce(loss)<br>
                if self.args.local_rank == 0:<br>
                    print("【train】 epoch：{}/{} step：{}/{} loss：{:.6f}".format(<br>
                        epoch, self.args.epochs, gloabl_step, self.args.total_step, <br>
loss<br>
                    ))<br>
                gloabl_step += 1<br>
                if self.args.dev:<br>
</p>

<h2>第 8 页</h2>

<p>                    if gloabl_step % self.args.eval_step == 0:<br>
                        loss, accuracy = self.dev(dev_loader)<br>
                        if self.args.local_rank == 0:<br>
                            print("【dev】 loss：{:.6f} accuracy：<br>
{:.4f}".format(loss, accuracy))<br>
                        if accuracy &gt; best_acc:<br>
                            best_acc = accuracy<br>
                            self.model_engine.save_checkpoint(self.args.ckpt_path, <br>
save_latest=True)<br>
                            if self.args.local_rank == 0:<br>
                                print("【best accuracy】 {:.4f}".format(best_acc))<br>
        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev:<br>
            self.model_engine.save_checkpoint(self.args.ckpt_path, <br>
save_latest=True)<br>
    def dev(self, dev_loader):<br>
        self.model_engine.eval()<br>
        correct_total = 0<br>
        num_total = 0<br>
        loss_total = 0.<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(dev_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                # loss 聚合 计算<br>
                loss = self.loss_reduce(loss)<br>
                # output 聚合<br>
                logits, label = self.output_reduce(logits, label)<br>
                loss_total += loss<br>
                logits = logits.detach().cpu().numpy()<br>
                label = label.view(-1).detach().cpu().numpy()<br>
                num_total += len(label)<br>
                preds = np.argmax(logits, axis=1).flatten()<br>
                correct_num = (preds == label).sum()<br>
                correct_total += correct_num<br>
        return loss_total, correct_total / num_total<br>
    def test(self, model, test_loader, labels):<br>
        self.model_engine = model<br>
        self.model_engine.eval()<br>
        preds = []<br>
</p>

<h2>第 9 页</h2>

<p>        trues = []<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(test_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                # output 聚合<br>
                logits, label = self.output_reduce(logits, label)<br>
                label = label.view(-1).detach().cpu().numpy().tolist()<br>
                logits = logits.detach().cpu().numpy()<br>
                pred = np.argmax(logits, axis=1).flatten().tolist()<br>
                trues.extend(label)<br>
                preds.extend(pred)<br>
        # print(trues, preds, labels)<br>
        print(np.array(trues).shape, np.array(preds).shape)<br>
        report = classification_report(trues, preds, target_names=labels)<br>
        return report<br>
5. Args 参数类定义<br>
class Args:<br>
    model_path = "/mnt/kaimo/data/pretrain/bert-base-chinese"<br>
    ckpt_path = "output/deepspeed/"<br>
    max_seq_len = 128<br>
    ratio = 0.92<br>
    epochs = 5<br>
    eval_step = 50<br>
    dev = False<br>
    local_rank = None<br>
6. 初始化DeepSpeed引擎<br>
deepspeed_config = {<br>
    "train_micro_batch_size_per_gpu": 32,<br>
    "gradient_accumulation_steps": 1,<br>
    "optimizer": {<br>
        "type": "AdamW",<br>
        "params": {<br>
            "lr": 3e-5<br>
        }<br>
    },<br>
    "fp16": {<br>
        "enabled": True<br>
    },<br>
    "zero_optimization": {<br>
        "stage": 3,                   # ZeRO第3阶段<br>
        "allgather_partitions": True,<br>
        "allgather_bucket_size": 2e8,<br>
        "overlap_comm": True,<br>
        "reduce_scatter": True,<br>
        "reduce_bucket_size": 2e8<br>
</p>

<h2>第 10 页</h2>

<p>注：需要注意的是在ZeRO第3阶段，模型被划分到不同的GPU了<br>
    },<br>
    "activation_checkpointing": {<br>
        "partition_activations": True,<br>
        "cpu_checkpointing": True,<br>
        "contiguous_memory_optimization": True<br>
    },<br>
    "wall_clock_breakdown": True,<br>
    "log_dist": False,<br>
}<br>
1. 主函数 main()<br>
def main():<br>
    # =======================================<br>
    # 定义相关参数<br>
    set_seed()<br>
    label2id = {<br>
        "其他": 0,<br>
        "喜好": 1,<br>
        "悲伤": 2,<br>
        "厌恶": 3,<br>
        "愤怒": 4,<br>
        "高兴": 5,<br>
    }<br>
    args = Args()<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
    # =======================================<br>
    # =======================================<br>
    # 加载数据集<br>
    data = load_data()<br>
    # 取1万条数据出来<br>
    data = data[:10000]<br>
    random.shuffle(data)<br>
    train_num = int(len(data) * args.ratio)<br>
    train_data = data[:train_num]<br>
    dev_data = data[train_num:]<br>
    collate = Collate(tokenizer, args.max_seq_len)<br>
    train_loader = DataLoader(<br>
        train_data,<br>
        batch_size=deepspeed_config["train_micro_batch_size_per_gpu"],<br>
        shuffle=True,<br>
        num_workers=2,<br>
        collate_fn=collate.collate_fn<br>
    )<br>
    total_step = len(train_loader) * args.epochs<br>
</p>

<h2>第 11 页</h2>

<p>    args.total_step = total_step<br>
    dev_loader = DataLoader(<br>
        dev_data,<br>
        batch_size=deepspeed_config["train_micro_batch_size_per_gpu"],<br>
        shuffle=False,<br>
        num_workers=2,<br>
        collate_fn=collate.collate_fn<br>
    )<br>
    test_loader = dev_loader<br>
    # =======================================<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
    criterion = torch.nn.CrossEntropyLoss()<br>
    '''注： 这里需要用 deepspeed 初始化 model 和 optimizer'''<br>
    model_engine, optimizer, _, _ = deepspeed.initialize(<br>
        config=deepspeed_config,<br>
        model=model,<br>
        model_parameters=model.parameters()<br>
    )<br>
    args.local_rank = model_engine.local_rank<br>
    # =======================================<br>
    # =======================================<br>
    # 定义训练器<br>
    trainer = Trainer(<br>
        args,<br>
        config,<br>
        model_engine,<br>
        criterion,<br>
        optimizer<br>
    )<br>
    # 训练和验证<br>
    trainer.train(train_loader, dev_loader)<br>
    # =======================================<br>
    # =======================================<br>
    # 测试<br>
    labels = list(label2id.keys())<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
</p>

<h2>第 12 页</h2>

<div class="image-container">
  <img src="images/50-图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析_page12_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>开始运行代码：<br>
测试的时候发现每块GPU对每批数据都进行计算了一次，这里可能需要做些修改，暂时还没找到<br>
相关的方法。<br>
在./output/deepspeed 生成 多GPU的模型：<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
    # 需要重新初始化引擎<br>
    model_engine, optimizer, _, _ = deepspeed.initialize(<br>
        config=deepspeed_config,<br>
        model=model,<br>
        model_parameters=model.parameters())<br>
    model_engine.load_checkpoint(args.ckpt_path, load_module_only=True)<br>
    report = trainer.test(model_engine, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
2. 运行代码<br>
    $ deepspeed test.py --deepspeed_config config.json<br>
[2023-06-28 00:58:12,759] [INFO] [engine.py:2824:_get_all_zero_checkpointe_dicts] <br>
successfully read 2 ZeRO state_dicts for rank 0<br>
[2023-06-28 00:58:13,005] [INFO] [engine.py:2774:_load_zero_checkpoint] lg 2 zero <br>
partition checkpoints for rank 1<br>
[2023-06-28 00:58:13,005] [INFO] [engine.py:2774:_load_zero_checkpoint] lg 2 zero <br>
partition checkpoints for rank 0<br>
(1600,) (1600,)<br>
              precision    recall  f1-score   support<br>
          其他       0.62      0.67      0.64       546<br>
          喜好       0.50      0.57      0.53       224<br>
          悲伤       0.49      0.39      0.44       228<br>
          厌恶       0.42      0.42      0.42       240<br>
          愤怒       0.58      0.48      0.53       124<br>
          高兴       0.64      0.62      0.63       238<br>
    accuracy                           0.56      1600<br>
   macro avg       0.54      0.53      0.53      1600<br>
weighted avg       0.55      0.56      0.55      1600<br>
(1600,) (1600,)<br>
1. 训练模型转化<br>
    $ ls output/deepspeed/global_step1440/<br>
    &gt;&gt;&gt;<br>
</p>

<h2>第 13 页</h2>

<p>需要利用./output/deepspeed下有一个zero_to_fp32.py文件，我们可以利用将多GPU的模型转换为<br>
完整的：<br>
五、DeepSpeed 全部代码<br>
    zero_pp_rank_0_mp_rank_00_model_states.pt<br>
    zero_pp_rank_0_mp_rank_00_optim_states.pt<br>
    zero_pp_rank_1_mp_rank_00_model_states.pt<br>
    zero_pp_rank_1_mp_rank_00_optim_states.pt<br>
    $ python zero_to_fp32.py pytorch-distributed/output/deepspeed/ <br>
./pytorch_model.bin<br>
import json<br>
import time<br>
import random<br>
import torch<br>
import deepspeed<br>
import torch.nn as nn<br>
import numpy as np<br>
import torch.distributed as dist<br>
from sklearn.metrics import classification_report<br>
from torch.utils.data import DataLoader<br>
from collections import Counter<br>
from transformers import BertForMaskedLM, BertTokenizer, <br>
BertForSequenceClassification, BertConfig, AdamW<br>
# 定义 设置随机种子 <br>
def set_seed(seed=123):<br>
    """<br>
    设置随机数种子，保证实验可重现<br>
    :param seed:<br>
    :return:<br>
    """<br>
    random.seed(seed)<br>
    torch.manual_seed(seed)<br>
    np.random.seed(seed)<br>
    torch.cuda.manual_seed_all(seed)<br>
def get_data():<br>
    with open("data/train.json", "r", encoding="utf-8") as fp:<br>
        data = fp.read()<br>
    data = json.loads(data)<br>
    return data<br>
</p>

<h2>第 14 页</h2>

<p>def load_data():<br>
    data = get_data()<br>
    return_data = []<br>
    # [(文本， 标签id)]<br>
    for d in data:<br>
        text = d[0]<br>
        label = d[1]<br>
        return_data.append(("".join(text.split(" ")).strip(), label))<br>
    return return_data<br>
class Collate:<br>
    def __init__(<br>
            self,<br>
            tokenizer,<br>
            max_seq_len,<br>
        ):<br>
        self.tokenizer = tokenizer<br>
        self.max_seq_len = max_seq_len<br>
    def collate_fn(self, batch):<br>
        input_ids_all = []<br>
        token_type_ids_all = []<br>
        attention_mask_all = []<br>
        label_all = []<br>
        for data in batch:<br>
            text = data[0]<br>
            label = data[1]<br>
            inputs = self.tokenizer.encode_plus(<br>
                text=text,<br>
                max_length=self.max_seq_len,<br>
                padding="max_length",<br>
                truncation="longest_first",<br>
                return_attention_mask=True,<br>
                return_token_type_ids=True<br>
            )<br>
            input_ids = inputs["input_ids"]<br>
            token_type_ids = inputs["token_type_ids"]<br>
            attention_mask = inputs["attention_mask"]<br>
            input_ids_all.append(input_ids)<br>
            token_type_ids_all.append(token_type_ids)<br>
            attention_mask_all.append(attention_mask)<br>
            label_all.append(label)<br>
        input_ids_all = torch.tensor(input_ids_all, dtype=torch.long)<br>
        token_type_ids_all = torch.tensor(token_type_ids_all, dtype=torch.long)<br>
        attention_mask_all = torch.tensor(attention_mask_all, dtype=torch.long)<br>
        label_all = torch.tensor(label_all, dtype=torch.long)<br>
</p>

<h2>第 15 页</h2>

<p>        return_data = {<br>
            "input_ids": input_ids_all,<br>
            "attention_mask": attention_mask_all,<br>
            "token_type_ids": token_type_ids_all,<br>
            "label": label_all<br>
        }<br>
        return return_data<br>
class Trainer:<br>
    def __init__(<br>
                self,<br>
                args,<br>
                config,<br>
                model_engine,<br>
                criterion,<br>
                optimizer<br>
            ):<br>
        self.args = args<br>
        self.config = config<br>
        self.model_engine = model_engine<br>
        self.criterion = criterion<br>
        self.optimizer = optimizer<br>
    def on_step(self, batch_data):<br>
        label = batch_data["label"].cuda()<br>
        input_ids = batch_data["input_ids"].cuda()<br>
        token_type_ids = batch_data["token_type_ids"].cuda()<br>
        attention_mask = batch_data["attention_mask"].cuda()<br>
        output = self.model_engine.forward(<br>
            input_ids=input_ids,<br>
            token_type_ids=token_type_ids,<br>
            attention_mask=attention_mask,<br>
            labels=label<br>
        )<br>
        logits = output[1]<br>
        return logits, label<br>
    # loss 聚合 计算<br>
    def loss_reduce(self, loss):<br>
        rt = loss.clone()<br>
        dist.all_reduce(rt, op=dist.ReduceOp.SUM)<br>
        rt /= torch.cuda.device_count()<br>
        return rt<br>
    # output 聚合<br>
    def output_reduce(self, outputs, targets):<br>
</p>

<h2>第 16 页</h2>

<p>        output_gather_list = [torch.zeros_like(outputs) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(output_gather_list, outputs)<br>
        outputs = torch.cat(output_gather_list, dim=0)<br>
        target_gather_list = [torch.zeros_like(targets) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(target_gather_list, targets)<br>
        targets = torch.cat(target_gather_list, dim=0)<br>
        return outputs, targets<br>
    def train(self, train_loader, dev_loader=None):<br>
        gloabl_step = 1<br>
        best_acc = 0.<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model_engine.train()<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                self.model_engine.backward(loss)<br>
                self.model_engine.step()<br>
                # loss 聚合 计算<br>
                loss = self.loss_reduce(loss)<br>
                if self.args.local_rank == 0:<br>
                    print("【train】 epoch：{}/{} step：{}/{} loss：{:.6f}".format(<br>
                        epoch, self.args.epochs, gloabl_step, self.args.total_step, <br>
loss<br>
                    ))<br>
                gloabl_step += 1<br>
                if self.args.dev:<br>
                    if gloabl_step % self.args.eval_step == 0:<br>
                        loss, accuracy = self.dev(dev_loader)<br>
                        if self.args.local_rank == 0:<br>
                            print("【dev】 loss：{:.6f} accuracy：<br>
{:.4f}".format(loss, accuracy))<br>
                        if accuracy &gt; best_acc:<br>
                            best_acc = accuracy<br>
                            self.model_engine.save_checkpoint(self.args.ckpt_path, <br>
save_latest=True)<br>
                            if self.args.local_rank == 0:<br>
                                print("【best accuracy】 {:.4f}".format(best_acc))<br>
</p>

<h2>第 17 页</h2>

<p>        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev:<br>
            self.model_engine.save_checkpoint(self.args.ckpt_path, <br>
save_latest=True)<br>
    def dev(self, dev_loader):<br>
        self.model_engine.eval()<br>
        correct_total = 0<br>
        num_total = 0<br>
        loss_total = 0.<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(dev_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                # loss 聚合 计算<br>
                loss = self.loss_reduce(loss)<br>
                # output 聚合<br>
                logits, label = self.output_reduce(logits, label)<br>
                loss_total += loss<br>
                logits = logits.detach().cpu().numpy()<br>
                label = label.view(-1).detach().cpu().numpy()<br>
                num_total += len(label)<br>
                preds = np.argmax(logits, axis=1).flatten()<br>
                correct_num = (preds == label).sum()<br>
                correct_total += correct_num<br>
        return loss_total, correct_total / num_total<br>
    def test(self, model, test_loader, labels):<br>
        self.model_engine = model<br>
        self.model_engine.eval()<br>
        preds = []<br>
        trues = []<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(test_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                # output 聚合<br>
                logits, label = self.output_reduce(logits, label)<br>
                label = label.view(-1).detach().cpu().numpy().tolist()<br>
                logits = logits.detach().cpu().numpy()<br>
                pred = np.argmax(logits, axis=1).flatten().tolist()<br>
                trues.extend(label)<br>
                preds.extend(pred)<br>
        # print(trues, preds, labels)<br>
        print(np.array(trues).shape, np.array(preds).shape)<br>
</p>

<h2>第 18 页</h2>

<p>        report = classification_report(trues, preds, target_names=labels)<br>
        return report<br>
class Args:<br>
    model_path = "/mnt/kaimo/data/pretrain/bert-base-chinese"<br>
    ckpt_path = "output/deepspeed/"<br>
    max_seq_len = 128<br>
    ratio = 0.92<br>
    epochs = 5<br>
    eval_step = 50<br>
    dev = False<br>
    local_rank = None<br>
deepspeed_config = {<br>
    "train_micro_batch_size_per_gpu": 32,<br>
    "gradient_accumulation_steps": 1,<br>
    "optimizer": {<br>
        "type": "AdamW",<br>
        "params": {<br>
            "lr": 3e-5<br>
        }<br>
    },<br>
    "fp16": {<br>
        "enabled": True<br>
    },<br>
    "zero_optimization": {<br>
        "stage": 3,<br>
        "allgather_partitions": True,<br>
        "allgather_bucket_size": 2e8,<br>
        "overlap_comm": True,<br>
        "reduce_scatter": True,<br>
        "reduce_bucket_size": 2e8<br>
    },<br>
    "activation_checkpointing": {<br>
        "partition_activations": True,<br>
        "cpu_checkpointing": True,<br>
        "contiguous_memory_optimization": True<br>
    },<br>
    "wall_clock_breakdown": True,<br>
    "log_dist": False,<br>
}<br>
def main():<br>
    # =======================================<br>
    # 定义相关参数<br>
    set_seed()<br>
    label2id = {<br>
</p>

<h2>第 19 页</h2>

<p>        "其他": 0,<br>
        "喜好": 1,<br>
        "悲伤": 2,<br>
        "厌恶": 3,<br>
        "愤怒": 4,<br>
        "高兴": 5,<br>
    }<br>
    args = Args()<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
    # =======================================<br>
    # =======================================<br>
    # 加载数据集<br>
    data = load_data()<br>
    # 取1万条数据出来<br>
    data = data[:10000]<br>
    random.shuffle(data)<br>
    train_num = int(len(data) * args.ratio)<br>
    train_data = data[:train_num]<br>
    dev_data = data[train_num:]<br>
    collate = Collate(tokenizer, args.max_seq_len)<br>
    train_loader = DataLoader(<br>
        train_data,<br>
        batch_size=deepspeed_config["train_micro_batch_size_per_gpu"],<br>
        shuffle=True,<br>
        num_workers=2,<br>
        collate_fn=collate.collate_fn<br>
    )<br>
    total_step = len(train_loader) * args.epochs<br>
    args.total_step = total_step<br>
    dev_loader = DataLoader(<br>
        dev_data,<br>
        batch_size=deepspeed_config["train_micro_batch_size_per_gpu"],<br>
        shuffle=False,<br>
        num_workers=2,<br>
        collate_fn=collate.collate_fn<br>
    )<br>
    test_loader = dev_loader<br>
    # =======================================<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
</p>

<h2>第 20 页</h2>

<p>    criterion = torch.nn.CrossEntropyLoss()<br>
    '''注： 这里需要用 deepspeed 初始化 model 和 optimizer'''<br>
    model_engine, optimizer, _, _ = deepspeed.initialize(<br>
        config=deepspeed_config,<br>
        model=model,<br>
        model_parameters=model.parameters()<br>
    )<br>
    args.local_rank = model_engine.local_rank<br>
    # =======================================<br>
    # =======================================<br>
    # 定义训练器<br>
    trainer = Trainer(<br>
        args,<br>
        config,<br>
        model_engine,<br>
        criterion,<br>
        optimizer<br>
    )<br>
    # 训练和验证<br>
    trainer.train(train_loader, dev_loader)<br>
    # =======================================<br>
    # =======================================<br>
    # 测试<br>
    labels = list(label2id.keys())<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
    # 需要重新初始化引擎<br>
    model_engine, optimizer, _, _ = deepspeed.initialize(<br>
        config=deepspeed_config,<br>
        model=model,<br>
        model_parameters=model.parameters())<br>
    model_engine.load_checkpoint(args.ckpt_path, load_module_only=True)<br>
    report = trainer.test(model_engine, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
if __name__ == '__main__':<br>
    main()<br>
</p>

<h2>第 21 页</h2>

<div class="image-container">
  <img src="images/50-图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析_page21_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>六、优化器和调度器<br>
当不使用offload_optimizer 时，可以按照下表，混合使用HF和DS的优化器和迭代器，除了HF <br>
Scheduler和DS Optimizer这一种情况。<br>
6.1 优化器<br>
6.2 调度器<br>
七、训练精度<br>
• 启用 offload_optimizer 时可以使用非 DeepSpeed 的优化器，只要它同时具有 CPU 和 GPU 的<br>
实现（LAMB 除外）。<br>
• DeepSpeed 的主要优化器是 Adam、AdamW、OneBitAdam 和 Lamb。 这些已通过 ZeRO 进<br>
行了彻底测试，建议使用。<br>
• 如果没有在配置文件中配置优化器参数，Trainer 将自动将其设置为 AdamW，并将使用命令行<br>
参数的默认值：--learning_rate、--adam_beta1、--adam_beta2、 --adam_epsilon 和 --<br>
weight_decay。<br>
• 与 AdamW 类似，可以配置其他官方支持的优化器。 请记住，它们可能具有不同的配置值。 <br>
例如 对于 Adam，需要将 weight_decay 设置为 0.01 左右。<br>
• 此外，offload在与 Deepspeed 的 CPU Adam 优化器一起使用时效果最佳。 如果想对offload使<br>
用不同的优化器，deepspeed==0.8.3 以后的版本，还需要添加：<br>
{<br>
    "zero_force_ds_cpu_optimizer": false<br>
}<br>
• DeepSpeed 支持 LRRangeTest、OneCycle、WarmupLR 和 WarmupDecayLR 学习率调度<br>
器。<br>
• Transformers和DeepSpeed中调度器的overlap<br>
    WarmupLR 使用 --lr_scheduler_type constant_with_warmup<br>
    WarmupDecayLR 使用 --lr_scheduler_type linear<br>
• 由于 fp16 混合精度大大减少了内存需求，并可以实现更快的速度，因此只有在在此训练模式下<br>
表现不佳时，才考虑不使用混合精度训练。 通常，当模型未在 fp16 混合精度中进行预训练<br>
时，会出现这种情况（例如，使用 bf16 预训练的模型）。 这样的模型可能会溢出，导致loss为<br>
NaN。 如果是这种情况，使用完整的 fp32 模式。<br>
• 如果是基于 Ampere 架构的 GPU，pytorch 1.7 及更高版本将自动切换为使用更高效的 tf32 格<br>
式进行某些操作，但结果仍将采用 fp32。<br>
• 使用 Trainer，可以使用 --tf32 启用它，或使用 --tf32 0 或 --no_tf32 禁用它。 PyTorch 默认值<br>
是使用tf32。<br>
</p>

<h2>第 22 页</h2>

<p>7.1 自动混合精度<br>
7.2 NCCL<br>
7.3 apex<br>
八、获取模型参数<br>
• fp16<br>
• 可以使用 pytorch-like AMP 方式或者 apex-like 方式<br>
• 使用 --fp16--fp16_backend amp 或 --fp16_full_eval 命令行参数时启用此模式<br>
• bf16<br>
• 使用--bf16 or --bf16_full_eval 命令行参数时启用此模式<br>
• 通讯会采用一种单独的数据类型<br>
• 默认情况下，半精度训练使用 fp16 作为reduction操作的默认值<br>
• 可以增加一个小的开销并确保reduction将使用 fp32 作为累积数据类型<br>
{<br>
    "communication_data_type": "fp32"<br>
}<br>
• Apex 是一个在 PyTorch 深度学习框架下用于加速训练和提高性能的库。Apex 提供了混合精度<br>
训练、分布式训练和内存优化等功能，帮助用户提高训练速度、扩展训练规模以及优化 GPU <br>
资源利用率。<br>
• 使用--fp16、 --fp16_backend apex、 --fp16_opt_level 01 命令行参数时启用此模式<br>
"amp": {<br>
     "enabled": "auto",<br>
     "opt_level": "auto"<br>
}<br>
• deepspeed会在优化器参数中存储模型的主参数，存储在global_step*/*optim_states.pt 文件<br>
中，数据类型为fp32。因此，想要从checkpoint中恢复训练，则保持默认即可<br>
• 如果模型是在ZeRO-2模式下保存的，模型参数会以fp16的形式存储在pytorch_model.bin中<br>
• 如果模型是在ZeRO-3模式下保存的，需要如下所示设置参数，否则pytorch_model.bin将不会<br>
被创建<br>
{<br>
  "zero_optimization": {<br>
         "stage3_gather_16bit_weights_on_model_save": true<br>
    }<br>
}<br>
• 在线fp32权重恢复（需要很多的RAM）略<br>
• 离线获取fp32权重<br>
    $ python zero_to_fp32.py . pytorch_model.bin<br>
</p>

<h2>第 23 页</h2>

<p>8.1 ZeRO-3 and Infinity Nuances<br>
填坑笔记<br>
1. ModuleNotFoundError: No module named 'torch._six<br>
2. 为什么单卡的情况，也可以使用deepspeed？<br>
3. 不同 ZeRO 如何配置<br>
3.1 ZeRO-2<br>
配置示例<br>
• 构造超大模型（略）<br>
• 搜集参数（略）<br>
• 报错内容<br>
    $ ModuleNotFoundError: No module named 'torch._six：找到报错的文件，<br>
• 解决方法<br>
    注释掉：from torch._six import string_classes<br>
    加入：<br>
    int_classes = int<br>
    string_classes = str<br>
    如果还报错：NameError: name 'inf' is not defined<br>
    找到文件中的那一行，<br>
    前面加入：<br>
    import math<br>
    inf = math.inf<br>
1. 使用ZeRO-offload，将部分数据offload到CPU，降低对显存的需求<br>
2. 提供了对显存的管理，减少显存中的碎片<br>
{<br>
    "fp16": {<br>
        "enabled": "auto",<br>
        "loss_scale": 0,<br>
        "loss_scale_window": 1000,<br>
        "initial_scale_power": 16,<br>
        "hysteresis": 2,<br>
        "min_loss_scale": 1<br>
    },<br>
    "optimizer": {<br>
        "type": "AdamW",<br>
        "params": {<br>
            "lr": "auto",<br>
</p>

<h2>第 24 页</h2>

<p>            "betas": "auto",<br>
            "eps": "auto",<br>
            "weight_decay": "auto"<br>
        }<br>
    },<br>
    "scheduler": {<br>
        "type": "WarmupLR",<br>
        "params": {<br>
            "warmup_min_lr": "auto",<br>
            "warmup_max_lr": "auto",<br>
            "warmup_num_steps": "auto"<br>
        }<br>
    },<br>
    "zero_optimization": {<br>
        "stage": 2,<br>
        "offload_optimizer": {<br>
            "device": "cpu",<br>
            "pin_memory": true<br>
        },<br>
        "allgather_partitions": true,<br>
        "allgather_bucket_size": 2e8,<br>
        "overlap_comm": true,<br>
        "reduce_scatter": true,<br>
        "reduce_bucket_size": 2e8,<br>
        "contiguous_gradients": true<br>
    },<br>
    "gradient_accumulation_steps": "auto",<br>
    "gradient_clipping": "auto",<br>
    "steps_per_print": 2000,<br>
    "train_batch_size": "auto",<br>
    "train_micro_batch_size_per_gpu": "auto",<br>
    "wall_clock_breakdown": false<br>
}<br>
• 重要参数介绍：<br>
• overlap_comm：控制是否使用通信与计算的重叠。当设置为True时，DeepSpeed将在<br>
梯度计算时尝试并行执行梯度通信。可以有效地减少通信时间，从而加速整个训练过<br>
程。<br>
• allgather_bucket_size：用于控制Allgather操作的分桶大小。Allgather操作是指在分<br>
布式训练中，每个进程收集其他所有进程的张量，并将这些张量按顺序拼接起来。通过<br>
将张量划分为较小的桶（buckets），可以在通信过程中更高效地传输数据。<br>
allgather_bucket_size值越大，每个桶的大小越大，通信操作可能会变得更快，但也需<br>
要更多的内存来存储中间结果。合适的桶大小要根据实际情况调整。<br>
• reduce_bucket_size：类似于allgather_bucket_size，用于控制Allreduce操作的分桶<br>
大小。Allreduce操作是将所有进程的某个张量进行规约（例如求和），并将结果广播回<br>
</p>

<h2>第 25 页</h2>

<p>3.2 ZeRO-3<br>
配置示例<br>
所有进程。通过将张量划分为较小的桶，可以更高效地传输数据。reduce_bucket_size<br>
值越大，每个桶的大小越大，通信操作可能会变得更快，但同时也需要更多的内存来存<br>
储中间结果。合适的桶大小需要根据实际情况进行调整。<br>
• overlap_comm使用的是allgather_bucket_size和reduce_bucket_size值的4.5倍。如<br>
果它们被设置为5e8，需要9GB显存（5e8 x 2Bytes x 2 x 4.5）。如果内存大小是8GB或<br>
更小，需要将这些参数减少到约2e8，从而避免OOM，这需要3.6GB显存。如果在大容<br>
量GPU上也出现OOM，也需要做同样的调整。<br>
• 在deepspeed==0.4.4中新增了round_robin_gradients选项，可以并行化CPU的<br>
offload。当梯度累积的步数增加，或者GPU数量增加时，会有更好的性能优势。<br>
{<br>
    "fp16": {<br>
        "enabled": "auto",<br>
        "loss_scale": 0,<br>
        "loss_scale_window": 1000,<br>
        "initial_scale_power": 16,<br>
        "hysteresis": 2,<br>
        "min_loss_scale": 1<br>
    },<br>
    "optimizer": {<br>
        "type": "AdamW",<br>
        "params": {<br>
            "lr": "auto",<br>
            "betas": "auto",<br>
            "eps": "auto",<br>
            "weight_decay": "auto"<br>
        }<br>
    },<br>
    "scheduler": {<br>
        "type": "WarmupLR",<br>
        "params": {<br>
            "warmup_min_lr": "auto",<br>
            "warmup_max_lr": "auto",<br>
            "warmup_num_steps": "auto"<br>
        }<br>
    },<br>
    "zero_optimization": {<br>
        "stage": 3,<br>
        "offload_optimizer": {<br>
            "device": "cpu",<br>
            "pin_memory": true<br>
</p>

<h2>第 26 页</h2>

<p>3.3 ZeRO-stage-0<br>
        },<br>
        "offload_param": {<br>
            "device": "cpu",<br>
            "pin_memory": true<br>
        },<br>
        "overlap_comm": true,<br>
        "contiguous_gradients": true,<br>
        "sub_group_size": 1e9,<br>
        "reduce_bucket_size": "auto",<br>
        "stage3_prefetch_bucket_size": "auto",<br>
        "stage3_param_persistence_threshold": "auto",<br>
        "stage3_max_live_parameters": 1e9,<br>
        "stage3_max_reuse_distance": 1e9,<br>
        "stage3_gather_16bit_weights_on_model_save": true<br>
    },<br>
    "gradient_accumulation_steps": "auto",<br>
    "gradient_clipping": "auto",<br>
    "steps_per_print": 2000,<br>
    "train_batch_size": "auto",<br>
    "train_micro_batch_size_per_gpu": "auto",<br>
    "wall_clock_breakdown": false<br>
}<br>
• 重要参数介绍：<br>
• stage3_max_live_parameters是保留在 GPU 上的完整参数数量的上限。<br>
• stage3_max_reuse_distance是指将来何时再次使用参数的指标，从而决定是丢弃参数<br>
还是保留参数。 如果一个参数在不久的将来要再次使用（小于 <br>
stage3_max_reuse_distance），可以保留以减少通信开销。 使用activation <br>
checkpointing时，这一点非常有用。<br>
• 如果遇到 OOM，可以减少 stage3_max_live_parameters 和 <br>
stage3_max_reuse_distance。 除非正在使用activation checkpointing，否则它们对性<br>
能的影响应该很小。 1e9 会消耗 ~2GB。 内存由 stage3_max_live_parameters 和 <br>
stage3_max_reuse_distance 共享，所以不是相加的，一共 2GB。<br>
• stage3_gather_16bit_weights_on_model_save 在保存模型时启用模型 fp16 权重合<br>
并。 对大型模型和多GPU，在内存和速度方面都是一项昂贵的操作。 如果打算恢复训<br>
练，目前需要使用它。 未来的更新将消除此限制。<br>
• sub_group_size 控制在optimizer steps中更新参数的粒度。 参数被分组到 <br>
sub_group_size 的桶中，每个桶一次更新一个。 当与 ZeRO-Infinity 中的 NVMe offload<br>
一起使用时，sub_group_size 控制模型状态在optimizer steps期间从 NVMe 移入和移出 <br>
CPU 内存的粒度。 防止超大模型耗尽 CPU 内存。不使用NVMe offload时，使其保持默<br>
认值。出现OOM时，减小sub_group_size。当优化器迭代很慢时，可以增大<br>
sub_group_size 。<br>
• ZeRO-3 中未使用 allgather_partitions、allgather_bucket_size 和 reduce_scatter <br>
配置参数<br>
</p>

<h2>第 27 页</h2>

<p>配置示例<br>
stage 0会禁用所有的分片，然后把DeepSpeed当作时DDP来使用。<br>
3.4 ZeRO-stage-1<br>
配置示例<br>
只对优化器参数进行分片，可以加速一丢丢<br>
4. ZeRO-3 会比 ZeRO-2 慢很多 如何优化？<br>
5. 如何选择不同的Zero stage和offload<br>
从左到右，越来越慢<br>
从左到右，所需GPU显存越来越少<br>
6. DeepSpeed 遇到问题，如何 确定 调参步骤？<br>
{<br>
    "zero_optimization": {<br>
        "stage": 0<br>
    }<br>
}<br>
{<br>
    "zero_optimization": {<br>
        "stage": 1<br>
    }<br>
}<br>
• ZeRO-Infinity 需要使用 ZeRO-3<br>
• ZeRO-3 会比 ZeRO-2 慢很多。使用以下策略，可以使得ZeRO-3 的速度更接近ZeRO-2<br>
• 将stage3_param_persistence_threshold参数设置的很大，比如6 * hidden_size * <br>
hidden_size<br>
• 将offload_params参数关闭（可以极大改善性能）<br>
    Stage 0 (DDP) &gt; Stage 1 &gt; Stage 2 &gt; Stage 2 + offload &gt; Stage 3 &gt; Stage 3 + <br>
offloads<br>
    Stage 0 (DDP) &lt; Stage 1 &lt; Stage 2 &lt; Stage 2 + offload &lt; Stage 3 &lt; Stage 3 + <br>
offloads<br>
1. 将batch_size设置为1，通过梯度累积实现任意的有效batch_size<br>
2. 如果OOM则，设置--gradient_checkpointing 1 (HF Trainer)，或者 <br>
model.gradient_checkpointing_enable()<br>
3. 如果OOM则，尝试ZeRO stage 2<br>
4. 如果OOM则，尝试ZeRO stage 2 + offload_optimizer<br>
5. 如果OOM则，尝试ZeRO stage 3<br>
6. 如果OOM则，尝试offload_param到CPU<br>
</p>

<h2>第 28 页</h2>

<p>7. 如何估算需要的显存？<br>
可以通过下面的代码，先估算不同配置需要的显存数量，从而决定开始尝试的ZeRO stage。<br>
8. 启动时，进程被杀死，并且没有打印出traceback<br>
9. loss是NaN<br>
10. 确保一致性<br>
7. 如果OOM则，尝试offload_optimizer到CPU<br>
8. 如果OOM则，尝试降低一些默认参数。比如使用generate时，减小beam search的搜索范围<br>
9. 如果OOM则，使用混合精度训练，在Ampere的GPU上使用bf16，在旧版本GPU上使用fp16<br>
10.如果仍然OOM，则使用ZeRO-Infinity ，使用offload_param和offload_optimizer到NVME<br>
11. 一旦使用batch_size=1时，没有导致OOM，测量此时的有效吞吐量，然后尽可能增大<br>
batch_size<br>
12.开始优化参数，可以关闭offload参数，或者降低ZeRO stage，然后调整batch_size，然后继续<br>
测量吞吐量，直到性能比较满意（调参可以增加66%的性能）<br>
    python -c 'from transformers import AutoModel; \<br>
    from deepspeed.runtime.zero.stage3 import <br>
estimate_zero3_model_states_mem_needs_all_live; \<br>
    model = AutoModel.from_pretrained("bigscience/T0_3B"); \<br>
    estimate_zero3_model_states_mem_needs_all_live(model, num_gpus_per_node=2, <br>
num_nodes=1)'<br>
    [...]<br>
    Estimated memory needed for params, optim states and gradients for a:<br>
    HW: Setup with 1 node, 2 GPUs per node.<br>
    SW: Model with 2783M total params, 65M largest layer params.<br>
    per CPU  |  per GPU |   Options<br>
    70.00GB |   0.25GB | offload_param=cpu , offload_optimizer=cpu , zero_init=1<br>
    70.00GB |   0.25GB | offload_param=cpu , offload_optimizer=cpu , zero_init=0<br>
    62.23GB |   2.84GB | offload_param=none, offload_optimizer=cpu , zero_init=1<br>
    62.23GB |   2.84GB | offload_param=none, offload_optimizer=cpu , zero_init=0<br>
    0.74GB |  23.58GB | offload_param=none, offload_optimizer=none, zero_init=1<br>
    31.11GB |  23.58GB | offload_param=none, offload_optimizer=none, zero_init=0<br>
• 问题描述：启动时，进程被杀死，并且没有打印出traceback<br>
• 问题定位：GPU显存不够<br>
• 解决方法：加卡<br>
• 问题描述：loss是NaN<br>
• 问题定位：训练时用的是bf16，使用时是fp16。常常发生于google在TPU上train的模型，如<br>
T5。此时需要使用fp32或者bf16。<br>
1. 代码路径 执行训练的代码、模型、数据集等相关文件、路径要一致（如果不一致，考虑建立软<br>
链接）<br>
</p>

<h2>第 29 页</h2>

<p>11. 如何配置 配置ssh？<br>
如果不需要输入密码且连成了，则配置成功（服务器需要使用相同的用户名）<br>
12. 如何配置 安装pdsh？<br>
pdsh是一个并行分布式运维工具，它的优点是只需要在一台机上运行脚本就可以，pdsh会自动帮<br>
你把命令和环境变量推送到其他节点上，然后汇总所有节点的日志到主节点。<br>
12. 如何配置 配置deepspeed文件？<br>
2. 环境配置 虚拟环境路径也要一样；<br>
3. conda各个节点安装路径也要一样（否则会报exits with return code = 127）；<br>
4. 各种安装的库版本要保持高度一致（否则会报exits with return code = -6）；<br>
5. 环境路径加载 主节点上默认虚拟环境路径系统加载可能不正确,python环境无法正常加载，所以<br>
需要在入口python文件里加入：（这个问题目前没有在部署中遇到，记录一下别人的坑）<br>
local_env = os.environ.copy()<br>
local_env["PATH"]="/home/<USER>/.conda/envs/pretrain6/bin:" + local_env["PATH"]<br>
os.environ.update(local_env)<br>
1. hosts中假如节点名称 同时在相关的机器上，vim /etc/hosts，输入：<br>
************ model1<br>
************ model2<br>
2. 生成sshkey<br>
ssh-keygen -t rsa<br>
3. 互相拷贝sshkey 自己的sshkey拷贝到对方以及自己的authorized_keys<br>
ssh-copy-id ccwork@************<br>
ssh-copy-id ccwork@************<br>
4. 测试一下, 比如：<br>
ssh model2<br>
sudo apt-get install pdsh<br>
export PDSH_RCMD_TYPE=ssh<br>
1. hostfile 在你的工程下<br>
vim hostfile<br>
2. 编辑 hostfile<br>
model1 slots=8<br>
model2 slots=8<br>
3. 运行<br>
deepspeed --hostfile=src2/hostfile --include="model2:1,2@model1:3" <br>
src2/train_bash.py  --stage sft  --blabla bla 除了头上这部分，其他都跟单机一样<br>
</p>

<h2>第 30 页</h2>

<div class="image-container">
  <img src="images/50-图解分布式训练（六）—— Pytorch的 DeepSpeed 详细解析_page30_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:47</p>
        </div>
    </div>
</body>
</html>