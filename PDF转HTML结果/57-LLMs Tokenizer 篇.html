<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>57-LLMs Tokenizer 篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>57-LLMs Tokenizer 篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/57-LLMs Tokenizer 篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/57-LLMs Tokenizer 篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/57-LLMs Tokenizer 篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>LLMs Tokenizer 篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:20<br>
Byte-Pair Encoding(BPE)篇<br>
1 Byte-Pair Encoding(BPE) 如何构建词典？<br>
注：GPT2、BART和LLaMA就采用了BPE。<br>
WordPiece 篇<br>
1 WordPiece 与 BPE 异同点是什么？<br>
本质上还是BPE的思想。与BPE最大区别在于:如何选择两个子词进行合并<br>
注：BERT采用了WordPiece。<br>
SentencePiece 篇<br>
简单介绍一下 SentencePiece 思路？<br>
把空格也当作一种特殊字符来处理，再用BPE或者来构造词汇表。<br>
注：ChatGLM、BLOOM、PaLM采用了SentencePiece。<br>
对比篇<br>
1 举例 介绍一下 不同 大模型LLMs 的分词方式？<br>
2 介绍一下 不同 大模型LLMs 的分词方式 的区别？<br>
1. 准备足够的训练语料;以及期望的词表大小；<br>
2. 将单词拆分为字符粒度(字粒度)，并在末尾添加后缀“”，统计单词频率<br>
3. 合并方式:统计每一个连续/相邻字节对的出现频率，将最高频的连续字节对合并为新的子词；<br>
4. 重复第3步，直到词表达到设定的词表大小;或下一个最高频字节对出现频率为1。<br>
• BPE是选择频次最大的相邻子词合并;<br>
• WordPiece算法选择 能够提升语言模型概率最大的相邻子词进行合并，来加入词表<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/57-LLMs Tokenizer 篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/57-LLMs Tokenizer 篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>知识星球<br>
1. LLaMA的词表是最小的，LLaMA在中英文上的平均token数都是最多的，这意味着LLaMA对中英文分词都会<br>
比较碎，比较细粒度。尤其在中文上平均token数高达1.45，这意味着LLaMA大概率会将中文字符切分为2个<br>
以上的token。<br>
2. Chinese LLaMA扩展词表后，中文平均token数显著降低，会将一个汉字或两个汉字切分为一个token，提高<br>
了中文编码效率。<br>
3. ChatGLM-6B是平衡中英文分词效果最好的tokenizer。由于词表比较大，中文处理时间也有增加<br>
4. BLOOM虽然是词表最大的，但由于是多语种的，在中英文上分词效率与ChatGLM-6B基本相当。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:49</p>
        </div>
    </div>
</body>
</html>