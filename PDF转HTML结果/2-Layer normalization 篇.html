<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2-Layer normalization 篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>2-Layer normalization 篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page1_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page1_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<p>Layer normalization 篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:37<br>
Layer normalization-方法篇<br>
一、Layer Norm 篇<br>
1.1 Layer Norm 的计算公式写一下？<br>
二、RMS Norm 篇 （均方根 Norm）<br>
2.1 RMS Norm 的计算公式写一下？<br>
2.2 RMS Norm 相比于 Layer Norm 有什么特点？<br>
RMS Norm 简化了 Layer Norm ，去除掉计算均值进行平移的部分。<br>
对比LN，RMS Norm的计算速度更快。效果基本相当，甚至略有提升。<br>
三、Deep Norm 篇<br>
3.1 Deep Norm 思路？<br>
Deep Norm方法在执行Layer Norm之前，up-scale了残差连接 (alpha&gt;1)；另外，在初始化阶段down-scale了模<br>
型参数(beta&lt;1)。<br>
3.2 写一下 Deep Norm 代码实现？<br>
Deep Norm 有什么优点？<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page2_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>Deep Norm可以缓解爆炸式模型更新的问题，把模型更新限制在常数，使得模型训练过程更稳定。<br>
Layer normalization-位置篇<br>
1 LN 在 LLMs 中的不同位置 有什么区别么？如果有，能介绍一下区别么？<br>
回答：有，LN 在 LLMs 位置有以下几种：<br>
1. Post LN：<br>
a. 位置：layer norm在残差链接之后<br>
b. 缺点：Post LN 在深层的梯度范式逐渐增大，导致使用post-LN的深层transformer容易出现训练不稳<br>
定的问题<br>
2. Pre-LN：<br>
a. 位置：layer norm在残差链接中<br>
b. 优点：相比于Post-LN，Pre LN 在深层的梯度范式近似相等，所以使用Pre-LN的深层transformer训<br>
练更稳定，可以缓解训练不稳定问题<br>
c. 缺点：相比于Post-LN，Pre-LN的模型效果略差<br>
3. Sandwich-LN：<br>
a. 位置：在pre-LN的基础上，额外插入了一个layer norm<br>
b. 优点：Cogview用来避免值爆炸的问题<br>
c. 缺点：训练不稳定，可能会导致训练崩溃。<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/2-Layer normalization 篇_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>Layer normalization 对比篇<br>
LLMs 各模型分别用了 哪种 Layer normalization？<br>
BLOOM在embedding层后添加layer normalization，有利于提升训练稳定性:但可能会带来很大的性能损失<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:50</p>
        </div>
    </div>
</body>
</html>