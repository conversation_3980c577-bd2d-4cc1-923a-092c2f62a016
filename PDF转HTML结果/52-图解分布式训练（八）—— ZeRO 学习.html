<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>52-图解分布式训练（八）—— ZeRO 学习</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>52-图解分布式训练（八）—— ZeRO 学习</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/52-图解分布式训练（八）—— ZeRO 学习_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/52-图解分布式训练（八）—— ZeRO 学习_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图解分布式训练（八）—— ZeRO 学习<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:03<br>
一、什么是 3D 并行？<br>
3D 并行可以让大型模型以非常有效的方式进行训练<br>
二、3D 并行 策略有哪些？<br>
2.1 DataParallel (DP)<br>
2.2 TensorParallel (TP)<br>
2.3 PipelineParallel (PP)<br>
三、为什么需要 ZeRO？<br>
虽然 DataParallel (DP) 因为简单易实现，所以目前应用相比于其他两种 广泛，但是 由于 DataParallel (DP) 需要 <br>
每张卡都存储一个模型，导致 显存大小 成为 制约模型规模 的 主要因素。<br>
• DataParallel (DP)<br>
• TensorParallel (TP)<br>
• PipelineParallel (PP)<br>
• 介绍：假设有N张卡，每张卡都保存一个模型，每一次迭代（iteration/step）都将batch数据分割成N个等大<br>
小的micro-batch，每张卡根据拿到的micro-batch数据独立计算梯度，然后调用AllReduce计算梯度均值，<br>
每张卡再独立进行参数更新。<br>
• 举例说明：<br>
    # 假设模型有三层：L0, L1, L2   <br>
    # 每层有两个神经元   # 两张卡<br>
    GPU0:    L0 | L1 | L2   ---|----|---   a0 | b0 | c0   a1 | b1 | c1<br>
    GPU1:   L0 | L1 | L2   ---|----|---   a0 | b0 | c0   a1 | b1 | c1  <br>
• 介绍：每个张量都被分成多个块，因此不是让整个张量驻留在单个 GPU 上，而是张量的每个分片都驻留在<br>
其指定的 GPU 上。在处理过程中，每个分片在不同的 GPU 上分别并行处理，最终结果在步骤结束时同步。<br>
这也被称作横向并行。<br>
• 举例说明：<br>
    # 假设模型有三层：L0, L1, L2   <br>
    # 每层有两个神经元   # 两张卡<br>
    GPU0:    L0 | L1 | L2   ---|----|---   a0 | b0 | c0   a1 | b1 | c1<br>
    GPU1:   L0 | L1 | L2   ---|----|---   a0 | b0 | c0   a1 | b1 | c1  <br>
• 介绍：模型在多个 GPU 上垂直（层级）拆分，因此只有模型的一个或多个层放置在单个 GPU 上。每个 <br>
GPU 并行处理管道的不同阶段，并处理一小部分批处理。<br>
• 举例说明：<br>
    # 假设模型有8层  <br>
    # 两张卡<br>
    |  L0 | L1 | L2 | L3 |  | L4 | L5 | L6 | L7 |  <br>
    ======================  =====================          <br>
            GPU0                 GPU1   <br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/52-图解分布式训练（八）—— ZeRO 学习_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/52-图解分布式训练（八）—— ZeRO 学习_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>既然 每张卡都存储一个模型 会 增加 模型训练过程中的显存占用，那么 是否可以 让 每行卡训练 1/N 的模型参<br>
数，然后 合并起来就是一个完整模型呢？ 这样，随着卡数的增加，每张卡 用于 模型训练的显存占用将减低，能<br>
够训练的模型也就越大。<br>
如今训练大模型离不开各种分布式并行策略，ZeRO系列技术就是一种显存优化的数据并行方案，旨在训练超大<br>
规模的语言模型。<br>
四、ZeRO 的 核心思想是什么？<br>
去除数据并行中的冗余参数，使每张卡只存储一部分模型状态，从而减少显存占用。<br>
五、ZeRO 显存如何分配？<br>
ZeRO将模型训练阶段中每张卡的显存内容分为两类：<br>
来看一个例子，GPT-2含有1.5B个参数，如果用fp16格式（混合精度），只需要3GB显存，但是模型状态实际上<br>
需要耗费24GB！所以模型状态就成了头号显存杀手，它也是ZeRO的重点优化对象。而其中优化器状态又是第一<br>
个要被优化的。<br>
六、ZeRO 优化策略是怎么样？<br>
针对模型状态的存储优化（去除冗余），ZeRO使用的方法是分片，即每张卡只存 1/N的模型状态量，这样系统<br>
内只维护一份模型状态。<br>
ZeRO 具有三个主要的优化阶段（ZeRO-1，ZeRO-2，ZeRO-3），它们对应于优化器状态（optimizer <br>
states）、梯度（gradients）和参数（parameters）的分片。累积启用时：<br>
注：图中Memory Consumption 第二列给出了一个示例： k=12,Φ=7.5B,Nd​=64 ，可以看到随着<br>
ZeRO 阶段深入，显存优化相当明显。<br>
• 模型状态：包括参数、梯度和优化器状态，其中优化器状态占比 75% 。<br>
• 剩余状态：除了模型状态之外的显存占用，包括激活值、各种临时缓冲区以及无法使用的显存碎片。<br>
• 优化器状态分区 (Pos​) – 内存减少 4 倍，通信量与数据并行性相同<br>
• 添加梯度分区 (Pos+g​) – 内存减少 8 倍，通信量与数据并行性相同<br>
• 添加参数分区 (Pos+g+p​) – 内存减少与数据并行度 Nd 成线性关系。例如，拆分为 64 个 GPU ( Nd =64) 内<br>
存将减少到 1/64 。GPU 通信量略有增加 50%。<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/52-图解分布式训练（八）—— ZeRO 学习_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/52-图解分布式训练（八）—— ZeRO 学习_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/52-图解分布式训练（八）—— ZeRO 学习_page3_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>一张卡训不了大模型，根因是显存不足，ZeRO-Offload则将训练阶段的某些模型状态下放（offload）到内存以<br>
及CPU计算，即显存不足，内存来补。相比于昂贵的显存，内存廉价多了。<br>
下图是某一层的一次迭代过程，使用了混合精度训练，前向计算（FWD）需要用到上一次的激活值<br>
（activation）和本层的参数（parameter），反向传播（BWD）也需要用到激活值和参数计算梯度，当我们用<br>
Adam优化器进行参数更新时，假设模型参数量是 M ，在混合精度训练的前提下，边的权重要么是2M<br>
（fp16），要么是4M（fp32）。为了不降低计算效率，将前两个节点放在GPU，后两个节点不但计算量小还需<br>
要和Adam状态打交道，所以放在CPU上，Adam状态自然也放在内存中，为了简化数据图，将前两个节点融合<br>
成一个节点FWD-BWD Super Node，将后两个节点融合成一个节点Update Super Node，沿着gradient 16和<br>
parameter 16把数据流图切分为两部分，分布对应GPU和CPU<br>
七、ZeRO Offload后的计算流程是怎么样？<br>
知识星球<br>
• 在GPU上面进行前向和后向计算，将梯度传给CPU。同时为了提高效率，可以将计算和通信并行起来，GPU<br>
在反向传播阶段，可以待梯度值填满bucket后，一边计算新的梯度一边将bucket传输给CPU，当反向传播结<br>
束，CPU基本上已经有最新的梯度值了；<br>
• cpu进行参数更新，再将更新后的参数传给GPU。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:50</p>
        </div>
    </div>
</body>
</html>