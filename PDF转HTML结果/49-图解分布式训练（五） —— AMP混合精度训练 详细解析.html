<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>49-图解分布式训练（五） —— AMP混合精度训练 详细解析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>49-图解分布式训练（五） —— AMP混合精度训练 详细解析</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/49-图解分布式训练（五） —— AMP混合精度训练 详细解析_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/49-图解分布式训练（五） —— AMP混合精度训练 详细解析_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图解分布式训练（五） —— AMP混合精度训练 详细解析<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 11:36<br>
为什么需要 AMP混合精度训练？<br>
PyTorch 1.6版本今天发布了，带来的最大更新就是自动混合精度。release说明的标题是：<br>
Stable release of automatic mixed precision (AMP).New Beta features include a TensorPipe backend for RPC, <br>
memory profiler, and several improvements to distributed training for both RPC and DDP.<br>
可见自动混合精度正是PyTorch 1.6的最大更新。这就带来了几个问题：<br>
一、什么是自动混合精度训练(AMP)<br>
我们知道神经网络框架的计算核心是Tensor，也就是那个从scaler -&gt; array -&gt; matrix -&gt; tensor 维度一路丰富过来<br>
的tensor。在PyTorch中，我们可以这样创建一个Tensor：<br>
可以看到默认创建的tensor都是FloatTensor类型。而在PyTorch中，一共有10种类型的tensor：<br>
由此可见，默认的Tensor是32-bit floating point，这就是32位浮点型精度的Tensor。<br>
自动混合精度的关键词有两个：自动、混合精度。这是由PyTorch 1.6的torch.cuda.amp模块带来的：<br>
混合精度预示着有不止一种精度的Tensor，那在PyTorch的AMP模块里是几种呢？2种：torch.FloatTensor和<br>
torch.HalfTensor；<br>
• 什么是自动混合精度训练？<br>
• 为什么需要自动混合精度？<br>
• 如何在PyTorch中使用自动混合精度？<br>
&gt;&gt;&gt; import torch<br>
&gt;&gt;&gt; gemfield = torch.zeros(70,30)<br>
&gt;&gt;&gt; gemfield.type()<br>
'torch.FloatTensor'<br>
&gt;&gt;&gt; syszux = torch.Tensor([1,2])<br>
&gt;&gt;&gt; syszux.type()<br>
'torch.FloatTensor'<br>
• torch.FloatTensor (32-bit floating point)<br>
• torch.DoubleTensor (64-bit floating point)<br>
• torch.HalfTensor (16-bit floating point 1)<br>
• torch.BFloat16Tensor (16-bit floating point 2)<br>
• torch.ByteTensor (8-bit integer (unsigned))<br>
• torch.CharTensor (8-bit integer (signed))<br>
• torch.ShortTensor (16-bit integer (signed))<br>
• torch.IntTensor (32-bit integer (signed))<br>
• torch.LongTensor (64-bit integer (signed))<br>
• torch.BoolTensor (Boolean)<br>
  from torch.cuda.amp import autocast as autocast<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>自动预示着Tensor的dtype类型会自动变化，也就是框架按需自动调整tensor的dtype（其实不是完全自动，有些<br>
地方还是需要手工干预）；<br>
torch.cuda.amp 的名字意味着这个功能只能在cuda上使用，事实上，这个功能正是NVIDIA的开发人员贡献到<br>
PyTorch项目中的。而只有支持Tensor core的CUDA硬件才能享受到AMP的好处（比如2080ti显卡）。Tensor <br>
Core是一种矩阵乘累加的计算单元，每个Tensor Core每个时钟执行64个浮点混合精度操作（FP16矩阵相乘和<br>
FP32累加），英伟达宣称使用Tensor Core进行矩阵运算可以轻易的提速，同时降低一半的显存访问和存储。<br>
因此，在PyTorch中，当我们提到自动混合精度训练，我们说的就是在NVIDIA的支持Tensor core的CUDA设备上<br>
使用torch.cuda.amp.autocast （以及torch.cuda.amp.GradScaler）来进行训练。咦？为什么还要有<br>
torch.cuda.amp.GradScaler？<br>
二、为什么需要自动混合精度？<br>
这个问题其实暗含着这样的意思：为什么需要自动混合精度，也就是torch.FloatTensor和torch.HalfTensor的混<br>
合，而不全是torch.FloatTensor？或者全是torch.HalfTensor？<br>
如果非要以这种方式问，那么答案只能是，在某些上下文中torch.FloatTensor有优势，在某些上下文中<br>
torch.HalfTensor有优势呗。答案进一步可以转化为，相比于之前的默认的torch.FloatTensor，torch.HalfTensor<br>
有时具有优势，有时劣势不可忽视。<br>
torch.HalfTensor的优势就是存储小、计算快、更好的利用CUDA设备的Tensor Core。因此训练的时候可以减少<br>
显存的占用（可以增加batchsize了），同时训练速度更快；<br>
torch.HalfTensor的劣势就是：数值范围小（更容易Overflow / Underflow）、舍入误差（Rounding Error，导致<br>
一些微小的梯度信息达不到16bit精度的最低分辨率，从而丢失）。<br>
可见，当有优势的时候就用torch.HalfTensor，而为了消除torch.HalfTensor的劣势，我们带来了两种解决方案：<br>
1. 梯度scale，这正是上一小节中提到的torch.cuda.amp.GradScaler，通过放大loss的值来防止梯度的<br>
underflow（这只是BP的时候传递梯度信息使用，真正更新权重的时候还是要把放大的梯度再unscale回<br>
去）；<br>
2. 回落到torch.FloatTensor，这就是混合一词的由来。那怎么知道什么时候用torch.FloatTensor，什么时候用半<br>
精度浮点型呢？这是PyTorch框架决定的，在PyTorch 1.6的AMP上下文中，如下操作中tensor会被自动转化<br>
为半精度浮点型的torch.HalfTensor：<br>
  __matmul__<br>
  addbmm<br>
  addmm<br>
  addmv<br>
  addr<br>
  baddbmm<br>
  bmm<br>
  chain_matmul<br>
  conv1d<br>
  conv2d<br>
  conv3d<br>
  conv_transpose1d<br>
  conv_transpose2d<br>
  conv_transpose3d<br>
  linear<br>
  matmul<br>
  mm<br>
  mv<br>
  prelu<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/49-图解分布式训练（五） —— AMP混合精度训练 详细解析_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/49-图解分布式训练（五） —— AMP混合精度训练 详细解析_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>三、混合精度训练的优点是什么？<br>
四、混合精度训练的缺点是什么？<br>
五、混合精度训练的关键技术是什么？<br>
六、介绍一下 混合精度训练 动态损失缩放？<br>
七、如何在PyTorch中使用自动混合精度？<br>
答案就是autocast + GradScaler。<br>
正如前文所说，需要使用torch.cuda.amp模块中的autocast 类。使用也是非常简单的：<br>
• 减少显存<br>
• 占用加快训练速度:通信量<br>
• 减半;计算性能翻倍。<br>
• 数据溢出<br>
• 舍入误差<br>
• float32主权重备份<br>
• 动态损失缩放<br>
• 介绍：即损失标度。这会将梯度也放大1024倍，大大降只需要将损失乘以某个大数字 (如1024)，低了梯度发<br>
生下溢的几率。计算出梯度后，只需将其除以1024就可以得到准确值。<br>
• 动态选择损失标度 介绍：发生溢出，跳过优化器更新，损失标度减半;连续N个steps没有发生溢出，损失标<br>
度翻倍<br>
1. autocast<br>
from torch.cuda.amp import autocast as autocast<br>
# 创建model，默认是torch.FloatTensor<br>
model = Net().cuda()<br>
optimizer = optim.SGD(model.parameters(), ...)<br>
</p>

<h2>第 4 页</h2>

<p>可以使用autocast的context managers语义（如上所示），也可以使用decorators语义。 当进入autocast的上下<br>
文后，上面列出来的那些CUDA ops 会把tensor的dtype转换为半精度浮点型，从而在不损失训练精度的情况下加<br>
快运算。刚进入autocast的上下文时，tensor可以是任何类型，你不要在model或者input上手工调用.half() ，框架<br>
会自动做，这也是自动混合精度中“自动”一词的由来。<br>
另外一点就是，autocast上下文应该只包含网络的前向过程（包括loss的计算），而不要包含反向传播，因为BP<br>
的op会使用和前向op相同的类型。<br>
还有的时候呀，你的代码在autocast上下文中会报如下的错误：<br>
对于RuntimeError: expected scalar type float but found c10::Half，这估计是个bug。你可以在tensor上手工调<br>
用.float()来让type匹配。<br>
但是别忘了前面提到的梯度scaler模块呀，需要在训练最开始之前实例化一个GradScaler对象。因此PyTorch中<br>
经典的AMP使用方式如下：<br>
for input, target in data:<br>
    optimizer.zero_grad()<br>
    # 前向过程(model + loss)开启 autocast<br>
    with autocast():<br>
        output = model(input)<br>
        loss = loss_fn(output, target)<br>
    # 反向传播在autocast上下文之外<br>
    loss.backward()<br>
    optimizer.step()<br>
Traceback (most recent call last):<br>
......<br>
  File "/opt/conda/lib/python3.7/site-packages/torch/nn/modules/module.py", line <br>
722, in _call_impl<br>
    result = self.forward(*input, **kwargs)<br>
......<br>
RuntimeError: expected scalar type float but found c10::Half<br>
1. GradScaler<br>
from torch.cuda.amp import autocast as autocast<br>
# 创建model，默认是torch.FloatTensor<br>
model = Net().cuda()<br>
optimizer = optim.SGD(model.parameters(), ...)<br>
# 在训练最开始之前实例化一个GradScaler对象<br>
scaler = GradScaler()<br>
for epoch in epochs:<br>
    for input, target in data:<br>
        optimizer.zero_grad()<br>
        # 前向过程(model + loss)开启 autocast<br>
        with autocast():<br>
</p>

<h2>第 5 页</h2>

<p>scaler的大小在每次迭代中动态的估计，为了尽可能的减少梯度underflow，scaler应该更大；但是如果太大的<br>
话，半精度浮点型的tensor又容易overflow（变成inf或者NaN）。所以动态估计的原理就是在不出现inf或者NaN<br>
梯度值的情况下尽可能的增大scaler的值——在每次scaler.step(optimizer)中，都会检查是否又inf或NaN的梯度出<br>
现：<br>
八、如何使用 AMP混合精度训练 ？<br>
8.1 AMP混合精度训练 代码<br>
            output = model(input)<br>
            loss = loss_fn(output, target)<br>
        # Scales loss. 为了梯度放大.<br>
        scaler.scale(loss).backward()<br>
        # scaler.step() 首先把梯度的值unscale回来.<br>
        # 如果梯度的值不是 infs 或者 NaNs, 那么调用optimizer.step()来更新权重,<br>
        # 否则，忽略step调用，从而保证权重不更新（不被破坏）<br>
        scaler.step(optimizer)<br>
        # 准备着，看是否要增大scaler<br>
        scaler.update()<br>
• 如果出现了inf或者NaN，scaler.step(optimizer)会忽略此次的权重更新（optimizer.step() )，并且将scaler的<br>
大小缩小（乘上backoff_factor）；<br>
• 如果没有出现inf或者NaN，那么权重正常更新，并且当连续多次（growth_interval指定）没有出现inf或者<br>
NaN，则scaler.update()会将scaler的大小增加（乘上growth_factor）。<br>
1. Trainer 训练类<br>
  class Trainer:<br>
    ...<br>
     def train(self, train_loader, dev_loader=None, train_sampler=None):<br>
        ...<br>
        # 设置 AMP混合精度训练<br>
        if self.args.use_amp:<br>
            scaler = torch.cuda.amp.GradScaler()<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            train_sampler.set_epoch(epoch)<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model.train()<br>
                # 使用 AMP混合精度训练 <br>
                if self.args.use_amp:<br>
                    with torch.cuda.amp.autocast():<br>
                        logits, label = self.on_step(batch_data)<br>
                        loss = self.criterion(logits, label)<br>
                        torch.distributed.barrier()<br>
                        scaler.scale(loss).backward()<br>
                        scaler.step(self.optimizer)<br>
                        scaler.update()<br>
</p>

<h2>第 6 页</h2>

<p>                else:<br>
                    logits, label = self.on_step(batch_data)<br>
                    loss = self.criterion(logits, label)<br>
                    torch.distributed.barrier()<br>
                    loss.backward()<br>
                    self.optimizer.step()<br>
                ...<br>
        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev and self.args.local_rank == 0:<br>
            torch.save(self.model.state_dict(), self.args.ckpt_path)<br>
2. Args 参数类<br>
class Args:<br>
    ...<br>
    local_rank = None<br>
    local_world_size = None<br>
    device_ids = None<br>
    rank = None<br>
    dev = False<br>
    use_amp = True<br>
3. main_worker 主函数<br>
def main_worker(local_rank, local_world_size):<br>
    # =======================================<br>
    # 设置参数<br>
    ...<br>
    dist.init_process_group(backend="nccl", init_method="tcp://localhost:12345", <br>
world_size=local_world_size, rank=local_rank)<br>
    n = torch.cuda.device_count() // local_world_size<br>
    device_ids = [local_rank]<br>
    print(<br>
        f"[{os.getpid()}] rank = {local_rank}, "<br>
        + f"world_size = {local_world_size}, n = {n}, device_ids = {device_ids} <br>
\n", end=''<br>
    )<br>
    torch.cuda.set_device(local_rank)<br>
    args = Args()<br>
    args.local_world_size = local_world_size<br>
    args.local_rank = local_rank<br>
    args.device_ids = device_ids<br>
    args.rank = local_rank<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
</p>

<h2>第 7 页</h2>

<p>8.2 AMP混合精度训练 完整代码<br>
    ...<br>
    # 第三步：封装模型<br>
    model.cuda()<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    ...<br>
    model.cuda(args.local_rank)<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    ...<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
    # =======================================<br>
    dist.destroy_process_group()<br>
    # =======================================<br>
import os<br>
import time<br>
import json<br>
import random<br>
import torch<br>
import torch.nn as nn<br>
import numpy as np<br>
import torch.distributed as dist<br>
import torch.multiprocessing as mp<br>
from collections import Counter<br>
from tqdm import tqdm<br>
from sklearn.metrics import classification_report<br>
from torch.utils.data import DataLoader, Dataset<br>
from transformers import BertForMaskedLM, BertTokenizer, <br>
BertForSequenceClassification, BertConfig, AdamW<br>
def set_seed(seed=123):<br>
    """<br>
    设置随机数种子，保证实验可重现<br>
    :param seed:<br>
    :return:<br>
    """<br>
    random.seed(seed)<br>
    torch.manual_seed(seed)<br>
    np.random.seed(seed)<br>
</p>

<h2>第 8 页</h2>

<p>    torch.cuda.manual_seed_all(seed)<br>
def get_data():<br>
    with open("data/train.json", "r", encoding="utf-8") as fp:<br>
        data = fp.read()<br>
    data = json.loads(data)<br>
    return data<br>
def load_data():<br>
    data = get_data()<br>
    return_data = []<br>
    # [(文本， 标签id)]<br>
    for d in data:<br>
        text = d[0]<br>
        label = d[1]<br>
        return_data.append(("".join(text.split(" ")).strip(), label))<br>
    return return_data<br>
class ClsDataset(Dataset):<br>
    def __init__(self, data):<br>
        self.data = data<br>
    def __len__(self):<br>
        return len(self.data)<br>
    def __getitem__(self, index):<br>
        return self.data[index]<br>
class Collate:<br>
    def __init__(self,<br>
                 tokenizer,<br>
                 max_seq_len,<br>
                 ):<br>
        self.tokenizer = tokenizer<br>
        self.max_seq_len = max_seq_len<br>
    def collate_fn(self, batch):<br>
        input_ids_all = []<br>
        token_type_ids_all = []<br>
        attention_mask_all = []<br>
        label_all = []<br>
        for data in batch:<br>
            text = data[0]<br>
            label = data[1]<br>
            inputs = self.tokenizer.encode_plus(text=text,<br>
                                                max_length=self.max_seq_len,<br>
                                                padding="max_length",<br>
</p>

<h2>第 9 页</h2>

<p>                                                truncation="longest_first",<br>
                                                return_attention_mask=True,<br>
                                                return_token_type_ids=True)<br>
            input_ids = inputs["input_ids"]<br>
            token_type_ids = inputs["token_type_ids"]<br>
            attention_mask = inputs["attention_mask"]<br>
            input_ids_all.append(input_ids)<br>
            token_type_ids_all.append(token_type_ids)<br>
            attention_mask_all.append(attention_mask)<br>
            label_all.append(label)<br>
        input_ids_all = torch.tensor(input_ids_all, dtype=torch.long)<br>
        token_type_ids_all = torch.tensor(token_type_ids_all, dtype=torch.long)<br>
        attention_mask_all = torch.tensor(attention_mask_all, dtype=torch.long)<br>
        label_all = torch.tensor(label_all, dtype=torch.long)<br>
        return_data = {<br>
            "input_ids": input_ids_all,<br>
            "attention_mask": attention_mask_all,<br>
            "token_type_ids": token_type_ids_all,<br>
            "label": label_all<br>
        }<br>
        return return_data<br>
def build_optimizer(model, args):<br>
    no_decay = ['bias', 'LayerNorm.weight']<br>
    optimizer_grouped_parameters = [<br>
        {'params': [p for n, p in model.named_parameters() if not any(nd in n for <br>
nd in no_decay)],<br>
         'weight_decay': args.weight_decay},<br>
        {'params': [p for n, p in model.named_parameters() if any(nd in n for nd in <br>
no_decay)],<br>
         'weight_decay': 0.0}<br>
    ]<br>
    # optimizer = AdamW(model.parameters(), lr=learning_rate)<br>
    optimizer = AdamW(optimizer_grouped_parameters, lr=args.learning_rate)<br>
    return optimizer<br>
class Trainer:<br>
    def __init__(self,<br>
                 args,<br>
                 config,<br>
                 model,<br>
                 criterion,<br>
                 optimizer):<br>
        self.args = args<br>
        self.config = config,<br>
</p>

<h2>第 10 页</h2>

<p>        self.model = model<br>
        self.criterion = criterion<br>
        self.optimizer = optimizer<br>
    def on_step(self, batch_data):<br>
        label = batch_data["label"].cuda()<br>
        input_ids = batch_data["input_ids"].cuda()<br>
        token_type_ids = batch_data["token_type_ids"].cuda()<br>
        attention_mask = batch_data["attention_mask"].cuda()<br>
        output = self.model(input_ids=input_ids,<br>
                            token_type_ids=token_type_ids,<br>
                            attention_mask=attention_mask,<br>
                            labels=label)<br>
        logits = output[1]<br>
        return logits, label<br>
    def loss_reduce(self, loss):<br>
        rt = loss.clone()<br>
        dist.all_reduce(rt, op=dist.ReduceOp.SUM)<br>
        rt /= self.args.local_world_size<br>
        return rt<br>
    def output_reduce(self, outputs, targets):<br>
        output_gather_list = [torch.zeros_like(outputs) for _ in <br>
range(self.args.local_world_size)]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(output_gather_list, outputs)<br>
        outputs = torch.cat(output_gather_list, dim=0)<br>
        target_gather_list = [torch.zeros_like(targets) for _ in <br>
range(self.args.local_world_size)]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(target_gather_list, targets)<br>
        targets = torch.cat(target_gather_list, dim=0)<br>
        return outputs, targets<br>
    def train(self, train_loader, dev_loader=None, train_sampler=None):<br>
        gloabl_step = 1<br>
        best_acc = 0.<br>
        if self.args.use_amp:<br>
            scaler = torch.cuda.amp.GradScaler()<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            train_sampler.set_epoch(epoch)<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model.train()<br>
</p>

<h2>第 11 页</h2>

<p>                if self.args.use_amp:<br>
                    with torch.cuda.amp.autocast():<br>
                        logits, label = self.on_step(batch_data)<br>
                        loss = self.criterion(logits, label)<br>
                        torch.distributed.barrier()<br>
                        scaler.scale(loss).backward()<br>
                        scaler.step(self.optimizer)<br>
                        scaler.update()<br>
                else:<br>
                    logits, label = self.on_step(batch_data)<br>
                    loss = self.criterion(logits, label)<br>
                    torch.distributed.barrier()<br>
                    loss.backward()<br>
                    self.optimizer.step()<br>
                if self.args.local_rank == 0:<br>
                    print("【train】 epoch：{}/{} step：{}/{} loss：{:.6f}".format(<br>
                        epoch, self.args.epochs, gloabl_step, self.args.total_step, <br>
loss<br>
                    ))<br>
                gloabl_step += 1<br>
                if self.args.dev:<br>
                    if gloabl_step % self.args.eval_step == 0:<br>
                        loss, accuracy = self.dev(dev_loader)<br>
                        if self.args.local_rank == 0:<br>
                            print("【dev】 loss：{:.6f} accuracy：<br>
{:.4f}".format(loss, accuracy))<br>
                            if accuracy &gt; best_acc:<br>
                                best_acc = accuracy<br>
                                print("【best accuracy】 {:.4f}".format(best_acc))<br>
                                torch.save(self.model.state_dict(), <br>
self.args.ckpt_path)<br>
        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev and self.args.local_rank == 0:<br>
            torch.save(self.model.state_dict(), self.args.ckpt_path)<br>
    def dev(self, dev_loader):<br>
        self.model.eval()<br>
        correct_total = 0<br>
        num_total = 0<br>
        loss_total = 0.<br>
        with torch.no_grad():<br>
            for step, batch_data in tqdm(enumerate(dev_loader)):<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                torch.distributed.barrier()<br>
</p>

<h2>第 12 页</h2>

<p>                loss = self.loss_reduce(loss)<br>
                loss_total += loss<br>
                logits, label = self.output_reduce(logits, label)<br>
                logits = logits.detach().cpu().numpy()<br>
                label = label.view(-1).detach().cpu().numpy()<br>
                num_total += len(label)<br>
                preds = np.argmax(logits, axis=1).flatten()<br>
                correct_num = (preds == label).sum()<br>
                correct_total += correct_num<br>
        return loss_total, correct_total / num_total<br>
    def test(self, model, test_loader, labels):<br>
        self.model = model<br>
        self.model.eval()<br>
        preds = []<br>
        trues = []<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(test_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                torch.distributed.barrier()<br>
                logits, label = self.output_reduce(logits, label)<br>
                label = label.view(-1).detach().cpu().numpy().tolist()<br>
                logits = logits.detach().cpu().numpy()<br>
                pred = np.argmax(logits, axis=1).flatten().tolist()<br>
                trues.extend(label)<br>
                preds.extend(pred)<br>
        report = classification_report(trues, preds, target_names=labels)<br>
        return report<br>
class Args:<br>
    model_path = "/mnt/kaimo/data/pretrain/bert-base-chinese"<br>
    ckpt_path = "output/multi-gpu-distributed-mp-amp-cls.pt"<br>
    max_seq_len = 128<br>
    ratio = 0.92<br>
    device = torch.device("cuda" if torch.cuda.is_available else "cpu")<br>
    train_batch_size = 32<br>
    dev_batch_size = 32<br>
    weight_decay = 0.01<br>
    epochs = 1<br>
    learning_rate = 3e-5<br>
    eval_step = 50<br>
    local_rank = None<br>
    local_world_size = None<br>
    device_ids = None<br>
    rank = None<br>
    dev = False<br>
</p>

<h2>第 13 页</h2>

<p>    use_amp = True<br>
def main_worker(local_rank, local_world_size):<br>
    # =======================================<br>
    # 设置参数<br>
    set_seed()<br>
    label2id = {<br>
        "其他": 0,<br>
        "喜好": 1,<br>
        "悲伤": 2,<br>
        "厌恶": 3,<br>
        "愤怒": 4,<br>
        "高兴": 5,<br>
    }<br>
    dist.init_process_group(backend="nccl", init_method="tcp://localhost:12345", <br>
world_size=local_world_size,<br>
                            rank=local_rank)<br>
    n = torch.cuda.device_count() // local_world_size<br>
    device_ids = [local_rank]<br>
    print(<br>
        f"[{os.getpid()}] rank = {local_rank}, "<br>
        + f"world_size = {local_world_size}, n = {n}, device_ids = {device_ids} <br>
\n", end=''<br>
    )<br>
    torch.cuda.set_device(local_rank)<br>
    args = Args()<br>
    args.local_world_size = local_world_size<br>
    args.local_rank = local_rank<br>
    args.device_ids = device_ids<br>
    args.rank = local_rank<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
    # =======================================<br>
    # =======================================<br>
    # 加载数据集<br>
    data = load_data()<br>
    # 取1万条数据出来<br>
    data = data[:10000]<br>
    random.shuffle(data)<br>
    train_num = int(len(data) * args.ratio)<br>
    train_data = data[:train_num]<br>
    dev_data = data[train_num:]<br>
    collate = Collate(tokenizer, args.max_seq_len)<br>
</p>

<h2>第 14 页</h2>

<p>    train_dataset = ClsDataset(train_data)<br>
    train_sampler = torch.utils.data.distributed.DistributedSampler(train_dataset)<br>
    train_loader = DataLoader(train_dataset,<br>
                              batch_size=args.train_batch_size,<br>
                              num_workers=2,<br>
                              collate_fn=collate.collate_fn,<br>
                              sampler=train_sampler)<br>
    total_step = len(train_loader) * args.epochs<br>
    args.total_step = total_step<br>
    dev_dataset = ClsDataset(dev_data)<br>
    dev_sampler = torch.utils.data.distributed.DistributedSampler(dev_dataset)<br>
    dev_loader = DataLoader(dev_dataset,<br>
                            batch_size=args.dev_batch_size,<br>
                            shuffle=False,<br>
                            num_workers=2,<br>
                            collate_fn=collate.collate_fn,<br>
                            sampler=dev_sampler)<br>
    test_loader = dev_loader<br>
    # =======================================<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path,<br>
                                                          config=config)<br>
    # 第三步：封装模型<br>
    model.cuda()<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    criterion = torch.nn.CrossEntropyLoss()<br>
    optimizer = build_optimizer(model, args)<br>
    # =======================================<br>
    # =======================================<br>
    # 定义训练器，进行训练、验证和测试<br>
    trainer = Trainer(args,<br>
                      config,<br>
                      model,<br>
                      criterion,<br>
                      optimizer)<br>
    trainer.train(train_loader, dev_loader, train_sampler)<br>
    labels = list(label2id.keys())<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
</p>

<h2>第 15 页</h2>

<div class="image-container">
  <img src="images/49-图解分布式训练（五） —— AMP混合精度训练 详细解析_page15_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda(args.local_rank)<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    model.load_state_dict(torch.load(args.ckpt_path))<br>
    report = trainer.test(model, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
    # =======================================<br>
    dist.destroy_process_group()<br>
    # =======================================<br>
if __name__ == '__main__':<br>
    import argparse<br>
    parser = argparse.ArgumentParser()<br>
    parser.add_argument("--local_world_size", type=int, default=1)<br>
    p_args = parser.parse_args()<br>
    # 第零步：启动进程<br>
    mp.spawn(main_worker, nprocs=p_args.local_world_size, args=<br>
(p_args.local_world_size,))<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:47</p>
        </div>
    </div>
</body>
</html>