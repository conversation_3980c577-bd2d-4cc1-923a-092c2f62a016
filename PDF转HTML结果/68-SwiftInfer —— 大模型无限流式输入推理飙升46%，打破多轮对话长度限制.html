<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="images/68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:44<br>
StreamingLLM 篇<br>
EFFICIENT STREAMING LANGUAGE MODELS WITH ATTENTION SINKS <br>
:https://arxiv.org/pdf/2309.17453.pdf<br>
一、为什么需要 StreamingLLM？<br>
二、StreamingLLM 思路是什么？<br>
通过观察注意力模块中Softmax的输出，发现了attention sink的现象。<br>
注意力机制会为每一个token分配一个注意力值，而文本最初的几个token总是会分配到很多无用的<br>
注意力。<br>
当我们使用基于滑动窗口的注意力机制时，一旦这几个token被踢出了窗口，模型的生成效果就会<br>
迅速崩溃。但只要一直把这几个token保留在窗口内，模型就能稳定地生成出高质量的文本。<br>
比起密集注意力（Dense Attention）、窗口注意力（Window Attention）以及带重计算的滑动窗口<br>
注意力(Sliding Window w/ Re-computing)，StreamingLLM基于attention sink的注意力机制无论是<br>
在计算复杂度还是生成效果上都表现优异。<br>
在不需要重新训练模型的前提下，StreamingLLM能够直接兼容目前的主流大语言模型并改善推理<br>
性能。<br>
三、StreamingLLM 优点是什么？<br>
• SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制<br>
• StreamingLLM 篇<br>
• 一、为什么需要 StreamingLLM？<br>
• 二、StreamingLLM 思路是什么？<br>
• 三、StreamingLLM 优点是什么？<br>
• SwiftInfer 篇：基于TensorRT的StreamingLLM实现<br>
• 一、为什么需要 SwiftInfer？<br>
• 二、SwiftInfer 思路是什么？<br>
• 三、SwiftInfer 优点是什么？<br>
• 致谢<br>
1. 大语言模型能够记住的上下文长度问题，对ChatGPT等大模型应用与用户互动的质量的影响；<br>
2. LLM在预训练期间只能在有限的注意力窗口的限制下进行训练；<br>
3. 常见的KV Cache机制能够节约模型计算的时间，但是在多轮对话的情景下，key和value的缓<br>
存会消耗大量的内存，无法在有限的显存下无限扩展上下文；<br>
4. 二次微调后的模型无法很好地泛化到比训练序列长度更长的文本，导致生成效果糟糕；<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="images/68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>StreamingLLM 能够在不牺牲推理速度和生成效果的前提下，可实现多轮对话总共400万个token的<br>
流式输入，22.2倍的推理速度提升。<br>
SwiftInfer 篇：基于TensorRT的StreamingLLM实现<br>
一、为什么需要 SwiftInfer？<br>
StreamingLLM使用原生PyTorch实现，对于多轮对话推理场景落地应用的低成本、低延迟、高吞吐<br>
等需求仍有优化空间。<br>
二、SwiftInfer 思路是什么？<br>
如下图所示，假设窗口大小为10个token，随着生成的token增加（由黄色方块表示），我们在KV<br>
缓存中将中间的token踢出，与此同时，始终保持着文本开始的几个token（由蓝色方块表示）。由<br>
于黄色方块的位置会发生变化，在计算注意力时，我们也需要重新注入位置信息。<br>
需要注意的是，StreamingLLM不会直接提高模型能访问的上下文窗口，而是能够在支持流式超多<br>
轮对话的同时保证模型的生成效果。<br>
三、SwiftInfer 优点是什么？<br>
原版本的StreamingLLM可以可靠地实现超过400万个token的流式输入，实现了比带重计算的滑动<br>
窗口注意力机制高出22.2倍的速度提升。<br>
Colossal-AI团队发布的SwiftInfer可以进一步提升推理性能，最多带来额外的最多46%的推理吞吐<br>
速度提升，为大模型多轮对话推理提供低成本、低延迟、高吞吐的最佳实践。TensorRT-LLM团队<br>
也在同期对StreamingLLM进行了类似支持。<br>
1. 将StreamingLLM方法与TensorRT推理优化结合，使 SwiftInfer 不仅拥有 原始StreamingLLM的<br>
所有优点，而且还具有更高的运行效率；<br>
2. 重新实现了KV Cache机制以及带有位置偏移的注意力模块；<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/68-SwiftInfer —— 大模型无限流式输入推理飙升46%，打破多轮对话长度限制_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:49</p>
        </div>
    </div>
</body>
</html>