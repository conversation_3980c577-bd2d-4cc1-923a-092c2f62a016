<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>61-大模型（LLMs）加速篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>61-大模型（LLMs）加速篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="images/61-大模型（LLMs）加速篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="images/61-大模型（LLMs）加速篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）加速篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:44<br>
1. 当前优化模型最主要技术手段有哪些？<br>
2. 推理加速框架有哪一些？都有什么特点？<br>
3 vLLM 篇<br>
3.1 vLLM 的 功能有哪些？<br>
3.2 vLLM 的 优点有哪些？<br>
3.3 vLLM 的 缺点有哪些？<br>
3.4 vLLM 离线批量推理？<br>
• 算法层面：蒸馏、量化<br>
• 软件层面：计算图优化、模型编译<br>
• 硬件层面：FP8（NVIDIA H系列GPU开始支持FP8，兼有fp16的稳定性和int8的速度）<br>
• FasterTransformer：英伟达推出的FasterTransformer不修改模型架构而是在计算加速层面优化 Transformer <br>
的 encoder 和 decoder 模块。具体包括如下：<br>
• 尽可能多地融合除了 GEMM 以外的操作<br>
• 支持 FP16、INT8、FP8<br>
• 移除 encoder 输入中无用的 padding 来减少计算开销<br>
• TurboTransformers：腾讯推出的 TurboTransformers 由 computation runtime 及 serving framework 组成。<br>
加速推理框架适用于 CPU 和 GPU，最重要的是，它可以无需预处理便可处理变长的输入序列。具体包括如<br>
下：<br>
• 与 FasterTransformer 类似，它融合了除 GEMM 之外的操作以减少计算量<br>
• smart batching，对于一个 batch 内不同长度的序列，它也最小化了 zero-padding 开销<br>
• 对 LayerNorm 和 Softmax 进行批处理，使它们更适合并行计算<br>
• 引入了模型感知分配器，以确保在可变长度请求服务期间内存占用较小<br>
• Continuous batching：有iteration-level的调度机制，每次迭代batch大小都有所变化，因此vLLM在大量查询<br>
下仍可以很好的工作；<br>
• PagedAttention：受操作系统中虚拟内存和分页的经典思想启发的注意力算法，这就是模型加速的秘诀<br>
1. 文本生成的速度：实验多次，发现vLLM的推理速度是最快的；<br>
2. 高吞吐量服务：支持各种解码算法，比如parallel sampling, beam search等；<br>
3. 与OpenAI API兼容：如果使用OpenAI API，只需要替换端点的URL即可；<br>
1. 添加自定义模型：虽然可以合并自己的模型，但如果模型没有使用与vLLM中现有模型类似的架构，则过程会<br>
变得更加复杂。例如，增加Falcon的支持，这似乎很有挑战性；<br>
2. 缺乏对适配器（LoRA、QLoRA等）的支持：当针对特定任务进行微调时，开源LLM具有重要价值。然而，<br>
在当前的实现中，没有单独使用模型和适配器权重的选项，这限制了有效利用此类模型的灵活性。<br>
3. 缺少权重量化：有时，LLM可能不需要使用GPU内存，这对于减少GPU内存消耗至关重要。<br>
# pip install vllm<br>
from vllm import LLM, SamplingParams<br>
prompts = [<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>3.5 vLLM API Server？<br>
4 Text generation inference 篇<br>
4.1 介绍一下 Text generation inference？<br>
Text generation inference是用于文本生成推断的Rust、Python和gRPC服务器，在HuggingFace中已有LLM 推理<br>
API使用。<br>
4.2 Text generation inference 的 功能有哪些？<br>
4.3 Text generation inference 的 优点有哪些？<br>
4.4 Text generation inference 的 缺点有哪些？<br>
4.5 Text generation inference 的 使用docker运行web server？<br>
    "Funniest joke ever:",<br>
    "The capital of France is",<br>
    "The future of AI is",<br>
]<br>
sampling_params = SamplingParams(temperature=0.95, top_p=0.95, max_tokens=200)<br>
llm = LLM(model="huggyllama/llama-13b")<br>
outputs = llm.generate(prompts, sampling_params)<br>
for output in outputs:<br>
    prompt = output.prompt<br>
    generated_text = output.outputs[0].text<br>
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")<br>
# Start the server:<br>
python -m vllm.entrypoints.api_server --env MODEL_NAME=huggyllama/llama-13b<br>
# Query the model in shell:<br>
curl http://localhost:8000/generate \<br>
    -d '{<br>
        "prompt": "Funniest joke ever:",<br>
        "n": 1,<br>
        "temperature": 0.95,<br>
        "max_tokens": 200<br>
    }'<br>
• 内置服务评估：可以监控服务器负载并深入了解其性能；<br>
• 使用flash attention（和v2）和Paged attention优化transformer推理代码：并非所有模型都内置了对这些优化<br>
的支持，该技术可以对未使用该技术的模型可以进行优化；<br>
• 所有的依赖项都安装在Docker中：会得到一个现成的环境；<br>
• 支持HuggingFace模型：轻松运行自己的模型或使用任何HuggingFace模型中心；<br>
• 对模型推理的控制：该框架提供了一系列管理模型推理的选项，包括精度调整、量化、张量并行性、重复惩<br>
罚等；<br>
• 缺乏对适配器的支持：需要注意的是，尽管可以使用适配器部署LLM（可以参考<br>
https://www.youtube.com/watch?v=HI3cYN0c9ZU），但目前还没有官方支持或文档；<br>
• 从源代码（Rust+CUDA内核）编译：对于不熟悉Rust的人，将客户化代码纳入库中变得很有挑战性；<br>
• 文档不完整：所有信息都可以在项目的自述文件中找到。尽管它涵盖了基础知识，但必须在问题或源代码中<br>
搜索更多细节；<br>
mkdir data<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="images/61-大模型（LLMs）加速篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
docker run --gpus all --shm-size 1g -p 8080:80 \<br>
-v data:/data ghcr.io/huggingface/text-generation-inference:0.9 \<br>
  --model-id huggyllama/llama-13b \<br>
  --num-shard 1<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:40:51</p>
        </div>
    </div>
</body>
</html>