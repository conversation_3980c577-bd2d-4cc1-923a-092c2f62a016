#!/bin/bash

# PDF批量转换为HTML工具 - Shell脚本

echo "============================================================"
echo "PDF批量转换为HTML工具 - Shell脚本"
echo "============================================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3，请先安装Python"
    exit 1
fi

# 检查脚本文件是否存在
if [ ! -f "pdf_to_html_converter.py" ]; then
    echo "错误: 未找到pdf_to_html_converter.py脚本文件"
    exit 1
fi

# 安装依赖
echo "正在检查并安装依赖..."
python3 -m pip install PyMuPDF tqdm --quiet

# 获取用户输入
read -p "请输入PDF文件所在目录路径（直接回车使用当前目录）: " input_dir
if [ -z "$input_dir" ]; then
    input_dir="."
fi

read -p "请输入输出目录路径（直接回车在原目录生成）: " output_dir

read -p "是否递归处理子目录？(y/n，默认y): " recursive
if [ -z "$recursive" ]; then
    recursive="y"
fi

# 构建命令
command="python3 pdf_to_html_converter.py \"$input_dir\""

if [ -n "$output_dir" ]; then
    command="$command -o \"$output_dir\""
fi

if [ "$recursive" = "n" ] || [ "$recursive" = "N" ]; then
    command="$command --no-recursive"
fi

echo ""
echo "执行命令: $command"
echo ""

# 执行转换
eval $command

echo ""
echo "转换完成！"
