<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>10-LLMs 训练经验帖</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>10-LLMs 训练经验帖</h1>
        <h2>第 1 页</h2>

<p>LLMs 训练经验帖<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月28日 22:03<br>
分布式训练框架选择？<br>
多用 DeepSpeed，少用 Pytorch 原生的 torchrun。在节点数量较少的情况下，使用何种训练框架并不是特别重<br>
要；然而，一旦涉及到数百个节点，DeepSpeed显现出其强大之处，其简便的启动和便于性能分析的特点使其成<br>
为理想之选。<br>
LLMs 训练时 有哪些有用的建议？<br>
大模型训练不是以往那种单机训个几小时就结束的任务，往往需要训练好几周甚至好几个月，这时候你就知道能<br>
稳定训练有多么重要。弹性容错能让你在机器故障的情况下依然继续重启训练；自动重启能让你在训练中断之后<br>
立刻重启训练。毕竟，大模型时代，节约时间就是节约钱。<br>
训练的时候每隔一段时间做个checkpointing，这样如果训练中断还能从上次的断点来恢复训练。<br>
训练一次大模型的成本很高的。在训练之前先想清楚这次训练的目的，记录训练参数和中间过程结果，少做重复<br>
劳动。<br>
有时候，即使增加了多块 A100 GPU，大型模型的训练速度未必会加快，这很可能是因为GPU使用效率不高，尤<br>
其在多机训练情况下更为明显。仅仅依赖nvidia-smi显示的GPU 利用率并不足以准确反映实际情况，因为即使显<br>
示为100%，实际GPU利用率也可能不是真正的 100%。要更准确地评估GPU利用率，需要关注TFLOPS和吞吐<br>
率等指标，这些监控在DeepSpeed框架中都得以整合。<br>
对于同一模型，选择不同的训练框架，对于资源的消耗情况可能存在显著差异（比如使用Huggingface <br>
Transformers和DeepSpeed训练OPT-30相对于使用Alpa对于资源的消耗会低不少）。<br>
针对已有的环境进行分布式训练环境搭建时，一定要注意之前环境的python、pip、virtualenv、setuptools的版<br>
本。不然创建的虚拟环境即使指定对了Python版本，也可能会遇到很多安装依赖库的问题（GPU服务器能够访问<br>
外网的情况下，建议使用Docker相对来说更方便）。<br>
遇到需要升级GLIBC等底层库需要升级的提示时，一定要慎重，不要轻易升级，否则，可能会造成系统宕机或很<br>
多命令无法操作等情况。<br>
模型大小如何选择？<br>
进行大模型模型训练时，先使用小规模模型（如：OPT-125m/2.7b）进行尝试，然后再进行大规模模型（如：<br>
OPT-13b/30b...）的尝试，便于出现问题时进行排查。目前来看，业界也是基于相对较小规模参数的模型<br>
（6B/7B/13B）进行的优化，同时，13B模型经过指令精调之后的模型效果已经能够到达GPT4的90%的效果。<br>
加速卡如何选择？<br>
于一些国产AI加速卡，目前来说，坑还比较多，如果时间不是时间非常充裕，还是尽量选择Nvidia的AI加速卡。<br>
1. 弹性容错和自动重启机制<br>
1. 定期保存模型<br>
1. 想清楚再开始训练<br>
1. 关注GPU使用效率<br>
1. 不同的训练框架 对 同一个模型 影响不同<br>
1. 环境问题<br>
1. 升级GLIBC等底层库问题<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:57</p>
        </div>
    </div>
</body>
</html>