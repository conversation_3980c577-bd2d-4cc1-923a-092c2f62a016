<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-LLMs 激活函数篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>3-LLMs 激活函数篇</h1>
        <h2>第 1 页</h2>

<p>LLMs 激活函数篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:41<br>
1 介绍一下 FFN 块 计算公式？<br>
2 介绍一下 GeLU 计算公式？<br>
3 介绍一下 Swish 计算公式？<br>
2个可训练权重矩阵，中间维度为 4h<br>
4 介绍一下 使用 GLU 线性门控单元的 FFN 块 计算公式？<br>
5 介绍一下 使用 GeLU 的 GLU 块 计算公式？<br>
6 介绍一下 使用 Swish 的 GLU 块 计算公式？<br>
3个可训练权重矩阵，中间维度为 4h*2/3<br>
各LLMs 都使用哪种激活函数？<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>4h = 4*4096 = 16384<br>
2/3 * 4h = 10022 -&gt; 11008<br>
11008/128 = 86<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:56</p>
        </div>
    </div>
</body>
</html>