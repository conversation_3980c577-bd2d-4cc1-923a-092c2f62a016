<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>91-向量检索常见面试篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>91-向量检索常见面试篇</h1>
        <h2>第 1 页</h2>

<p>向量检索常见面试篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月12日 06:45<br>
一、向量检索库总结<br>
1.1 Annoy<br>
github:https://github.com/spotify/annoy<br>
1.1.1 Annoy 介绍<br>
Annoy是高维空间求近似最近邻的一个开源库。全称：Approximate Nearest Neighbors Oh <br>
Yeah，是一种适合实际应用的快速相似查找算法。<br>
Annoy构建一个二叉树，查询时间为O（logn）。<br>
1.1.2 Annoy 使用<br>
直接通过pip install annoy安装。<br>
• 向量检索常见面试篇<br>
• 一、向量检索库总结<br>
• 1.1 Annoy<br>
• 1.1.1 Annoy 介绍<br>
• 1.1.2 Annoy 使用<br>
• 1.2 Faiss<br>
• 1.2.1 Faiss 介绍<br>
• 1.2.2 Faiss 主要特性<br>
• 1.2.3 Faiss 使用<br>
• 1.3 Milvus<br>
• 1.4 ElasticSearch<br>
• 1.4.1 ElasticSearch 介绍<br>
• 1.4.2 什么是倒排索引呢？<br>
• 1.4.3 ES机制<br>
from annoy import AnnoyIndex<br>
import random<br>
# 向量的维度<br>
f = 40  # Length of item vector that will be indexed<br>
# 返回一个可读可写的存储 f 维向量的索引<br>
t = AnnoyIndex(f, 'angular')<br>
for i in range(1000):<br>
    # random.gauss为随机生成高斯分布的随机数<br>
    v = [random.gauss(0, 1) for z in range(f)]<br>
    # 在位置 i 添加向量<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>上面的例子为官方github提供的例子。<br>
向量检索时用到的函数<br>
索引属性函数<br>
annoy接口中一般需要调整的参数有两个：树的数量n_trees和搜索过程中检查的节点数量search_k<br>
1.2 Faiss<br>
1.2.1 Faiss 介绍<br>
Faiss库是由 Facebook 开发的适用于稠密向量匹配的开源库，支持 c++ 与 python 调用。Faiss提<br>
供了高效的索引类库。是向量化检索开山鼻祖的应用。<br>
Faiss 支持多种向量检索方式，包括内积、欧氏距离等，同时支持精确检索与模糊搜索。<br>
1.2.2 Faiss 主要特性<br>
    t.add_item(i, v)<br>
# 建立一棵 n_trees的森林，树越多，精度越高<br>
t.build(10) # 10 trees<br>
# 保存<br>
t.save('test.ann')<br>
# ...<br>
u = AnnoyIndex(f, 'angular')<br>
# 直接加载<br>
u.load('test.ann') # super fast, will just mmap the file<br>
print(u.get_nns_by_item(0, 1000)) # will find the 1000 nearest neighbors<br>
• a.get_nns_by_item(i, n, search_k=-1, include_distances=False): 返回最接近 i 的n个item。查<br>
询过程中，将检查search_k个节点，默认为n_trees* n。serarch_k实现了准确性和速度之间的<br>
运行时间权衡。include_distances为True时将返回一个包含两个列表的2元素元组:第二个包含<br>
所有相应的距离。<br>
• a.get_nns_by_vector(v, n, search_k=-1, include_distances=False): 和上面的根据item查询一<br>
样，只不过这里时给定一个查询向量v,比如给定一个用户embedding， 返回n个最近邻的item, <br>
一般这样用的时候， 后面的距离会带着，可能作为精排那面的强特。<br>
• a.get_item_vector(i): 返回索引i对应的向量。<br>
• a.get_distance(i, j): 返回item_i和item_j的平方距离。<br>
• a.get_n_items(): 返回索引中的items个数，即词典大小。<br>
• a.get_n_trees(): 索引树的个数。<br>
• n_trees: 在构建期间提供，影响构建时间和索引大小。值越大，结果越准确，但索引越大。<br>
• search_k: 在运行时提供，并影响搜索性能。值越大，结果越准确，但返回的时间越长。如果不<br>
提供，就是n_trees * n， n是最近邻的个数。<br>
• github:https://github.com/facebookresearch/faiss<br>
• tutorial:https://github.com/facebookresearch/faiss/wiki/Getting-started<br>
• 支持相似度检索和聚类；<br>
• 支持多种索引方式；<br>
• 支持CPU和GPU计算；<br>
</p>

<h2>第 3 页</h2>

<p>1.2.3 Faiss 使用<br>
直接通过pip install faiss-cpu --no-cache进行安装。<br>
faiss的使用方法也比较简单，归纳为以下三个步骤：<br>
1.3 Milvus<br>
Milvus的更多介绍可以查看：https://gitee.com/milvus-io/mil<br>
Milvus 是一款开源的特征向量相似度搜索引擎，使用方便、实用可靠、易于扩展、稳定高效和搜索<br>
迅速。<br>
1.4 ElasticSearch<br>
1.4.1 ElasticSearch 介绍<br>
• 支持Python和C++调用；<br>
1. 构建向量库，对已知的数据进行向量，最终以矩阵的形式表示<br>
2. 为矩阵选择合适的index，将第一步得到的矩阵add到index中<br>
3. search得到最终结果<br>
import numpy as np <br>
import faiss <br>
d = 64 <br>
nb = 100000<br>
nq = 10000<br>
# 构建向量库<br>
xb = np.random.random((nb, d)).astype('float32')  <br>
xb[:, 0] += np.arange(nb) / 1000.<br>
xq = np.random.random((nq, d)).astype('float32')<br>
xq[:, 0] += np.arange(nq) / 1000.<br>
# 关键步骤，build index<br>
index = faiss.IndexFlatL2(d)   <br>
index.add(xb)<br>
k = 4 <br>
D, I = index.search(xq[:5], k)   # 分别返回距离和索引<br>
• 高性能：涵盖如Faiss、Annoy和hnswlib等主流第三方索引库，性能高，支持对海量向量数据进<br>
行相似搜索。<br>
• 高可用、高可靠：Milvus支持使用Kubernetes部署，支持在云上扩展。其容灾能力能够保证服<br>
务的高可用。Milvus依照日志及数据的理念，使用如Pulsar、Kafka等消息队列的技术实现组件<br>
间的通信，对组件进行解耦，拥抱云原生。<br>
• 混合查询：Milvus支持在向量检索过程中进行标量字段过滤，实现混合查询。<br>
• 开发者友好：支持多语言、多工具的Milvus生态。如今Milvus已经支持Python、Java、Go和<br>
Node.js，未来可能还会扩展对更多语言的支持。Milvus提供了如Attu等工具，帮助用户简化操<br>
作。<br>
</p>

<h2>第 4 页</h2>

<p>Elasticsearch 是⼀个分布式可扩展的实时搜索和分析引擎，⼀个建⽴在全⽂搜索引擎 Apache <br>
Lucene(TM)基础上的搜索引擎，当然 Elasticsearch 并不仅仅是 Lucene 那么简单，它不仅包括了<br>
全⽂搜索功能，还可以进行以下⼯作：<br>
ES本质上是一个支持全文搜索的分布式内存数据库，特别适用于构建搜索系统，比如内容检索、<br>
文本检索、日志检索。其原因是采用了倒排索引。<br>
1.4.2 什么是倒排索引呢？<br>
倒排索引是一种特别为搜索而设计的索引结构。<br>
先对需要索引的字段进行分词，然后以分词为索引组成一个查找树，这样就把一个全文匹配的查找<br>
转换成了对树的查找。<br>
倒排索引相比于一般数据库采用B树索引，其写入和更新的性能比较差，因此倒排索引只适合全文<br>
搜索，不适合更新频繁的交易类数据。<br>
1.4.3 ES机制<br>
Elasticsearch的⽂件存储， Elasticsearch是⾯向⽂档型数据库，⼀条数据在这⾥就是⼀个⽂档，<br>
⽤JSON作为⽂档序列化的格式，比如下面这条用户数据：<br>
也就是说Elasticsearch比较适合存储非结构化或半结构化数据。<br>
⽤Mysql这样的数据库存储就会容易想到建⽴⼀张User表，有⽤户信息的字段等，在Elasticsearch<br>
⾥这就是⼀个⽂档，当然这个⽂档会属于⼀个User的类型，各种各样的类型存在于⼀个索引当中。<br>
这⾥有⼀份将Elasticsearch和关系型数据术语对照表：<br>
关系数据库【数据库关系系统】 ⇒ 数据库 ⇒ 表 ⇒ ⾏ ⇒ 列(Columns)<br>
Elasticsearch ⇒ 索引(Index) ⇒ 类型(type) ⇒ ⽂档(Docments) ⇒ 字段(Fields)<br>
知识星球<br>
• 分布式实时⽂件存储，并将每⼀个字段都编入索引，使其可以被搜索。<br>
• 实时分析的分布式搜索引擎。<br>
• 可以扩展到上百台服务器，处理PB级别的结构化或非结构化数据。<br>
{<br>
"name" : "John",<br>
"sex" : "Male",<br>
"age" : 25,<br>
"birthDate": "1990/05/01",<br>
"about" : "I love to go rock climbing",<br>
"interests": [ "sports", "music" ]<br>
} <br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:57</p>
        </div>
    </div>
</body>
</html>