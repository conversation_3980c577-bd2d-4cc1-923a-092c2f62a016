<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>73-百川智能baichuan7B、13B、53B、baichuan2 总结篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>73-百川智能baichuan7B、13B、53B、baichuan2 总结篇</h1>
        <h2>第 1 页</h2>

<p>百川智能baichuan7B、13B、53B、baichuan2 总结篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 13:44<br>
一、baichuan-7B篇<br>
项目地址：https://github.com/baichuan-inc/baichuan-7B<br>
预训练模型：https://huggingface.co/baichuan-inc/baichuan-7B<br>
modelscope：https://modelscope.cn/models/baichuan-inc/baichuan-7B/<br>
1. 你了解baichuan-7B解构么？介绍一下？<br>
baichuan-7B 基于 Transformer 结构，在大约1.2万亿 tokens 上训练的70亿参数模型，支持中英双语，上下文窗<br>
口长度为4096。<br>
2. baichuan-7B 如何 收集原始数据并 构建 训练数据？<br>
数据处理流程图<br>
3. baichuan-7B 如何 提高 训练稳定性和吞吐？<br>
在原本的 LLaMA 框架上进行诸多修改以提升训练时的吞吐，具体包括：<br>
最终的loss如下图：<br>
• 原始数据收集：开源的中英文数据和自行抓取的中文互联网数据，以及部分高质量知识性数据；<br>
• 数据预处理：频率和质量是数据处理环节重点考虑的两个维度。baichuan-7B 基于启发式规则和质量模型打<br>
分，对原始数据集进行篇章和句子粒度的过滤。在全量数据上，利用局部敏感哈希方法，对篇章和句子粒度<br>
做滤重。<br>
• 如何对数据进行配比：使用了一个基于自动学习的数据权重策略，对不同类别的数据进行配比<br>
1. 算子优化技术：采用更高效算子，如 Flash-Attention，NVIDIA apex 的 RMSNorm 等；<br>
2. 算子切分技术：将部分计算算子进行切分，减小内存峰值；<br>
3. 混合精度技术：降低在不损失模型精度的情况下加速计算过程；<br>
4. 训练容灾技术：训练平台和训练框架联合优化，IaaS + PaaS 实现分钟级的故障定位和任务恢复；<br>
5. 通信优化技术，具体包括：<br>
a. 采用拓扑感知的集合通信算法，避免网络拥塞问题，提高通信效率；<br>
b. 根据卡数自适应设置 bucket size，提高带宽利用率；<br>
c. 根据模型和集群环境，调优通信原语的触发时机，从而将计算和通信重叠；<br>
• 效果：基于上述的几个优化技术，我们在千卡 A800 显卡上达到了 7B 模型 182 TFLOPS 的吞吐，GPU 峰值<br>
算力利用率高达 58.3%。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>二、baichuan-13B篇<br>
项目地址：https://github.com/Baichuan-inc/Baichuan-13B<br>
预训练模型：https://huggingface.co/baichuan-inc/Baichuan-13B-Base<br>
对话模型：https://huggingface.co/baichuan-inc/Baichuan-13B-Chat<br>
modelscope：https://modelscope.cn/models/Baichuan-inc/Baichuan-13B-Chat/summary<br>
1. 相比于 baichuan-7B，baichuan-13B 的 特点体现在哪里？<br>
2. 如何 对 baichuan-13B 进行推理和部署？<br>
环境安装<br>
2.1 GPU直接部署<br>
方法一：Python代码方式<br>
1. 更大尺寸、更多数据：Baichuan-13B 在 Baichuan-7B 的基础上进一步扩大参数量到 130 亿，并且在高质<br>
量的语料上训练了 1.4 万亿 tokens，超过 LLaMA-13B 40%，是当前开源 13B 尺寸下训练数据量最多的模<br>
型。支持中英双语，使用 ALiBi 位置编码，上下文窗口长度为 4096；<br>
2. 同时开源预训练和对齐模型：预训练模型是适用开发者的『 基座 』，而广大普通用户对有对话功能的对齐模<br>
型具有更强的需求。因此本次开源我们同时发布了对齐模型（Baichuan-13B-Chat），具有很强的对话能<br>
力，开箱即用，几行代码即可简单的部署；<br>
3. 更高效的推理：为了支持更广大用户的使用，同时开源了 int8 和 int4 的量化版本，相对非量化版本在几乎没<br>
有效果损失的情况下大大降低了部署的机器资源门槛，可以部署在如 Nvidia 3090 这样的消费级显卡上；<br>
    $ pip install -r requirements.txt<br>
&gt;&gt;&gt; import torch<br>
&gt;&gt;&gt; from transformers import AutoModelForCausalLM, AutoTokenizer<br>
&gt;&gt;&gt; from transformers.generation.utils import GenerationConfig<br>
&gt;&gt;&gt; tokenizer = AutoTokenizer.from_pretrained("baichuan-inc/Baichuan-13B-Chat", <br>
use_fast=False, trust_remote_code=True)<br>
&gt;&gt;&gt; model = AutoModelForCausalLM.from_pretrained("baichuan-inc/Baichuan-13B-Chat", <br>
device_map="auto", torch_dtype=torch.float16, trust_remote_code=True)<br>
&gt;&gt;&gt; model.generation_config = GenerationConfig.from_pretrained("baichuan-<br>
inc/Baichuan-13B-Chat")<br>
</p>

<h2>第 3 页</h2>

<p>注：模型加载指定 device_map='auto'，会使用所有可用显卡。如需指定使用的设备，可以使用<br>
类似 export CUDA_VISIBLE_DEVICES=0,1（使用了0、1号显卡）的方式控制。<br>
方法二：命令行方式<br>
2.2 量化部署<br>
Baichuan-13B 支持 int8 和 int4 量化，用户只需在推理代码中简单修改两行即可实现。<br>
注：如果是为了节省显存而进行量化，应加载原始精度模型到 CPU 后再开始量化；避免在<br>
from_pretrained时添加device_map='auto'或者其它会导致把原始精度模型直接加载到 GPU 的行<br>
为的参数。<br>
&gt;&gt;&gt; messages = []<br>
&gt;&gt;&gt; messages.append({"role": "user", "content": "世界上第二高的山峰是哪座"})<br>
&gt;&gt;&gt; response = model.chat(tokenizer, messages)<br>
&gt;&gt;&gt; print(response)<br>
乔戈里峰。世界第二高峰———乔戈里峰西方登山者称其为k2峰，海拔高度是8611米，位于喀<br>
喇昆仑山脉的中巴边境上<br>
    $ python cli_demo.py<br>
• int8量化<br>
model = AutoModelForCausalLM.from_pretrained(<br>
    "baichuan-inc/Baichuan-13B-Chat", <br>
    torch_dtype=torch.float16, <br>
    trust_remote_code=True<br>
)<br>
model = model.quantize(8).cuda()<br>
• int4量化<br>
model = AutoModelForCausalLM.from_pretrained(<br>
    "baichuan-inc/Baichuan-13B-Chat", <br>
    torch_dtype=torch.float16, <br>
    trust_remote_code=True<br>
)<br>
model = model.quantize(4).cuda()<br>
• 量化前后占用显存情况如下：<br>
• 量化后在各个 benchmark 上的结果和原始版本对比如下：<br>
</p>

<h2>第 4 页</h2>

<p>2.3 CPU部署<br>
使用CPU进行推理大概需要 60GB 内存<br>
3. 如何 对 baichuan-13B 进行微调？<br>
注：开发者可以对 Baichuan-13B-Base 或 Baichuan-13B-Chat 进行微调使用。团队测试了与 Baichuan-13B 兼<br>
容的微调工具 LLaMA Efficient Tuning，并给出全量微调和 LoRA微调的两种示范。<br>
输入数据为放置在项目data目录下的 json 文件，用--dataset选项指定（参考下面示例），多个输入文件用,分<br>
隔。json 文件示例格式和字段说明如下：<br>
json 文件中存储一个列表，列表的每个元素是一个 sample。其中instruction代表用户输入，input是可选项，如<br>
果开发者同时指定了instruction和input，会把二者用\n连接起来代表用户输入；output代表期望的模型输出。<br>
3.1 全量微调<br>
    model = AutoModelForCausalLM.from_pretrained(<br>
        "baichuan-inc/Baichuan-13B-Chat", <br>
        torch_dtype=torch.float32, <br>
        trust_remote_code=True<br>
    )<br>
• 数据格式<br>
    [<br>
        {<br>
            "instruction": "What are the three primary colors?",<br>
            "input": "",<br>
            "output": "The three primary colors are red, blue, and yellow."<br>
        },<br>
        ....<br>
    ]<br>
• 微调环境：8 * Nvidia A100 80 GB + deepspeed<br>
deepspeed --num_gpus=8 src/train_bash.py \<br>
    --stage sft \<br>
    --model_name_or_path baichuan-inc/Baichuan-13B-Base \<br>
    --do_train \<br>
    --dataset alpaca_gpt4_en,alpaca_gpt4_zh \<br>
    --finetuning_type full \<br>
    --output_dir path_to_your_sft_checkpoint \<br>
    --overwrite_cache \<br>
    --per_device_train_batch_size 4 \ <br>
    --per_device_eval_batch_size 4 \ <br>
    --gradient_accumulation_steps 8 \ <br>
    --preprocessing_num_workers 16 \<br>
    --lr_scheduler_type cosine \<br>
</p>

<h2>第 5 页</h2>

<p>deep_speed.json 配置示例：<br>
3.2 LoRA微调<br>
    --logging_steps 10 \<br>
    --save_steps 100 \<br>
    --eval_steps 100 \<br>
    --learning_rate 5e-5 \<br>
    --max_grad_norm 0.5 \<br>
    --num_train_epochs 2.0 \<br>
    --dev_ratio 0.01 \<br>
    --evaluation_strategy steps \<br>
    --load_best_model_at_end \<br>
    --plot_loss \<br>
    --fp16 \<br>
    --deepspeed deepspeed.json<br>
{<br>
  "train_micro_batch_size_per_gpu": "auto",<br>
  "zero_allow_untested_optimizer": true,<br>
  "fp16": {<br>
    "enabled": "auto",<br>
    "loss_scale": 0,<br>
    "initial_scale_power": 16, <br>
    "loss_scale_window": 1000,<br>
    "hysteresis": 2,<br>
    "min_loss_scale": 1<br>
  },  <br>
  "zero_optimization": {<br>
    "stage": 2,<br>
    "allgather_partitions": true,<br>
    "allgather_bucket_size": 5e8,<br>
    "overlap_comm": false,<br>
    "reduce_scatter": true,<br>
    "reduce_bucket_size": 5e8,<br>
    "contiguous_gradients" : true<br>
  }<br>
}<br>
• 微调环境：1 * Nvidia A100 80 GB<br>
CUDA_VISIBLE_DEVICES=0 python src/train_bash.py \<br>
    --stage sft \<br>
    --model_name_or_path baichuan-inc/Baichuan-13B-Base \<br>
    --do_train \<br>
    --dataset alpaca_gpt4_en,alpaca_gpt4_zh \<br>
    --finetuning_type lora \<br>
    --lora_rank 8 \ <br>
    --lora_target W_pack \<br>
    --output_dir path_to_your_sft_checkpoint \<br>
</p>

<h2>第 6 页</h2>

<p>三、baichuan-53B篇<br>
3.1 baichuan-53B 相比于 baichuan-7B 和 baichuan-13B 有哪些优势？<br>
Baichuan-53B 的三个技术优势：预训练数据、搜索增强和对齐能力，其中前两者与百川团队中丰富的搜索引擎<br>
经验有较强相关性。<br>
3.2 baichuan-53B 如何对 预训练数据 做处理？<br>
3.3 baichuan-53B 如何进行 搜索增强？<br>
四、baichuan2篇<br>
Baichuan 2 大模型开原链接：https://github.com/baichuan-inc/Baichuan2<br>
技术报告：https://cdn.baichuan-ai.com/paper/Baichuan2-technical-report.pdf<br>
4.1 baichuan2 与 其他大模型 对比<br>
Baichuan2-13B-Base 相比上一代 13B 模型，数学能力提升 49%，代码能力提升 46%，安全能力提升 37%，逻<br>
辑推理能力提升 25%，语义理解能力提升 15%。<br>
    --overwrite_cache \<br>
    --per_device_train_batch_size 4 \ <br>
    --per_device_eval_batch_size 4 \ <br>
    --gradient_accumulation_steps 8 \ <br>
    --preprocessing_num_workers 16 \<br>
    --lr_scheduler_type cosine \<br>
    --logging_steps 10 \<br>
    --save_steps 100 \<br>
    --eval_steps 100 \<br>
    --learning_rate 5e-5 \<br>
    --max_grad_norm 0.5 \<br>
    --num_train_epochs 2.0 \<br>
    --dev_ratio 0.01 \<br>
    --evaluation_strategy steps \<br>
    --load_best_model_at_end \<br>
    --plot_loss \<br>
    --fp16<br>
• 百川希望构建一个全面的世界知识体系，覆盖各个领域和学科的知识，通过整合各类信息源，确保文化、科<br>
学、技术等方面广泛的知识覆盖；<br>
• 目前百川已经建立了一套系统的数据质量体系，包括低质、优质、类别等，确保整个预训练过程中维持高标<br>
准的数据质量，以让数据为最终模型训练的目标服务；<br>
• 为保证数据的多样性并有效处理重复信息，百川设计了一个多粒度的大规模聚类系统。通过使用先进的聚类<br>
算法和方法，识别和整合相似或相关的数据，为去重、采样提供支撑；<br>
• 百川还开发了一种细粒度的自动化匹配算法，自动配比各类任务，例如课程学习。从而实现个性化的模型学<br>
习，使预训练数据能够更精确地匹配用户需求；<br>
1. 动态响应策略，依赖 Prompt，将指令任务细化为 16 个独立类别，覆盖各种用户指令的场景。<br>
2. 智能化搜索词生成，通过对问答样本进行精细化的人工标注，捕捉和理解用户多元化的志林需求。<br>
3. 高质量搜索结果筛选，百川构建了一个搜索结果相关性模型，对从搜索内容和知识库中获取的信息进行相关<br>
性频分，从而筛选出高质量的搜索引用内容，减少在知识抽取阶段引入的无关、低质量的信息。<br>
4. 回答结果的搜索增强，RLHF，让 Baichuan 大模型参照搜索结果，针对用户请求生成高价值且具有实时性的<br>
回答。<br>
</p>

<h2>第 7 页</h2>

<p>五、baichuan 数据构建篇<br>
5.1 baichuan 进行微调时，领域数据：通用数据配比？<br>
基于base预训练模型，发现 领域数据：通用数据配比是1:5的时候效果最好<br>
</p>

<h2>第 8 页</h2>

<p>从这张表里可以得出几个结论：<br>
知识星球<br>
1. 基于baichuan-13B base 预训练模型做fine-tune时， 领域数据：通用数据配比是1:10时在领域指标上最好<br>
2. 基于baichuan-13B base 上继续做预训练（不用通用领域数据）时，领域数据：通用数据配比是1:5时在领域<br>
指标上最好<br>
3. 基于baichuan-13B base 上继续做预训练（领域数据：通用数据配比是1:5）时，领域数据：通用数据配比是<br>
1:5时在领域指标上最好<br>
4. 基于baichuan-13B chat（做过多轮对话和指令微调），领域数据：通用数据配比是1:5时在领域指标上最好<br>
5. 基于baichuan-13B base 上做预训练和SFT finetune时（无通用数据），效果最好。【缺少了基于baichuan-<br>
13B base 上做预训练和SFT finetune时（领域数据：通用数据配比是1:5）的对比】<br>
• 参考：ChatHome: Development and Evaluation of a Domain-Specific Language Model for Home <br>
Renovation https://arxiv.org/abs/2307.15290<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:55</p>
        </div>
    </div>
</body>
</html>