<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLMs大模型面试问题和答案97页</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LLMs大模型面试问题和答案97页</h1>
        <h2>第 1 页</h2>

<p>LLMs <br>
⼤模型（LLMs）基础⾯<br>
⼤模型（LLMs）进阶⾯<br>
⼤模型（LLMs）微调⾯<br>
⼤模型（LLMs）微调⾯<br>
⼤模型（LLMs）训练经验帖<br>
⼤模型（LLMs）langchain ⾯<br>
⼤模型（LLMs）langchain ⾯<br>
基于LLM+向量库的⽂档对话 经验⾯<br>
LLM⽂档对话 —— pdf解析关键问题<br>
基于LLM+向量库的⽂档对话 经验⾯<br>
⼤模型（LLMs）参数⾼效微调(PEFT) ⾯<br>
⼤模型（LLMs）参数⾼效微调(PEFT) ⾯<br>
配器微调（Adapter-tuning）篇<br>
提示学习（Prompting）<br>
LoRA 系列篇<br>
⼤模型（LLMs）推理⾯<br>
⼤模型（LLMs）推理⾯<br>
⼤模型（LLMs）预训练⾯<br>
⼤模型（LLMs）增量预训练篇<br>
⼤模型（LLMs）评测⾯<br>
⼤模型（LLMs）强化学习⾯<br>
⼤模型（LLMs）软硬件配置⾯<br>
⼤模型（LLMs）训练集⾯<br>
⼤模型（LLMs）显存问题⾯<br>
⼤模型（LLMs）分布式训练⾯<br>
⼤模型（LLMs）分布式训练⾯<br>
图解分布式训练（⼀） —— 流⽔线并⾏（Pipeline Parallelism）⾯<br>
图解分布式训练（⼆） —— nn.DataParallel⾯<br>
图解分布式训练（三） ——  nn.parallel.DistributedDataParallel<br>
图解分布式训练（四） ——  torch.multiprocessing 详细解析<br>
图解分布式训练（五） ——  AMP混合精度训练 详细解析<br>
图解分布式训练（六） —— Pytorch的 DeepSpeed 详细解析<br>
图解分布式训练（七）—— accelerate 分布式训练 详细解析<br>
图解分布式训练（⼋）—— ZeRO 学习<br>
⼤模型（LLMs）agent ⾯<br>
Token及模型参数准备篇<br>
LLMs 位置编码篇<br>
LLMs Tokenizer 篇<br>
</p>

<h2>第 2 页</h2>

<p>LLMs Tokenizer 篇<br>
怎么让英⽂⼤语⾔模型⽀持中⽂？（⼀） —— 构建中⽂tokenization<br>
怎么让英⽂⼤语⾔模型⽀持中⽂？（⼆） —— 继续预训练篇<br>
怎么让英⽂⼤语⾔模型⽀持中⽂？（三） —— 对预训练模型进⾏指令微调<br>
Layer normalization 篇<br>
LLMs 激活函数篇<br>
LLMs 激活函数篇<br>
⼤模型（LLMs）加速篇<br>
⼤模型（LLMs）加速篇<br>
LLM（⼤语⾔模型）部署加速⽅法——PagedAttention篇<br>
⼤模型推理加速⼯具 —— vLLM<br>
LLM（⼤语⾔模型）部署加速⽅法——Faster Transformer篇<br>
纯Python超轻量⾼性能LLM推理框架 —— LightLLM<br>
Attention 升级⾯<br>
⼤模型幻觉（LLM Hallucination）⾯<br>
⼤模型幻觉（LLM Hallucination）⾯<br>
⼤模型的幻觉问题篇<br>
⼤模型的幻觉问题篇<br>
LLMs 对⽐篇<br>
LLMs 对⽐篇<br>
百川智能baichuan7B、13B、53B、baichuan2 总结篇<br>
思维链 Chain-of-Thought（COT）篇<br>
思维链 Chain-of-Thought（COT）篇<br>
思维链 Chain-of-Thought（COT）变体篇<br>
思维链 Chain-of-Thought（COT）变体篇<br>
⼤模型（LLMs）基础⾯<br>
 <br>
1. ⽬前 主流的开源模型体系 有哪些？<br>
2. preﬁx Decoder 和 causal Decoder 和 Encoder-Decoder 区别是什么？<br>
3. ⼤模型LLM的 训练⽬标 是什么？<br>
4. 涌现能⼒是啥原因？<br>
5. 为何现在的⼤模型⼤部分是Decoder only结构？<br>
6. 简单 介绍⼀下 ⼤模型【LLMs】？<br>
7. ⼤模型【LLMs】后⾯跟的 175B、60B、540B等 指什么？<br>
8. ⼤模型【LLMs】具有什么优点？<br>
9. ⼤模型【LLMs】具有什么缺点？<br>
</p>

<h2>第 3 页</h2>

<p>⼤模型（LLMs）基础⾯<br>
 <br>
⽬前主流的开源LLM（语⾔模型）模型体系包括以下⼏个：<br>
1. <br>
 ⽬前 主流的开源模型体系 有哪些？<br>
2. <br>
 preﬁx LM 和 causal LM 区别是什么？    <br>
3. <br>
 涌现能⼒是啥原因？<br>
  1. GPT（Generative Pre-trained Transformer）系列：由OpenAI发布的⼀系列基于<br>
Transformer架构的语⾔模型，包括GPT、GPT-2、GPT-3等。GPT模型通过在⼤规模⽆标签⽂本上进<br>
⾏预训练，然后在特定任务上进⾏微调，具有很强的⽣成能⼒和语⾔理解能⼒。<br>
  2. BERT（Bidirectional Encoder Representations from Transformers）：由<br>
Google发布的⼀种基于Transformer架构的双向预训练语⾔模型。BERT模型通过在⼤规模⽆标签⽂<br>
本上进⾏预训练，然后在下游任务上进⾏微调，具有强⼤的语⾔理解能⼒和表征能⼒。<br>
  3. XLNet：由CMU和Google Brain发布的⼀种基于Transformer架构的⾃回归预训练语⾔模<br>
型。XLNet模型通过⾃回归⽅式预训练，可以建模全局依赖关系，具有更好的语⾔建模能⼒和⽣成能<br>
⼒。<br>
  4. RoBERTa：由Facebook发布的⼀种基于Transformer架构的预训练语⾔模型。RoBERTa模型<br>
在BERT的基础上进⾏了改进，通过更⼤规模的数据和更⻓的训练时间，取得了更好的性能。<br>
  5. T5（Text-to-Text Transfer Transformer）：由Google发布的⼀种基于Transformer<br>
架构的多任务预训练语⾔模型。T5模型通过在⼤规模数据集上进⾏预训练，可以⽤于多种⾃然语⾔处理<br>
任务，如⽂本分类、机器翻译、问答等。<br>
  这些模型在⾃然语⾔处理领域取得了显著的成果，并被⼴泛应⽤于各种任务和应⽤中。<br>
  &lt;/aside&gt;<br>
  Prefix LM（前缀语⾔模型）和Causal LM（因果语⾔模型）是两种不同类型的语⾔模型，它们的<br>
区别在于⽣成⽂本的⽅式和训练⽬标。<br>
  1. Prefix LM：前缀语⾔模型是⼀种⽣成模型，它在⽣成每个词时都可以考虑之前的上下⽂信<br>
息。在⽣成时，前缀语⾔模型会根据给定的前缀（即部分⽂本序列）预测下⼀个可能的词。这种模型可<br>
以⽤于⽂本⽣成、机器翻译等任务。<br>
  2. Causal LM：因果语⾔模型是⼀种⾃回归模型，它只能根据之前的⽂本⽣成后续的⽂本，⽽不<br>
能根据后续的⽂本⽣成之前的⽂本。在训练时，因果语⾔模型的⽬标是预测下⼀个词的概率，给定之前<br>
的所有词作为上下⽂。这种模型可以⽤于⽂本⽣成、语⾔建模等任务。<br>
  总结来说，前缀语⾔模型可以根据给定的前缀⽣成后续的⽂本，⽽因果语⾔模型只能根据之前的⽂本<br>
⽣成后续的⽂本。它们的训练⽬标和⽣成⽅式略有不同，适⽤于不同的任务和应⽤场景。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 4 页</h2>

<p>⼤模型的涌现能⼒主要是由以下⼏个原因造成的：<br>
4. <br>
 ⼤模型LLM的架构介绍？<br>
  1. 数据量的增加：随着互联⽹的发展和数字化信息的爆炸增⻓，可⽤于训练模型的数据量⼤⼤增<br>
加。更多的数据可以提供更丰富、更⼴泛的语⾔知识和语境，使得模型能够更好地理解和⽣成⽂本。<br>
  2. 计算能⼒的提升：随着计算硬件的发展，特别是图形处理器（GPU）和专⽤的AI芯⽚（如TPU）<br>
的出现，计算能⼒⼤幅提升。这使得训练更⼤、更复杂的模型成为可能，从⽽提⾼了模型的性能和涌现<br>
能⼒。<br>
  3. 模型架构的改进：近年来，⼀些新的模型架构被引⼊，如Transformer，它在处理序列数据上<br>
表现出⾊。这些新的架构通过引⼊⾃注意⼒机制等技术，使得模型能够更好地捕捉⻓距离的依赖关系和<br>
语⾔结构，提⾼了模型的表达能⼒和⽣成能⼒。<br>
  4. 预训练和微调的⽅法：预训练和微调是⼀种有效的训练策略，可以在⼤规模⽆标签数据上进⾏预<br>
训练，然后在特定任务上进⾏微调。这种⽅法可以使模型从⼤规模数据中学习到更丰富的语⾔知识和语<br>
义理解，从⽽提⾼模型的涌现能⼒。<br>
  综上所述，⼤模型的涌现能⼒是由数据量的增加、计算能⼒的提升、模型架构的改进以及预训练和微<br>
调等因素共同作⽤的结果。这些因素的进步使得⼤模型能够更好地理解和⽣成⽂本，为⾃然语⾔处理领<br>
域带来了显著的进展。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 5 页</h2>

<p>⼤模型（LLMs）进阶⾯<br>
 <br>
1. LLMs 复读机问题<br>
1. 什么是 LLMs 复读机问题？<br>
2. 为什么会出现 LLMs 复读机问题？<br>
3. 如何缓解 LLMs 复读机问题？<br>
2. llama 系列问题<br>
1. llama 输⼊句⼦⻓度理论上可以⽆限⻓吗？<br>
3. 什么情况⽤Bert模型，什么情况⽤LLaMA、ChatGLM类⼤模型，咋选？<br>
4. 各个专业领域是否需要各⾃的⼤模型来服务？<br>
5. 如何让⼤模型处理更⻓的⽂本？<br>
⼤模型（LLMs）进阶⾯<br>
 <br>
1. LLMs 复读机问题<br>
  LLM（Large Language Model，⼤型语⾔模型）是指基于⼤规模数据和参数量的语⾔模型。具体<br>
的架构可以有多种选择，以下是⼀种常⻅的⼤模型LLM的架构介绍：<br>
  1. Transformer架构：⼤模型LLM常使⽤Transformer架构，它是⼀种基于⾃注意⼒机制的序列<br>
模型。Transformer架构由多个编码器层和解码器层组成，每个层都包含多头⾃注意⼒机制和前馈神<br>
经⽹络。这种架构可以捕捉⻓距离的依赖关系和语⾔结构，适⽤于处理⼤规模语⾔数据。<br>
  2. ⾃注意⼒机制（Self-Attention）：⾃注意⼒机制是Transformer架构的核⼼组件之⼀。它<br>
允许模型在⽣成每个词时，根据输⼊序列中的其他词来计算该词的表示。⾃注意⼒机制能够动态地为每<br>
个词分配不同的权重，从⽽更好地捕捉上下⽂信息。<br>
  3. 多头注意⼒（Multi-Head Attention）：多头注意⼒是⾃注意⼒机制的⼀种扩展形式。它将<br>
⾃注意⼒机制应⽤多次，每次使⽤不同的权重矩阵进⾏计算，得到多个注意⼒头。多头注意⼒可以提供<br>
更丰富的上下⽂表示，增强模型的表达能⼒。<br>
  4. 前馈神经⽹络（Feed-Forward Network）：在Transformer架构中，每个注意⼒层后⾯都<br>
有⼀个前馈神经⽹络。前馈神经⽹络由两个全连接层组成，通过⾮线性激活函数（如ReLU）进⾏变<br>
换。它可以对注意⼒层输出的表示进⾏进⼀步的映射和调整。<br>
  5. 预训练和微调：⼤模型LLM通常采⽤预训练和微调的⽅法进⾏训练。预训练阶段使⽤⼤规模⽆标<br>
签数据，通过⾃监督学习等⽅法进⾏训练，使模型学习到丰富的语⾔知识。微调阶段使⽤有标签的特定<br>
任务数据，如⽂本⽣成、机器翻译等，通过有监督学习进⾏模型的微调和优化。<br>
  需要注意的是，⼤模型LLM的具体架构可能会因不同的研究和应⽤⽽有所不同。上述介绍的是⼀种常<br>
⻅的架构，但实际应⽤中可能会有⼀些变体或改进。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 6 页</h2>

<p>      <br>
1. <br>
 什么是 LLMs 复读机问题？<br>
2. <br>
 为什么会出现 LLMs 复读机问题？<br>
  LLMs复读机问题指的是⼤型语⾔模型（LLMs）在⽣成⽂本时出现的⼀种现象，即模型倾向于⽆限地<br>
复制输⼊的⽂本或者以过度频繁的⽅式重复相同的句⼦或短语。这种现象使得模型的输出缺乏多样性和<br>
创造性，给⽤户带来了不好的体验。<br>
  复读机问题可能出现的原因包括：<br>
  1. 数据偏差：⼤型语⾔模型通常是通过预训练阶段使⽤⼤规模⽆标签数据进⾏训练的。如果训练数<br>
据中存在⼤量的重复⽂本或者某些特定的句⼦或短语出现频率较⾼，模型在⽣成⽂本时可能会倾向于复<br>
制这些常⻅的模式。<br>
  2. 训练⽬标的限制：⼤型语⾔模型的训练通常是基于⾃监督学习的⽅法，通过预测下⼀个词或掩盖<br>
词来学习语⾔模型。这样的训练⽬标可能使得模型更倾向于⽣成与输⼊相似的⽂本，导致复读机问题的<br>
出现。<br>
  3. 缺乏多样性的训练数据：虽然⼤型语⾔模型可以处理⼤规模的数据，但如果训练数据中缺乏多样<br>
性的语⾔表达和语境，模型可能⽆法学习到⾜够的多样性和创造性，导致复读机问题的出现。<br>
  为了解决复读机问题，可以采取以下策略：<br>
  1. 多样性训练数据：在训练阶段，尽量使⽤多样性的语料库来训练模型，避免数据偏差和重复⽂本<br>
的问题。<br>
  2. 引⼊噪声：在⽣成⽂本时，可以引⼊⼀些随机性或噪声，例如通过采样不同的词或短语，或者引<br>
⼊随机的变换操作，以增加⽣成⽂本的多样性。<br>
  3. 温度参数调整：温度参数是⽤来控制⽣成⽂本的多样性的⼀个参数。通过调整温度参数的值，可<br>
以控制⽣成⽂本的独创性和多样性，从⽽减少复读机问题的出现。<br>
  4. 后处理和过滤：对⽣成的⽂本进⾏后处理和过滤，去除重复的句⼦或短语，以提⾼⽣成⽂本的质<br>
量和多样性。<br>
  需要注意的是，复读机问题是⼤型语⾔模型⾯临的⼀个挑战，解决这个问题是⼀个复杂的任务，需要<br>
综合考虑数据、训练⽬标、模型架构和⽣成策略等多个因素。⽬前，研究⼈员和⼯程师们正在不断努⼒<br>
改进和优化⼤型语⾔模型，以提⾼其⽣成⽂本的多样性和创造性。<br>
  &lt;/aside&gt;<br>
  出现LLMs复读机问题可能有以下⼏个原因：<br>
  1. 数据偏差：⼤型语⾔模型通常是通过预训练阶段使⽤⼤规模⽆标签数据进⾏训练的。如果训练数<br>
据中存在⼤量的重复⽂本或者某些特定的句⼦或短语出现频率较⾼，模型在⽣成⽂本时可能会倾向于复<br>
制这些常⻅的模式。<br>
</p>

<h2>第 7 页</h2>

<p>3. <br>
 如何缓解 LLMs 复读机问题？<br>
  2. 训练⽬标的限制：⼤型语⾔模型的训练通常是基于⾃监督学习的⽅法，通过预测下⼀个词或掩盖<br>
词来学习语⾔模型。这样的训练⽬标可能使得模型更倾向于⽣成与输⼊相似的⽂本，导致复读机问题的<br>
出现。<br>
  3. 缺乏多样性的训练数据：虽然⼤型语⾔模型可以处理⼤规模的数据，但如果训练数据中缺乏多样<br>
性的语⾔表达和语境，模型可能⽆法学习到⾜够的多样性和创造性，导致复读机问题的出现。<br>
  4. 模型结构和参数设置：⼤型语⾔模型的结构和参数设置也可能对复读机问题产⽣影响。例如，模<br>
型的注意⼒机制和⽣成策略可能导致模型更倾向于复制输⼊的⽂本。<br>
  为了解决复读机问题，可以采取以下策略：<br>
  1. 多样性训练数据：在训练阶段，尽量使⽤多样性的语料库来训练模型，避免数据偏差和重复⽂本<br>
的问题。<br>
  2. 引⼊噪声：在⽣成⽂本时，可以引⼊⼀些随机性或噪声，例如通过采样不同的词或短语，或者引<br>
⼊随机的变换操作，以增加⽣成⽂本的多样性。<br>
  3. 温度参数调整：温度参数是⽤来控制⽣成⽂本的多样性的⼀个参数。通过调整温度参数的值，可<br>
以控制⽣成⽂本的独创性和多样性，从⽽减少复读机问题的出现。<br>
  4. 后处理和过滤：对⽣成的⽂本进⾏后处理和过滤，去除重复的句⼦或短语，以提⾼⽣成⽂本的质<br>
量和多样性。<br>
  需要注意的是，复读机问题是⼤型语⾔模型⾯临的⼀个挑战，解决这个问题是⼀个复杂的任务，需要<br>
综合考虑数据、训练⽬标、模型架构和⽣成策略等多个因素。⽬前，研究⼈员和⼯程师们正在不断努⼒<br>
改进和优化⼤型语⾔模型，以提⾼其⽣成⽂本的多样性和创造性。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 8 页</h2>

<p>2. llama 系列问题<br>
1. <br>
 llama 输⼊句⼦⻓度理论上可以⽆限⻓吗？<br>
  为了缓解LLMs复读机问题，可以尝试以下⽅法：<br>
  1. 多样性训练数据：在训练阶段，使⽤多样性的语料库来训练模型，避免数据偏差和重复⽂本的问<br>
题。这可以包括从不同领域、不同来源和不同⻛格的⽂本中获取数据。<br>
  2. 引⼊噪声：在⽣成⽂本时，引⼊⼀些随机性或噪声，例如通过采样不同的词或短语，或者引⼊随<br>
机的变换操作，以增加⽣成⽂本的多样性。这可以通过在⽣成过程中对模型的输出进⾏采样或添加随机<br>
性来实现。<br>
  3. 温度参数调整：温度参数是⽤来控制⽣成⽂本的多样性的⼀个参数。通过调整温度参数的值，可<br>
以控制⽣成⽂本的独创性和多样性。较⾼的温度值会增加随机性，从⽽减少复读机问题的出现。<br>
  4. Beam搜索调整：在⽣成⽂本时，可以调整Beam搜索算法的参数。Beam搜索是⼀种常⽤的⽣成策<br>
略，它在⽣成过程中维护了⼀个候选序列的集合。通过调整Beam⼤⼩和搜索宽度，可以控制⽣成⽂本<br>
的多样性和创造性。<br>
  5. 后处理和过滤：对⽣成的⽂本进⾏后处理和过滤，去除重复的句⼦或短语，以提⾼⽣成⽂本的质<br>
量和多样性。可以使⽤⽂本相似度计算⽅法或规则来检测和去除重复的⽂本。<br>
  6. ⼈⼯⼲预和控制：对于关键任务或敏感场景，可以引⼊⼈⼯⼲预和控制机制，对⽣成的⽂本进⾏<br>
审查和筛选，确保⽣成结果的准确性和多样性。<br>
  需要注意的是，缓解LLMs复读机问题是⼀个复杂的任务，没有⼀种通⽤的解决⽅案。不同的⽅法可<br>
能适⽤于不同的场景和任务，需要根据具体情况进⾏选择和调整。此外，解决复读机问题还需要综合考<br>
虑数据、训练⽬标、模型架构和⽣成策略等多个因素，需要进⼀步的研究和实践来提⾼⼤型语⾔模型的<br>
⽣成⽂本多样性和创造性。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 9 页</h2>

<p>3. <br>
 什么情况⽤Bert模型，什么情况⽤LLaMA、ChatGLM类⼤模型，咋选？<br>
   选择使⽤哪种⼤模型，如Bert、LLaMA或ChatGLM，取决于具体的应⽤场景和需求。下⾯是⼀些指导<br>
原则：<br>
1. Bert模型：Bert是⼀种预训练的语⾔模型，适⽤于各种⾃然语⾔处理任务，如⽂本分类、命名实体<br>
识别、语义相似度计算等。如果你的任务是通⽤的⽂本处理任务，⽽不依赖于特定领域的知识或语<br>
⾔⻛格，Bert模型通常是⼀个不错的选择。<br>
2. LLaMA模型：LLaMA（Language Model for the Medical Domain）是专⻔针对医学领域的预训练语<br>
⾔模型。如果你的应⽤场景涉及医学领域，例如医学⽂本的理解、医学问答系统等，LLaMA模型可<br>
能更适合，因为它在医学领域的知识和术语上进⾏了专⻔的训练。<br>
3. ChatGLM模型：ChatGLM是⼀个⾯向对话⽣成的语⾔模型，适⽤于构建聊天机器⼈、智能客服等<br>
对话系统。如果你的应⽤场景需要模型能够⽣成连贯、流畅的对话回复，并且需要处理对话上下<br>
⽂、⽣成多轮对话等，ChatGLM模型可能是⼀个较好的选择。<br>
   在选择模型时，还需要考虑以下因素：<br>
数据可⽤性：不同模型可能需要不同类型和规模的数据进⾏训练。确保你有⾜够的数据来训练和微<br>
调所选择的模型。<br>
计算资源：⼤模型通常需要更多的计算资源和存储空间。确保你有⾜够的硬件资源来⽀持所选择的<br>
模型的训练和推理。<br>
预训练和微调：⼤模型通常需要进⾏预训练和微调才能适应特定任务和领域。了解所选择模型的预<br>
训练和微调过程，并确保你有相应的数据和时间来完成这些步骤。<br>
  理论上来说，LLMs（⼤型语⾔模型）可以处理任意⻓度的输⼊句⼦，但实际上存在⼀些限制和挑<br>
战。下⾯是⼀些相关的考虑因素：<br>
  1. 计算资源：⽣成⻓句⼦需要更多的计算资源，包括内存和计算时间。由于LLMs通常是基于神经<br>
⽹络的模型，计算⻓句⼦可能会导致内存不⾜或计算时间过⻓的问题。<br>
  2. 模型训练和推理：训练和推理⻓句⼦可能会⾯临⼀些挑战。在训练阶段，处理⻓句⼦可能会导致<br>
梯度消失或梯度爆炸的问题，影响模型的收敛性和训练效果。在推理阶段，⽣成⻓句⼦可能会增加模型<br>
的错误率和⽣成时间。<br>
  3. 上下⽂建模：LLMs是基于上下⽂建模的模型，⻓句⼦的上下⽂可能会更加复杂和深层。模型需<br>
要能够捕捉⻓句⼦中的语义和语法结构，以⽣成准确和连贯的⽂本。<br>
  尽管存在这些挑战，研究⼈员和⼯程师们已经在不断努⼒改进和优化LLMs，以处理更⻓的句⼦。例<br>
如，可以采⽤分块的⽅式处理⻓句⼦，将其分成多个较短的⽚段进⾏处理。此外，还可以通过增加计算<br>
资源、优化模型结构和参数设置，以及使⽤更⾼效的推理算法来提⾼LLMs处理⻓句⼦的能⼒。<br>
  值得注意的是，实际应⽤中，⻓句⼦的处理可能还受到应⽤场景、任务需求和资源限制等因素的影<br>
响。因此，在使⽤LLMs处理⻓句⼦时，需要综合考虑这些因素，并根据具体情况进⾏选择和调整。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 10 页</h2>

<p>   最佳选择取决于具体的应⽤需求和限制条件。在做出决策之前，建议先进⾏⼀些实验和评估，以确定<br>
哪种模型最适合你的应⽤场景。<br>
   <br>
4. <br>
 各个专业领域是否需要各⾃的⼤模型来服务？<br>
   各个专业领域通常需要各⾃的⼤模型来服务，原因如下：<br>
1. 领域特定知识：不同领域拥有各⾃特定的知识和术语，需要针对该领域进⾏训练的⼤模型才能更好<br>
地理解和处理相关⽂本。例如，在医学领域，需要训练具有医学知识的⼤模型，以更准确地理解和<br>
⽣成医学⽂本。<br>
2. 语⾔⻛格和惯⽤语：各个领域通常有⾃⼰独特的语⾔⻛格和惯⽤语，这些特点对于模型的训练和⽣<br>
成都很重要。专⻔针对某个领域进⾏训练的⼤模型可以更好地掌握该领域的语⾔特点，⽣成更符合<br>
该领域要求的⽂本。<br>
3. 领域需求的差异：不同领域对于⽂本处理的需求也有所差异。例如，⾦融领域可能更关注数字和统<br>
计数据的处理，⽽法律领域可能更关注法律条款和案例的解析。因此，为了更好地满⾜不同领域的<br>
需求，需要专⻔针对各个领域进⾏训练的⼤模型。<br>
4. 数据稀缺性：某些领域的数据可能相对较少，⽆法充分训练通⽤的⼤模型。针对特定领域进⾏训练<br>
的⼤模型可以更好地利⽤该领域的数据，提⾼模型的性能和效果。<br>
   尽管需要各⾃的⼤模型来服务不同领域，但也可以共享⼀些通⽤的模型和技术。例如，通⽤的⼤模型<br>
可以⽤于处理通⽤的⽂本任务，⽽领域特定的模型可以在通⽤模型的基础上进⾏微调和定制，以适应特<br>
定领域的需求。这样可以在满⾜领域需求的同时，减少模型的重复训练和资源消耗。<br>
   <br>
5. <br>
 如何让⼤模型处理更⻓的⽂本？<br>
   要让⼤模型处理更⻓的⽂本，可以考虑以下⼏个⽅法：<br>
1. 分块处理：将⻓⽂本分割成较短的⽚段，然后逐个⽚段输⼊模型进⾏处理。这样可以避免⻓⽂本对<br>
模型内存和计算资源的压⼒。在处理分块⽂本时，可以使⽤重叠的⽅式，即将相邻⽚段的⼀部分重<br>
叠，以保持上下⽂的连贯性。<br>
2. 层次建模：通过引⼊层次结构，将⻓⽂本划分为更⼩的单元。例如，可以将⽂本分为段落、句⼦或<br>
⼦句等层次，然后逐层输⼊模型进⾏处理。这样可以减少每个单元的⻓度，提⾼模型处理⻓⽂本的<br>
能⼒。<br>
3. 部分⽣成：如果只需要模型⽣成⽂本的⼀部分，⽽不是整个⽂本，可以只输⼊部分⽂本作为上下<br>
⽂，然后让模型⽣成所需的部分。例如，输⼊前⼀部分⽂本，让模型⽣成后续的内容。<br>
4. 注意⼒机制：注意⼒机制可以帮助模型关注输⼊中的重要部分，可以⽤于处理⻓⽂本时的上下⽂建<br>
模。通过引⼊注意⼒机制，模型可以更好地捕捉⻓⽂本中的关键信息。<br>
5. 模型结构优化：通过优化模型结构和参数设置，可以提⾼模型处理⻓⽂本的能⼒。例如，可以增加<br>
模型的层数或参数量，以增加模型的表达能⼒。还可以使⽤更⾼效的模型架构，如Transformer等，<br>
以提⾼⻓⽂本的处理效率。<br>
</p>

<h2>第 11 页</h2>

<p>   需要注意的是，处理⻓⽂本时还需考虑计算资源和时间的限制。较⻓的⽂本可能需要更多的内存和计<br>
算时间，因此在实际应⽤中需要根据具体情况进⾏权衡和调整。<br>
   <br>
⼤模型（LLMs）微调⾯<br>
 <br>
⼤模型（LLMs）微调⾯<br>
 <br>
1. 如果想要在某个模型基础上做全参数微调，究竟需要多少显存？<br>
2. 为什么SFT之后感觉LLM傻了?<br>
3. SFT 指令微调数据 如何构建?<br>
4. 领域模型Continue PreTrain 数据选取？<br>
5. 领域数据训练后，通⽤能⼒往往会有所下降，如何缓解模型遗忘通⽤能⼒？<br>
6. 领域模型Continue PreTrain ，如何 让模型在预训练过程中就学习到更多的知识？<br>
7. 进⾏SFT操作的时候，基座模型选⽤Chat还是Base?<br>
8. 领域模型微调 指令&amp;数据输⼊格式 要求？<br>
9. 领域模型微调 领域评测集 构建？<br>
10. 领域模型词表扩增是不是有必要的？<br>
11. 如何训练⾃⼰的⼤模型？<br>
12. 训练中⽂⼤模型有啥经验？<br>
13. 指令微调的好处？<br>
14. 预训练和微调哪个阶段注⼊知识的？<br>
15. 想让模型学习某个领域或⾏业的知识，是应该预训练还是应该微调？<br>
16. 多轮对话任务如何微调模型？<br>
17. 微调后的模型出现能⼒劣化，灾难性遗忘是怎么回事？<br>
18. 微调模型需要多⼤显存？<br>
19. ⼤模型LLM进⾏SFT操作的时候在学习什么？<br>
20. 预训练和SFT操作有什么不同<br>
21. 样本量规模增⼤，训练出现OOM错<br>
22. ⼤模型LLM进⾏SFT 如何对样本进⾏优化？<br>
23. 模型参数迭代实验<br>
24. 微调⼤模型的⼀些建议<br>
⼤模型（LLMs）微调⾯<br>
 <br>
1. <br>
 如果想要在某个模型基础上做全参数微调，究竟需要多少显存？<br>
   要确定全参数微调所需的显存量，需要考虑以下⼏个因素：<br>
1. 模型的⼤⼩：模型的⼤⼩是指模型参数的数量。通常，参数越多，模型的⼤⼩就越⼤。⼤型的预训<br>
练模型如Bert、GPT等通常有数亿到数⼗亿个参数，⽽较⼩的模型可能只有数百万到数千万个参<br>
</p>

<h2>第 12 页</h2>

<p>数。模型的⼤⼩直接影响了所需的显存量。<br>
2. 批量⼤⼩：批量⼤⼩是指在每次训练迭代中⼀次性输⼊到模型中的样本数量。较⼤的批量⼤⼩可以<br>
提⾼训练的效率，但也需要更多的显存。通常，全参数微调时，较⼤的批量⼤⼩可以提供更好的性<br>
能。<br>
3. 训练数据的维度：训练数据的维度是指输⼊数据的形状。如果输⼊数据具有较⾼的维度，例如图像<br>
数据，那么所需的显存量可能会更⼤。对于⽂本数据，通常需要进⾏⼀些编码和嵌⼊操作，这也会<br>
增加显存的需求。<br>
4. 训练设备的显存限制：最后，需要考虑训练设备的显存限制。显卡的显存⼤⼩是⼀个硬性限制，超<br>
过显存限制可能导致训练失败或性能下降。确保所选择的模型和批量⼤⼩适应训练设备的显存⼤<br>
⼩。<br>
   综上所述，全参数微调所需的显存量取决于模型的⼤⼩、批量⼤⼩、训练数据的维度以及训练设备的<br>
显存限制。在进⾏全参数微调之前，建议先评估所需的显存量，并确保训练设备具备⾜够的显存来⽀持<br>
训练过程。<br>
   <br>
2. <br>
 为什么SFT之后感觉LLM傻了?<br>
   在进⾏Supervised Fine-Tuning（SFT）之后，有时可能会观察到基座模型（如语⾔模型）的性能下降<br>
或产⽣⼀些“傻”的⾏为。这可能是由于以下原因：<br>
1. 数据偏移：SFT过程中使⽤的微调数据集可能与基座模型在预训练阶段接触到的数据分布有所不<br>
同。如果微调数据集与预训练数据集之间存在显著的差异，模型可能会在新任务上表现较差。这种<br>
数据偏移可能导致模型在新任务上出现错误的预测或不准确的输出。<br>
2. ⾮典型标注：微调数据集的标注可能存在错误或不准确的标签。这些错误的标签可能会对模型的性<br>
能产⽣负⾯影响，导致模型产⽣“傻”的⾏为。<br>
3. 过拟合：如果微调数据集相对较⼩，或者模型的容量（参数数量）较⼤，模型可能会过拟合微调数<br>
据，导致在新的输⼊上表现不佳。过拟合可能导致模型过于依赖微调数据的特定样本，⽽⽆法泛化<br>
到更⼴泛的输⼊。<br>
4. 缺乏多样性：微调数据集可能缺乏多样性，未能涵盖模型在新任务上可能遇到的各种输⼊情况。这<br>
可能导致模型在⾯对新的、与微调数据集不同的输⼊时出现困惑或错误的预测。<br>
   为了解决这些问题，可以尝试以下⽅法：<br>
收集更多的训练数据，以增加数据的多样性和覆盖范围。<br>
仔细检查微调数据集的标注，确保标签的准确性和⼀致性。<br>
使⽤正则化技术（如权重衰减、dropout）来减少过拟合的⻛险。<br>
进⾏数据增强，通过对微调数据进⾏⼀些变换或扩充来增加多样性。<br>
使⽤更复杂的模型架构或调整模型的超参数，以提⾼模型的性能和泛化能⼒。<br>
   通过这些⽅法，可以尽量减少Supervised Fine-Tuning之后模型出现“傻”的情况，并提⾼模型在新任务<br>
上的表现。<br>
   <br>
</p>

<h2>第 13 页</h2>

<p>3. <br>
 SFT 指令微调数据 如何构建?<br>
   构建Supervised Fine-Tuning（SFT）的微调数据需要以下步骤：<br>
1. 收集原始数据：⾸先，您需要收集与⽬标任务相关的原始数据。这可以是对话数据、分类数据、⽣<br>
成任务数据等，具体取决于您的任务类型。确保数据集具有代表性和多样性，以提⾼模型的泛化能<br>
⼒。<br>
2. 标注数据：对原始数据进⾏标注，为每个样本提供正确的标签或⽬标输出。标签的类型取决于您的<br>
任务，可以是分类标签、⽣成⽂本、对话回复等。确保标注的准确性和⼀致性。<br>
3. 划分数据集：将标注数据划分为训练集、验证集和测试集。通常，⼤部分数据⽤于训练，⼀⼩部分<br>
⽤于验证模型的性能和调整超参数，最后⼀部分⽤于最终评估模型的泛化能⼒。<br>
4. 数据预处理：根据任务的要求，对数据进⾏预处理。这可能包括⽂本清洗、分词、去除停⽤词、词<br>
⼲化等处理步骤。确保数据格式和特征表示适合模型的输⼊要求。<br>
5. 格式转换：将数据转换为适合模型训练的格式。这可能涉及将数据转换为⽂本⽂件、JSON格式或<br>
其他适合模型输⼊的格式。<br>
6. 模型微调：使⽤转换后的数据对基座模型进⾏微调。根据任务的要求，选择适当的微调⽅法和超参<br>
数进⾏训练。这可以使⽤常⻅的深度学习框架（如PyTorch、TensorFlow）来实现。<br>
7. 模型评估：使⽤测试集对微调后的模型进⾏评估，计算模型在任务上的性能指标，如准确率、召回<br>
率、⽣成质量等。根据评估结果对模型进⾏进⼀步的优化和调整。<br>
   通过以上步骤，您可以构建适合Supervised Fine-Tuning的微调数据集，并使⽤该数据集对基座模型进<br>
⾏微调，以适应特定任务的需求。<br>
   <br>
4. <br>
 领域模型Continue PreTrain 数据选取？<br>
   在领域模型的Continue PreTrain过程中，数据选取是⼀个关键的步骤。以下是⼀些常⻅的数据选取⽅<br>
法：<br>
1. 领域相关数据：⾸先，可以收集与⽬标领域相关的数据。这些数据可以是从互联⽹上爬取的、来⾃<br>
特定领域的⽂档或者公司内部的数据等。这样的数据可以提供领域相关的语⾔和知识，有助于模型<br>
在特定领域上的表现。<br>
2. 领域专家标注：如果有领域专家可⽤，可以请他们对领域相关的数据进⾏标注。标注可以是分类、<br>
命名实体识别、关系抽取等任务，这样可以提供有监督的数据⽤于模型的训练。<br>
3. 伪标签：如果没有领域专家或者标注数据的成本较⾼，可以使⽤⼀些⾃动化的⽅法⽣成伪标签。例<br>
如，可以使⽤预训练的模型对领域相关的数据进⾏预测，将预测结果作为伪标签，然后使⽤这些伪<br>
标签进⾏模型的训练。<br>
4. 数据平衡：在进⾏数据选取时，需要注意数据的平衡性。如果某个类别的数据样本较少，可以考虑<br>
使⽤数据增强技术或者对该类别进⾏过采样，以平衡各个类别的数据量。<br>
5. 数据质量控制：在进⾏数据选取时，需要对数据的质量进⾏控制。可以使⽤⼀些质量评估指标，如<br>
数据的准确性、⼀致性等，来筛选和过滤数据。<br>
6. 数据预处理：在进⾏数据选取之前，可能需要对数据进⾏⼀些预处理，如分词、去除停⽤词、标准<br>
化等，以准备好输⼊模型进⾏训练。<br>
</p>

<h2>第 14 页</h2>

<p>   在数据选取过程中，需要根据具体任务和需求进⾏适当的调整和定制。选择合适的数据可以提⾼模型<br>
在特定领域上的性能和泛化能⼒。<br>
   <br>
5. <br>
 领域数据训练后，通⽤能⼒往往会有所下降，如何缓解模型遗忘通⽤能⼒？<br>
   当使⽤领域数据进⾏训练后，模型往往会出现遗忘通⽤能⼒的问题。以下是⼀些缓解模型遗忘通⽤能<br>
⼒的⽅法：<br>
1. 保留通⽤数据：在进⾏领域数据训练时，仍然需要保留⼀部分通⽤数据⽤于模型训练。这样可以确<br>
保模型仍然能够学习到通⽤的语⾔和知识，从⽽保持⼀定的通⽤能⼒。<br>
2. 增量学习：使⽤增量学习（Incremental Learning）的⽅法，将领域数据与通⽤数据逐步交替进⾏训<br>
练。这样可以在学习新领域的同时，保持对通⽤知识的记忆。<br>
3. 预训练和微调：在领域数据训练之前，可以使⽤⼤规模通⽤数据进⾏预训练，获得⼀个通⽤的基础<br>
模型。然后，在领域数据上进⾏微调，以适应特定领域的任务。这样可以在保留通⽤能⼒的同时，<br>
提升领域任务的性能。<br>
4. 强化学习：使⽤强化学习的⽅法，通过给模型设置奖励机制，⿎励模型在领域任务上表现好，同时<br>
保持⼀定的通⽤能⼒。<br>
5. 领域适应技术：使⽤领域适应技术，如领域⾃适应（Domain Adaptation）和领域对抗训练<br>
（Domain Adversarial Training），帮助模型在不同领域之间进⾏迁移学习，从⽽减少遗忘通⽤能⼒<br>
的问题。<br>
6. 数据重采样：在进⾏领域数据训练时，可以使⽤数据重采样的⽅法，使得模型在训练过程中能够更<br>
多地接触到通⽤数据，从⽽缓解遗忘通⽤能⼒的问题。<br>
   综合使⽤上述⽅法，可以在⼀定程度上缓解模型遗忘通⽤能⼒的问题，使得模型既能够适应特定领域<br>
的任务，⼜能够保持⼀定的通⽤能⼒。<br>
   <br>
6. <br>
 领域模型Continue PreTrain ，如何 让模型在预训练过程中就学习到更多的知识？<br>
   在领域模型的Continue PreTrain过程中，可以采取⼀些策略来让模型在预训练过程中学习到更多的知<br>
识。以下是⼀些⽅法：<br>
1. 多任务学习：在预训练过程中，可以引⼊多个任务，使得模型能够学习到更多的知识。这些任务可<br>
以是领域相关的任务，也可以是通⽤的语⾔理解任务。通过同时训练多个任务，模型可以学习到更<br>
多的语⾔规律和知识。<br>
2. 多领域数据：收集来⾃不同领域的数据，包括⽬标领域和其他相关领域的数据。将这些数据混合在<br>
⼀起进⾏预训练，可以使得模型在不同领域的知识都得到学习和融合。<br>
3. ⼤规模数据：使⽤更⼤规模的数据进⾏预训练，可以让模型接触到更多的语⾔和知识。可以从互联<br>
⽹上爬取⼤量的⽂本数据，或者利⽤公开的语料库进⾏预训练。<br>
4. 数据增强：在预训练过程中，可以采⽤数据增强的技术，如随机遮挡、词替换、句⼦重组等，来⽣<br>
成更多的训练样本。这样可以增加模型的训练数据量，使其能够学习到更多的知识和语⾔规律。<br>
5. ⾃监督学习：引⼊⾃监督学习的⽅法，通过设计⼀些⾃动⽣成的标签或任务，让模型在⽆监督的情<br>
</p>

<h2>第 15 页</h2>

<p>况下进⾏预训练。例如，可以设计⼀个掩码语⾔模型任务，让模型预测被掩码的词语。这样可以使<br>
模型在预训练过程中学习到更多的语⾔知识。<br>
   综合使⽤上述⽅法，可以让模型在预训练过程中学习到更多的知识和语⾔规律，提升其在领域任务上<br>
的性能。<br>
   <br>
7. <br>
 进⾏SFT操作的时候，基座模型选⽤Chat还是Base?<br>
   在进⾏Supervised Fine-Tuning（SFT）操作时，基座模型的选择也可以根据具体情况来决定。与之前<br>
的SFT操作不同，这次的⽬标是在特定的监督任务上进⾏微调，因此选择基座模型时需要考虑任务的性<br>
质和数据集的特点。<br>
   如果您的监督任务是对话⽣成相关的，⽐如⽣成对话回复或对话情感分类等，那么选择ChatGPT模型<br>
作为基座模型可能更合适。ChatGPT模型在对话⽣成任务上进⾏了专⻔的优化和训练，具有更好的对话<br>
交互能⼒。<br>
   然⽽，如果您的监督任务是单轮⽂本⽣成或⾮对话⽣成任务，那么选择Base GPT模型作为基座模型可<br>
能更合适。Base GPT模型在单轮⽂本⽣成和⾮对话⽣成任务上表现良好，可以提供更准确的⽂本⽣成<br>
能⼒。<br>
   总之，基座模型的选择应该根据监督任务的性质和数据集的特点进⾏权衡。如果任务是对话⽣成相关<br>
的，可以选择ChatGPT模型作为基座模型；如果任务是单轮⽂本⽣成或⾮对话⽣成，可以选择Base <br>
GPT模型作为基座模型。<br>
   <br>
8. <br>
 领域模型微调 指令&amp;数据输⼊格式 要求？<br>
   领域模型微调是指使⽤预训练的通⽤语⾔模型（如BERT、GPT等）对特定领域的数据进⾏微调，以适<br>
应该领域的任务需求。以下是领域模型微调的指令和数据输⼊格式的要求：<br>
   指令：<br>
1. 定义任务：明确所需的任务类型，如⽂本分类、命名实体识别、情感分析等。<br>
2. 选择预训练模型：根据任务需求选择适合的预训练模型，如BERT、GPT等。<br>
3. 准备微调数据：收集和标注与领域任务相关的数据，确保数据集具有代表性和多样性。<br>
4. 数据预处理：根据任务的要求，对数据进⾏预处理，例如分词、去除停⽤词、词⼲化等。<br>
5. 划分数据集：将数据集划分为训练集、验证集和测试集，⽤于模型的训练、验证和评估。<br>
6. 模型微调：使⽤预训练模型和微调数据对模型进⾏微调，调整超参数并进⾏训练。<br>
7. 模型评估：使⽤测试集评估微调后的模型的性能，计算适当的评估指标，如准确率、召回率等。<br>
8. 模型应⽤：将微调后的模型应⽤于实际任务，在新的输⼊上进⾏预测或⽣成。<br>
   数据输⼊格式要求：<br>
</p>

<h2>第 16 页</h2>

<p>1. 输⼊数据应以⽂本形式提供，每个样本对应⼀⾏。<br>
2. 对于分类任务，每个样本应包含⽂本和标签，可以使⽤制表符或逗号将⽂本和标签分隔开。<br>
3. 对于⽣成任务，每个样本只需包含⽂本即可。<br>
4. 对于序列标注任务，每个样本应包含⽂本和对应的标签序列，可以使⽤制表符或逗号将⽂本和标签<br>
序列分隔开。<br>
5. 数据集应以常⻅的⽂件格式（如⽂本⽂件、CSV⽂件、JSON⽂件等）保存，并确保数据的格式与<br>
模型输⼊的要求⼀致。<br>
   根据具体的任务和模型要求，数据输⼊格式可能会有所不同。在进⾏领域模型微调之前，建议仔细阅<br>
读所使⽤模型的⽂档和示例代码，以了解其具体的数据输⼊格式要求。<br>
   <br>
9. <br>
 领域模型微调 领域评测集 构建？<br>
   构建领域评测集的过程可以参考以下步骤：<br>
1. 收集数据：⾸先需要收集与⽬标领域相关的数据。这可以包括从互联⽹上爬取⽂本数据、使⽤已有<br>
的公开数据集或者通过与领域专家合作来获取数据。确保数据集具有代表性和多样性，能够涵盖领<br>
域中的各种情况和语境。<br>
2. 标注数据：对收集到的数据进⾏标注，以便⽤于评测模型的性能。标注可以根据任务类型来进⾏，<br>
如⽂本分类、命名实体识别、关系抽取等。标注过程可以由⼈⼯标注或者使⽤⾃动化⼯具进⾏，具<br>
体取决于数据集的规模和可⾏性。<br>
3. 划分数据集：将标注好的数据集划分为训练集、验证集和测试集。通常，训练集⽤于模型的训练，<br>
验证集⽤于调整超参数和模型选择，测试集⽤于最终评估模型的性能。划分数据集时要确保每个集<br>
合中的样本都具有代表性和多样性。<br>
4. 设计评测指标：根据任务类型和领域需求，选择合适的评测指标来评估模型的性能。例如，对于⽂<br>
本分类任务，可以使⽤准确率、召回率、F1值等指标来衡量模型的分类性能。<br>
5. 进⾏评测：使⽤构建好的评测集对微调后的模型进⾏评测。将评测集输⼊模型，获取模型的预测结<br>
果，并与标注结果进⾏⽐较，计算评测指标。<br>
6. 分析和改进：根据评测结果，分析模型在不同⽅⾯的表现，并根据需要进⾏模型的改进和调整。可<br>
以尝试不同的超参数设置、模型架构或优化算法，以提⾼模型的性能。<br>
   重复以上步骤，不断优化模型，直到达到满意的评测结果为⽌。<br>
   需要注意的是，构建领域评测集是⼀个耗时且需要专业知识的过程。在进⾏领域模型微调之前，建议<br>
与领域专家合作，确保评测集的质量和有效性。此外，还可以参考相关研究论⽂和公开数据集，以获取<br>
更多关于领域评测集构建的指导和经验。<br>
   <br>
10. <br>
 领域模型词表扩增是不是有必要的？<br>
</p>

<h2>第 17 页</h2>

<p>11. <br>
 如何训练⾃⼰的⼤模型？<br>
领域模型的词表扩增可以有助于提升模型在特定领域任务上的性能，但是否有必要取决于具体的情况。<br>
以下是⼀些考虑因素：<br>
1. 领域特定词汇：如果⽬标领域中存在⼀些特定的词汇或术语，⽽这些词汇在通⽤的预训练模型的词<br>
表中没有覆盖到，那么词表扩增就是必要的。通过将这些领域特定的词汇添加到模型的词表中，可以使<br>
模型更好地理解和处理这些特定的词汇。<br>
2. 领域特定上下⽂：在某些领域任务中，词汇的含义可能会受到特定上下⽂的影响。例如，在医学领<br>
域中，同⼀个词汇在不同的上下⽂中可能具有不同的含义。如果领域任务中的上下⽂与通⽤预训练模型<br>
的训练数据中的上下⽂有较⼤差异，那么词表扩增可以帮助模型更好地理解和处理领域特定的上下⽂。<br>
3. 数据稀缺性：如果⽬标领域的训练数据相对较少，⽽通⽤预训练模型的词表较⼤，那么词表扩增可<br>
以帮助模型更好地利⽤预训练模型的知识，并提升在⽬标领域任务上的性能。<br>
需要注意的是，词表扩增可能会增加模型的计算和存储成本。因此，在决定是否进⾏词表扩增时，需要<br>
综合考虑领域特定词汇的重要性、数据稀缺性以及计算资源的限制等因素。有时候，简单的词表截断或<br>
者使⽤基于规则的⽅法来处理领域特定词汇也可以取得不错的效果。最佳的词表扩增策略会因特定任务<br>
和领域的需求⽽有所不同，建议根据具体情况进⾏评估和实验。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 18 页</h2>

<p>12. <br>
 训练中⽂⼤模型有啥经验？<br>
训练⾃⼰的⼤模型通常需要以下步骤：<br>
1. 数据收集和准备：⾸先，需要收集与⽬标任务和领域相关的⼤规模数据集。这可以包括从互联⽹上<br>
爬取数据、使⽤公开数据集或者与合作伙伴合作获取数据。然后，对数据进⾏预处理和清洗，包括去除<br>
噪声、处理缺失值、标准化数据等。<br>
2. 模型设计和架构选择：根据任务的特点和⽬标，选择适合的模型架构。可以基于已有的模型进⾏修<br>
改和调整，或者设计全新的模型。常⻅的⼤模型架构包括深度神经⽹络（如卷积神经⽹络、循环神经⽹<br>
络、Transformer等）和预训练语⾔模型（如BERT、GPT等）。<br>
3. 数据划分和预处理：将数据集划分为训练集、验证集和测试集。训练集⽤于模型的训练，验证集⽤<br>
于调整超参数和模型选择，测试集⽤于最终评估模型的性能。进⾏数据预处理，如分词、编码、标记<br>
化、特征提取等，以便输⼊到模型中。<br>
4. 模型训练：使⽤训练集对模型进⾏训练。训练过程中，需要选择合适的优化算法、损失函数和学习<br>
率等超参数，并进⾏适当的调整和优化。可以使⽤GPU或者分布式训练来加速训练过程。<br>
5. 模型调优和验证：使⽤验证集对训练过程中的模型进⾏调优和验证。根据验证集的性能指标，调整<br>
模型的超参数、⽹络结构或者其他相关参数，以提升模型的性能。<br>
6. 模型评估和测试：使⽤测试集对最终训练好的模型进⾏评估和测试。计算模型的性能指标，如准确<br>
率、召回率、F1值等，评估模型的性能和泛化能⼒。<br>
7. 模型部署和优化：将训练好的模型部署到实际应⽤中。根据实际需求，对模型进⾏进⼀步的优化和<br>
调整，以提⾼模型的效率和性能。<br>
需要注意的是，训练⾃⼰的⼤模型通常需要⼤量的计算资源和时间。可以考虑使⽤云计算平台或者分布<br>
式训练来加速训练过程。此外，对于⼤模型的训练，还需要仔细选择合适的超参数和进⾏调优，以避免<br>
过拟合或者⽋拟合的问题。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 19 页</h2>

<p>13. <br>
 指令微调的好处？<br>
训练中⽂⼤模型时，以下经验可能会有所帮助：<br>
1. 数据预处理：对于中⽂⽂本，常⻅的预处理步骤包括分词、去除停⽤词、词性标注、拼⾳转换等。<br>
分词是中⽂处理的基本步骤，可以使⽤成熟的中⽂分词⼯具，如jieba、pkuseg等。<br>
2. 数据增强：中⽂数据集可能相对有限，可以考虑使⽤数据增强技术来扩充数据集。例如，可以使⽤<br>
同义词替换、随机插⼊或删除词语、句⼦重组等⽅法来⽣成新的训练样本。<br>
3. 字词级别的表示：中⽂中既有字级别的表示，也有词级别的表示。对于字级别的表示，可以使⽤字<br>
符嵌⼊或者字级别的CNN、RNN等模型。对于词级别的表示，可以使⽤预训练的词向量，如<br>
Word2Vec、GloVe等。<br>
4. 预训练模型：可以考虑使⽤已经在⼤规模中⽂语料上预训练好的模型作为初始模型，然后在⽬标任<br>
务上进⾏微调。例如，可以使⽤BERT、GPT等预训练语⾔模型。这样可以利⽤⼤规模中⽂语料的信息，<br>
提升模型的表达能⼒和泛化能⼒。<br>
5. 中⽂特定的任务：对于⼀些中⽂特定的任务，例如中⽂分词、命名实体识别、情感分析等，可以使<br>
⽤⼀些中⽂特定的⼯具或者模型来辅助训练。例如，可以使⽤THULAC、LTP等中⽂NLP⼯具包。<br>
6. 计算资源：训练⼤模型需要⼤量的计算资源，包括GPU、内存和存储。可以考虑使⽤云计算平台或<br>
者分布式训练来加速训练过程。<br>
7. 超参数调优：对于⼤模型的训练，超参数的选择和调优⾮常重要。可以使⽤⽹格搜索、随机搜索或<br>
者基于优化算法的⾃动调参⽅法来寻找最佳的超参数组合。<br>
需要注意的是，中⽂的复杂性和语义特点可能会对模型的训练和性能产⽣影响。因此，在训练中⽂⼤模<br>
型时，需要充分理解中⽂语⾔的特点，并根据具体任务和需求进⾏调整和优化。同时，也可以参考相关<br>
的中⽂⾃然语⾔处理研究和实践经验，以获取更多的指导和启发。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 20 页</h2>

<p>14. <br>
 预训练和微调哪个阶段注⼊知识的？<br>
在⼤模型训练中进⾏指令微调（Instruction Fine-tuning）的好处包括：<br>
1. 个性化适应：⼤模型通常是在⼤规模通⽤数据上进⾏训练的，具有强⼤的语⾔理解和表示能⼒。但<br>
是，对于某些特定任务或领域，模型可能需要更加个性化的适应。通过指令微调，可以在⼤模型的基础<br>
上，使⽤特定任务或领域的数据进⾏微调，使模型更好地适应⽬标任务的特点。<br>
2. 提升性能：⼤模型的泛化能⼒通常很强，但在某些特定任务上可能存在⼀定的性能瓶颈。通过指令<br>
微调，可以针对特定任务的要求，调整模型的参数和结构，以提升性能。例如，在机器翻译任务中，可<br>
以通过指令微调来调整注意⼒机制、解码器结构等，以提⾼翻译质量。<br>
3. 控制模型⾏为：⼤模型通常具有很⾼的复杂性和参数数量，其⾏为可能难以解释和控制。通过指令<br>
微调，可以引⼊特定的指令或约束，以约束模型的⾏为，使其更符合特定任务的需求。例如，在⽣成式<br>
任务中，可以使⽤基于指令的⽅法来控制⽣成结果的⻛格、⻓度等。<br>
4. 数据效率：⼤模型的训练通常需要⼤量的数据，但在某些任务或领域中，特定数据可能相对稀缺或<br>
难以获取。通过指令微调，可以利⽤⼤模型在通⽤数据上的预训练知识，结合少量特定任务数据进⾏微<br>
调，从⽽在数据有限的情况下获得更好的性能。<br>
5. 提⾼训练效率：⼤模型的训练通常需要⼤量的计算资源和时间。通过指令微调，可以在已经训练好<br>
的⼤模型的基础上进⾏微调，避免从头开始训练的时间和资源消耗，从⽽提⾼训练效率。<br>
指令微调的好处在于在⼤模型的基础上进⾏个性化调整，以适应特定任务的需求和提升性能，同时还能<br>
节省训练时间和资源消耗。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 21 页</h2>

<p>15. <br>
 想让模型学习某个领域或⾏业的知识，是应该预训练还是应该微调？<br>
在⼤模型训练过程中，知识注⼊通常是在预训练阶段进⾏的。具体来说，⼤模型的训练⼀般包括两个阶<br>
段：预训练和微调。<br>
在预训练阶段，使⽤⼤规模的通⽤数据对模型进⾏训练，以学习语⾔知识和表示能⼒。这⼀阶段的⽬标<br>
是通过⾃监督学习或其他⽆监督学习⽅法，让模型尽可能地捕捉到数据中的统计规律和语⾔结构，并⽣<br>
成丰富的语⾔表示。<br>
在预训练阶段，模型并没有针对特定任务进⾏优化，因此预训练模型通常是通⽤的，可以应⽤于多个不<br>
同的任务和领域。<br>
在微调阶段，使⽤特定任务的数据对预训练模型进⾏进⼀步的训练和调整。微调的⽬标是将预训练模型<br>
中学到的通⽤知识和能⼒迁移到特定任务上，提升模型在⽬标任务上的性能。<br>
在微调阶段，可以根据具体任务的需求，调整模型的参数和结构，以更好地适应⽬标任务的特点。微调<br>
通常需要较少的任务数据，因为预训练模型已经具备了⼀定的语⾔理解和泛化能⼒。<br>
因此，知识注⼊是在预训练阶段进⾏的，预训练模型通过⼤规模通⽤数据的训练，学习到了丰富的语⾔<br>
知识和表示能⼒，为后续的微调阶段提供了基础。微调阶段则是在预训练模型的基础上，使⽤特定任务<br>
的数据进⾏进⼀步训练和调整，以提升性能。<br>
&lt;/aside&gt;<br>
如果你想让⼤语⾔模型学习某个特定领域或⾏业的知识，通常建议进⾏微调⽽不是预训练。<br>
预训练阶段是在⼤规模通⽤数据上进⾏的，旨在为模型提供通⽤的语⾔理解和表示能⼒。预训练模型通<br>
常具有较强的泛化能⼒，可以适⽤于多个不同的任务和领域。然⽽，由于预训练模型是在通⽤数据上进<br>
⾏训练的，其对特定领域的知识和术语可能了解有限。<br>
因此，如果你希望⼤语⾔模型能够学习某个特定领域或⾏业的知识，微调是更合适的选择。在微调阶<br>
段，你可以使⽤特定领域的数据对预训练模型进⾏进⼀步训练和调整，以使模型更好地适应⽬标领域的<br>
特点和需求。微调可以帮助模型更深⼊地理解特定领域的术语、概念和语境，并提升在该领域任务上的<br>
性能。<br>
微调通常需要较少的任务数据，因为预训练模型已经具备了⼀定的语⾔理解和泛化能⼒。通过微调，你<br>
可以在预训练模型的基础上，利⽤特定领域的数据进⾏有针对性的调整，以使模型更好地适应⽬标领域<br>
的需求。<br>
总之，如果你希望⼤语⾔模型学习某个特定领域或⾏业的知识，建议进⾏微调⽽不是预训练。微调可以<br>
帮助模型更好地适应⽬标领域的特点和需求，并提升在该领域任务上的性能。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 22 页</h2>

<p>16. <br>
 多轮对话任务如何微调模型？<br>
17. <br>
 微调后的模型出现能⼒劣化，灾难性遗忘是怎么回事？<br>
微调⼤语⾔模型⽤于多轮对话任务时，可以采⽤以下步骤：<br>
1. 数据准备：收集或⽣成与⽬标对话任务相关的数据集。数据集应包含多轮对话的对话历史、当前对<br>
话回合的输⼊和对应的回答。<br>
2. 模型选择：选择⼀个合适的预训练模型作为基础模型。例如，可以选择GPT、BERT等⼤型语⾔模型<br>
作为基础模型。<br>
3. 任务特定层：为了适应多轮对话任务，需要在预训练模型上添加⼀些任务特定的层。这些层可以⽤<br>
于处理对话历史、上下⽂理解和⽣成回答等任务相关的操作。<br>
4. 微调过程：使⽤多轮对话数据集对预训练模型进⾏微调。微调的过程类似于监督学习，通过最⼩化<br>
模型在训练集上的损失函数来优化模型参数。可以使⽤常⻅的优化算法，如随机梯度下降（SGD）或<br>
Adam。<br>
5. 超参数调整：微调过程中需要选择合适的学习率、批次⼤⼩、训练轮数等超参数。可以通过交叉验<br>
证或其他调参⽅法来选择最佳的超参数组合。<br>
6. 评估和调优：使⽤验证集或开发集对微调后的模型进⾏评估。可以计算模型在多轮对话任务上的指<br>
标，如准确率、召回率、F1分数等，以选择最佳模型。<br>
7. 推理和部署：在微调后，可以使⽤微调后的模型进⾏推理和部署。将输⼊的多轮对话输⼊给模型，<br>
模型将⽣成对应的回答。<br>
需要注意的是，微调⼤语⾔模型⽤于多轮对话任务时，数据集的质量和多样性对模型性能⾄关重要。确<br>
保数据集包含各种对话场景和多样的对话历史，以提⾼模型的泛化能⼒和适应性。<br>
此外，还可以使⽤⼀些技巧来增强模型性能，如数据增强、对抗训练、模型融合等。这些技巧可以进⼀<br>
步提⾼模型在多轮对话任务上的表现。<br>
&lt;/aside&gt;<br>
灾难性遗忘（Catastrophic Forgetting）是指在模型微调过程中，当模型在新任务上进⾏训练<br>
时，可能会忘记之前学习到的知识，导致在旧任务上的性能下降。这种现象常⻅于神经⽹络模型的迁移<br>
学习或连续学习场景中。<br>
在微调⼤语⾔模型时，灾难性遗忘可能出现的原因包括：<br>
1. 数据分布差异：微调过程中使⽤的新任务数据与预训练数据或旧任务数据的分布存在差异。如果新<br>
任务的数据分布与预训练数据差异较⼤，模型可能会过度调整以适应新任务，导致旧任务上的性能下<br>
降。<br>
2. 参数更新冲突：微调过程中，对新任务进⾏训练时，模型参数可能会被更新，导致之前学习到的知<br>
识被覆盖或丢失。新任务的梯度更新可能会与旧任务的梯度更新发⽣冲突，导致旧任务的知识被遗忘。<br>
为了解决灾难性遗忘问题，可以尝试以下⽅法：<br>
</p>

<h2>第 23 页</h2>

<p>18. <br>
 微调模型需要多⼤显存？<br>
19. <br>
 ⼤模型LLM进⾏SFT操作的时候在学习什么？<br>
1. 重播缓冲区（Replay Buffer）：在微调过程中，使⽤⼀个缓冲区来存储旧任务的样本，然后将<br>
旧任务的样本与新任务的样本⼀起⽤于训练。这样可以保留旧任务的知识，减少灾难性遗忘的发⽣。<br>
2. 弹性权重共享（Elastic Weight Consolidation）：通过引⼊正则化项，限制模型参数的变<br>
动范围，以保护之前学习到的知识。这种⽅法可以在微调过程中平衡新任务和旧任务之间的重要性。<br>
3. 增量学习（Incremental Learning）：将微调过程分为多个阶段，每个阶段只微调⼀⼩部分参<br>
数。这样可以逐步引⼊新任务，减少参数更新的冲突，降低灾难性遗忘的⻛险。<br>
4. 多任务学习（Multi-Task Learning）：在微调过程中，同时训练多个相关任务，以提⾼模型的<br>
泛化能⼒和抗遗忘能⼒。通过共享模型参数，可以在不同任务之间传递知识，减少灾难性遗忘的影响。<br>
综上所述，灾难性遗忘是在模型微调过程中可能出现的问题。通过合适的⽅法和技术，可以减少灾难性<br>
遗忘的发⽣，保留之前学习到的知识，提⾼模型的整体性能。<br>
&lt;/aside&gt;<br>
微调⼤语⾔模型所需的显存⼤⼩取决于多个因素，包括模型的⼤⼩、批次⼤⼩、序列⻓度和训练过程中<br>
使⽤的优化算法等。<br>
对于⼤型语⾔模型，如GPT-2、GPT-3等，它们通常具有数亿或数⼗亿个参数，因此需要⼤量的显存来<br>
存储模型参数和梯度。⼀般来说，微调这些⼤型语⾔模型需要⾄少16GB以上的显存。<br>
此外，批次⼤⼩和序列⻓度也会对显存需求产⽣影响。较⼤的批次⼤⼩和较⻓的序列⻓度会占⽤更多的<br>
显存。如果显存不⾜以容纳整个批次或序列，可能需要减⼩批次⼤⼩或序列⻓度，或者使⽤分布式训练<br>
等策略来解决显存不⾜的问题。<br>
需要注意的是，显存需求还受到训练过程中使⽤的优化算法的影响。例如，如果使⽤梯度累积<br>
（Gradient Accumulation）来增加批次⼤⼩，可能需要更⼤的显存来存储累积的梯度。<br>
综上所述，微调⼤语⾔模型所需的显存⼤⼩取决于模型的⼤⼩、批次⼤⼩、序列⻓度和训练过程中使⽤<br>
的优化算法等因素。在进⾏微调之前，需要确保显存⾜够⼤以容纳模型和训练过程中的数据。如果显存<br>
不⾜，可以考虑减⼩批次⼤⼩、序列⻓度或使⽤分布式训练等策略来解决显存不⾜的问题。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 24 页</h2>

<p>20. <br>
 预训练和SFT操作有什么不同<br>
在⼤语⾔模型（LLM）进⾏有监督微调（Supervised Fine-Tuning）时，模型主要学习以下内容：<br>
1. 任务特定的标签预测：在有监督微调中，模型会根据给定的任务，学习预测相应的标签或⽬标。例<br>
如，对于⽂本分类任务，模型会学习将输⼊⽂本映射到正确的类别标签。<br>
2. 上下⽂理解和语⾔模式：⼤语⾔模型在预训练阶段已经学习到了⼤量的语⾔知识和模式。在有监督<br>
微调中，模型会利⽤这些学习到的知识来更好地理解任务相关的上下⽂，并捕捉语⾔中的各种模式和规<br>
律。<br>
3. 特征提取和表示学习：微调过程中，模型会通过学习任务相关的表示来提取有⽤的特征。这些特征<br>
可以帮助模型更好地区分不同的类别或进⾏其他任务相关的操作。<br>
4. 任务相关的优化：在有监督微调中，模型会通过反向传播和优化算法来调整模型参数，使得模型在<br>
给定任务上的性能最优化。模型会学习如何通过梯度下降来最⼩化损失函数，从⽽提⾼任务的准确性或<br>
其他性能指标。<br>
总的来说，有监督微调阶段主要通过任务特定的标签预测、上下⽂理解和语⾔模式、特征提取和表示学<br>
习以及任务相关的优化来进⾏学习。通过这些学习，模型可以适应特定的任务，并在该任务上表现出良<br>
好的性能。<br>
&lt;/aside&gt;<br>
⼤语⾔模型的预训练和有监督微调（Supervised Fine-Tuning）是两个不同的操作，它们在⽬标、<br>
数据和训练⽅式等⽅⾯存在⼀些区别。<br>
1. ⽬标：预训练的⽬标是通过⽆监督学习从⼤规模的⽂本语料库中学习语⾔模型的表示能⼒和语⾔知<br>
识。预训练的⽬标通常是通过⾃我预测任务，例如掩码语⾔模型（Masked Language Model，<br>
MLM）或下⼀句预测（Next Sentence Prediction，NSP）等，来训练模型。<br>
   有监督微调的⽬标是在特定的任务上进⾏训练，例如⽂本分类、命名实体识别等。在有监督微调<br>
中，模型会利⽤预训练阶段学到的语⾔表示和知识，通过有监督的⽅式调整模型参数，以适应特定任务<br>
的要求。<br>
2. 数据：在预训练阶段，⼤语⾔模型通常使⽤⼤规模的⽆标签⽂本数据进⾏训练，例如维基百科、⽹<br>
⻚⽂本等。这些数据没有特定的标签或任务信息，模型通过⾃我预测任务来学习语⾔模型。<br>
   在有监督微调中，模型需要使⽤带有标签的任务相关数据进⾏训练。这些数据通常是⼈⼯标注的，<br>
包含了输⼊⽂本和对应的标签或⽬标。模型通过这些标签来进⾏有监督学习，调整参数以适应特定任<br>
务。<br>
3. 训练⽅式：预训练阶段通常使⽤⽆监督的⽅式进⾏训练，模型通过最⼤化预训练任务的⽬标函数来<br>
学习语⾔模型的表示能⼒。<br>
</p>

<h2>第 25 页</h2>

<p>21. <br>
 样本量规模增⼤，训练出现OOM错<br>
22. <br>
 ⼤模型LLM进⾏SFT 如何对样本进⾏优化？<br>
   有监督微调阶段则使⽤有监督的⽅式进⾏训练，模型通过最⼩化损失函数来学习任务相关的特征和<br>
模式。在微调阶段，通常会使⽤预训练模型的参数作为初始参数，并在任务相关的数据上进⾏训练。<br>
总的来说，预训练和有监督微调是⼤语⾔模型训练的两个阶段，⽬标、数据和训练⽅式等⽅⾯存在差<br>
异。预训练阶段通过⽆监督学习从⼤规模⽂本数据中学习语⾔模型，⽽有监督微调阶段则在特定任务上<br>
使⽤带有标签的数据进⾏有监督学习，以适应任务要求。<br>
&lt;/aside&gt;<br>
当在⼤语⾔模型训练过程中，样本量规模增⼤导致内存不⾜的情况出现时，可以考虑以下⼏种解决⽅<br>
案：<br>
1. 减少批量⼤⼩（Batch Size）：将批量⼤⼩减⼩可以减少每个训练步骤中所需的内存量。较⼩的<br>
批量⼤⼩可能会导致训练过程中的梯度估计不稳定，但可以通过增加训练步骤的数量来弥补这⼀问题。<br>
2. 分布式训练：使⽤多台机器或多个GPU进⾏分布式训练可以将训练负载分散到多个设备上，从⽽减<br>
少单个设备上的内存需求。通过分布式训练，可以将模型参数和梯度在多个设备之间进⾏同步和更新。<br>
3. 内存优化技术：使⽤⼀些内存优化技术可以减少模型训练过程中的内存占⽤。例如，使⽤混合精度<br>
训练（Mixed Precision Training）可以减少模型参数的内存占⽤；使⽤梯度累积（Gradient <br>
Accumulation）可以减少每个训练步骤中的内存需求。<br>
4. 减少模型规模：如果内存问题仍然存在，可以考虑减少模型的规模，例如减少模型的层数、隐藏单<br>
元的数量等。虽然这可能会导致模型性能的⼀定损失，但可以在⼀定程度上减少内存需求。<br>
5. 增加硬件资源：如果条件允许，可以考虑增加硬件资源，例如增加内存容量或使⽤更⾼内存的设<br>
备。这样可以提供更多的内存空间来容纳更⼤规模的训练数据。<br>
6. 数据处理和加载优化：优化数据处理和加载过程可以减少训练过程中的内存占⽤。例如，可以使⽤<br>
数据流⽔线技术来并⾏加载和处理数据，减少内存中同时存在的数据量。<br>
综上所述，当在⼤语⾔模型训练中遇到内存不⾜的问题时，可以通过减⼩批量⼤⼩、分布式训练、内存<br>
优化技术、减少模型规模、增加硬件资源或优化数据处理等⽅式来解决。具体的解决⽅案需要根据具体<br>
情况进⾏选择和调整。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 26 页</h2>

<p>23. <br>
 模型参数迭代实验<br>
对于⼤语⾔模型进⾏有监督微调（Supervised Fine-Tuning）时，可以采⽤以下⼏种⽅式对样本进<br>
⾏优化：<br>
1. 数据清洗和预处理：对于有监督微调的任务，⾸先需要对样本数据进⾏清洗和预处理。这包括去除<br>
噪声、处理缺失值、进⾏标准化或归⼀化等操作，以确保数据的质量和⼀致性。<br>
2. 数据增强：通过数据增强技术可以扩充训练数据，增加样本的多样性和数量。例如，可以使⽤数据<br>
扩充⽅法如随机裁剪、旋转、翻转、加噪声等来⽣成新的训练样本，从⽽提⾼模型的泛化能⼒。<br>
3. 标签平衡：如果样本标签不平衡，即某些类别的样本数量远远多于其他类别，可以采取⼀些⽅法来<br>
平衡样本标签。例如，可以通过⽋采样、过采样或⽣成合成样本等技术来平衡不同类别的样本数量。<br>
4. 样本选择：在有限的资源和时间下，可以选择⼀部分具有代表性的样本进⾏微调训练。可以根据任<br>
务的需求和数据分布的特点，选择⼀些关键样本或难样本进⾏训练，以提⾼模型在关键样本上的性能。<br>
5. 样本权重：对于⼀些重要的样本或困难样本，可以给予更⾼的权重，以便模型更加关注这些样本的<br>
学习。可以通过调整损失函数中样本的权重或采⽤加权采样的⽅式来实现。<br>
6. 样本组合和分割：根据任务的特点和数据的结构，可以将多个样本组合成⼀个样本，或将⼀个样本<br>
分割成多个⼦样本。这样可以扩展训练数据，提供更多的信息和多样性。<br>
7. 样本筛选和策略：根据任务需求，可以制定⼀些样本筛选和选择策略。例如，可以根据样本的置信<br>
度、难度、多样性等指标进⾏筛选和选择，以提⾼模型的性能和泛化能⼒。<br>
总的来说，对⼤语⾔模型进⾏有监督微调时，可以通过数据清洗和预处理、数据增强、标签平衡、样本<br>
选择、样本权重、样本组合和分割、样本筛选和策略等⽅式对样本进⾏优化。这些优化⽅法可以提⾼训<br>
练样本的质量、多样性和数量，从⽽提升模型的性能和泛化能⼒。具体的优化策略需要根据任务需求和<br>
数据特点进⾏选择和调整。<br>
&lt;/aside&gt;<br>
模型参数迭代实验是指通过多次迭代更新模型参数，以逐步优化模型性能的过程。在实验中，可以尝试<br>
不同的参数更新策略、学习率调整⽅法、正则化技术等，以找到最佳的参数配置，从⽽达到更好的模型<br>
性能。<br>
下⾯是⼀个基本的模型参数迭代实验过程：<br>
1. 设定初始参数：⾸先，需要设定初始的模型参数。可以通过随机初始化或使⽤预训练模型的参数作<br>
为初始值。<br>
2. 选择损失函数：根据任务的特点，选择适当的损失函数作为模型的优化⽬标。常⻅的损失函数包括<br>
均⽅误差（MSE）、交叉熵损失等。<br>
3. 选择优化算法：选择适当的优化算法来更新模型参数。常⻅的优化算法包括随机梯度下降<br>
（SGD）、Adam、Adagrad等。可以尝试不同的优化算法，⽐较它们在模型训练过程中的效果。<br>
4. 划分训练集和验证集：将样本数据划分为训练集和验证集。训练集⽤于模型参数的更新，验证集⽤<br>
于评估模型性能和调整超参数。<br>
5. 迭代更新参数：通过多次迭代更新模型参数来优化模型。每次迭代中，使⽤训练集的⼀批样本进⾏<br>
前向传播和反向传播，计算损失函数并更新参数。可以根据需要调整批量⼤⼩、学习率等超参数。<br>
</p>

<h2>第 27 页</h2>

<p>⼤模型（LLMs）训练经验帖<br>
 <br>
分布式训练框架选择？<br>
LLMs 训练时 有哪些有⽤的建议？<br>
模型⼤⼩如何选择？<br>
加速卡如何选择？<br>
⼤模型（LLMs）langchain ⾯<br>
 <br>
⼤模型（LLMs）langchain ⾯<br>
 <br>
1. 什么是 LangChain?<br>
2. LangChain 包含哪些 核⼼概念？<br>
2.1 LangChain 中 Components and Chains 是什么？<br>
2.2 LangChain 中 Prompt Templates and Values 是什么？<br>
2.3 LangChain 中 Example Selectors 是什么？<br>
2.4 LangChain 中 Output Parsers 是什么？<br>
2.5 LangChain 中 Indexes and Retrievers 是什么？<br>
2.6 LangChain 中  Chat Message History 是什么？<br>
2.7 LangChain 中  Agents and Toolkits 是什么？<br>
3. 什么是 LangChain Agent?<br>
4. 如何使⽤ LangChain ?<br>
5. LangChain ⽀持哪些功能?<br>
6. 什么是 LangChain model?<br>
7. LangChain 包含哪些特点?<br>
8. LangChain 如何使⽤?<br>
8.1 LangChain 如何调⽤ LLMs ⽣成回复？<br>
8.2 LangChain 如何修改 提示模板？<br>
8.3 LangChain 如何链接多个组件处理⼀个特定的下游任务？<br>
6. 评估模型性能：在每次迭代的过程中，可以使⽤验证集评估模型的性能。可以计算准确率、精确<br>
率、召回率、F1值等指标，以及绘制学习曲线、混淆矩阵等来分析模型的性能。<br>
7. 调整超参数：根据验证集的评估结果，可以调整超参数，如学习率、正则化系数等，以进⼀步提升<br>
模型性能。可以使⽤⽹格搜索、随机搜索等⽅法来寻找最佳的超参数配置。<br>
8. 终⽌条件：可以设置终⽌条件，如达到最⼤迭代次数、模型性能不再提升等。当满⾜终⽌条件时，<br>
结束模型参数迭代实验。<br>
通过模型参数迭代实验，可以逐步优化模型性能，找到最佳的参数配置。在实验过程中，需要注意过拟<br>
合和⽋拟合等问题，并及时调整模型结构和正则化技术来解决。同时，要进⾏合理的实验设计和结果分<br>
析，以得到可靠的实验结论。<br>
&lt;/aside&gt;<br>
&lt;aside&gt;<br>
</p>

<h2>第 28 页</h2>

<p>8.4 LangChain 如何Embedding &amp; vector store？<br>
LangChain 存在哪些问题及⽅法⽅案？<br>
1. LangChain 低效的令牌使⽤问题<br>
2. LangChain ⽂档的问题<br>
3. LangChain 太多概念容易混淆，过多的“辅助”函数问题<br>
4. LangChain ⾏为不⼀致并且隐藏细节问题<br>
5. LangChain 缺乏标准的可互操作数据类型问题<br>
LangChain 替代⽅案？<br>
 <br>
 <br>
基于LLM+向量库的⽂档对话 经验⾯<br>
 <br>
⼀、基于LLM+向量库的⽂档对话 基础⾯<br>
1.1 为什么 ⼤模型 需要 外挂(向量)知识库？<br>
1.2. 基于LLM+向量库的⽂档对话 思路是怎么样？<br>
1.3. 基于LLM+向量库的⽂档对话 核⼼技术是什么？<br>
1.4. 基于LLM+向量库的⽂档对话 prompt 模板 如何构建？<br>
⼆、基于LLM+向量库的⽂档对话 存在哪些痛点？<br>
三、基于LLM+向量库的⽂档对话 ⼯程示例⾯<br>
基于LLM+向量库的⽂档对话经验⾯<br>
 <br>
1. 基于LLM+向量库的⽂档对话 基础⾯<br>
1. <br>
 LLMs 存在模型幻觉问题，请问如何处理？<br>
</p>

<h2>第 29 页</h2>

<p>2. <br>
 基于LLM+向量库的⽂档对话 思路是怎么样？<br>
  ⼤语⾔模型的模型幻觉问题是指其可能⽣成看似合理但实际上不准确或不符合事实的内容。这是由于<br>
⼤语⾔模型在训练过程中接触到的数据源的偏差、噪声或错误所导致的。处理⼤语⾔模型的模型幻觉问<br>
题需要采取⼀些⽅法和策略，以下是⼀些建议：<br>
  1. 数据清洗和预处理：在训练⼤语⾔模型之前，对数据进⾏仔细的清洗和预处理是⾄关重要的。删<br>
除不准确、噪声或有偏差的数据可以减少模型幻觉问题的出现。<br>
  2. 多样化训练数据：为了减少模型对特定数据源的依赖和偏好，可以尽量使⽤多样化的训练数据。<br>
包括来⾃不同领域、不同来源和不同观点的数据，以获得更全⾯的语⾔理解。<br>
  3. 引⼊多样性的⽣成策略：在⽣成⽂本时，可以采⽤多样性的⽣成策略来减少模型的倾向性和幻觉<br>
问题。例如，使⽤温度参数来调整⽣成的多样性，或者使⽤抽样和束搜索等不同的⽣成⽅法。<br>
  4. ⼈⼯审核和后处理：对⽣成的⽂本进⾏⼈⼯审核和后处理是⼀种常⽤的⽅法。通过⼈⼯的⼲预和<br>
修正，可以纠正模型幻觉问题，并确保⽣成的内容准确和可靠。<br>
  5. 引⼊外部知识和约束：为了提⾼⽣成⽂本的准确性，可以引⼊外部知识和约束。例如，结合知识<br>
图谱、实体识别或逻辑推理等技术，将先验知识和约束融⼊到⽣成过程中。<br>
  这些⽅法可以帮助减少⼤语⾔模型的模型幻觉问题，但并不能完全消除。因此，在使⽤⼤语⾔模型<br>
时，仍然需要谨慎评估⽣成结果的准确性和可靠性，并结合⼈⼯的审核和后处理来确保⽣成内容的质<br>
量。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 30 页</h2>

<p>3. <br>
 基于LLM+向量库的⽂档对话 核⼼技术是什么？<br>
  基于⼤语⾔模型和向量库的⽂档对话可以通过以下实现思路：<br>
  1. 数据预处理：⾸先，需要对⽂档数据进⾏预处理。这包括分词、去除停⽤词、词⼲化等步骤，以<br>
准备⽂档数据⽤于后续的向量化和建模。<br>
  2. ⽂档向量化：使⽤向量库的⽅法，将每个⽂档表示为⼀个向量。常⻅的向量化⽅法包括TF-<br>
IDF、Word2Vec、Doc2Vec等。这些⽅法可以将⽂档转换为数值向量，以便计算⽂档之间的相似度或<br>
进⾏聚类分析。<br>
  3. ⼤语⾔模型训练：使⽤⼤语⾔模型，如GPT、BERT等，对⽂档数据进⾏训练。这样可以使模型学<br>
习到⽂档之间的语义关系和上下⽂信息。<br>
  4. ⽂档检索：当⽤户提供⼀个查询⽂本时，⾸先对查询⽂本进⾏向量化，然后计算查询向量与⽂档<br>
向量之间的相似度。可以使⽤余弦相似度或其他相似度度量⽅法来衡量它们之间的相似程度。根据相似<br>
度排序，返回与查询⽂本最相关的⽂档。<br>
  5. ⽂档推荐：除了简单的⽂档检索，还可以使⽤⼤语⾔模型⽣成推荐⽂档。通过输⼊⽤户的查询⽂<br>
本，使⽤⼤语⾔模型⽣成与查询相关的⽂本⽚段或摘要，并根据这些⽣成的⽂本⽚段推荐相关的⽂档。<br>
  6. 对话交互：在⽂档对话系统中，⽤户可以提供多个查询⽂本，并根据系统的回复进⾏进⼀步的对<br>
话交互。可以使⽤⼤语⾔模型⽣成系统的回复，并根据⽤户的反馈进⾏迭代和改进。<br>
  通过以上实现思路，可以构建⼀个基于⼤语⾔模型和向量库的⽂档对话系统，使⽤户能够⽅便地进⾏<br>
⽂档检索、推荐和对话交互。具体的实现细节和技术选择会根据具体的应⽤场景和需求来确定。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 31 页</h2>

<p>4. <br>
 基于LLM+向量库的⽂档对话 prompt 模板 如何构建？<br>
  基于⼤语⾔模型和向量库的⽂档对话的核⼼技术包括以下⼏个⽅⾯：<br>
  1. ⼤语⾔模型：⼤语⾔模型是指能够理解和⽣成⼈类语⾔的深度学习模型，如GPT、BERT等。这些<br>
模型通过在⼤规模⽂本数据上进⾏预训练，学习到语⾔的语义和上下⽂信息。在⽂档对话系统中，⼤语<br>
⾔模型可以⽤于⽣成回复、推荐相关⽂档等任务。<br>
  2. ⽂档向量化：⽂档向量化是将⽂档表示为数值向量的过程。这可以使⽤向量库技术，如TF-<br>
IDF、Word2Vec、Doc2Vec等。⽂档向量化的⽬的是将⽂档转换为计算机可以处理的数值形式，以便<br>
计算⽂档之间的相似度或进⾏其他⽂本分析任务。<br>
  3. 相似度计算：相似度计算是⽂档对话系统中的重要技术。通过计算查询⽂本向量与⽂档向量之间<br>
的相似度，可以实现⽂档的检索和推荐。常⻅的相似度计算⽅法包括余弦相似度、欧⽒距离等。<br>
  4. 对话⽣成：对话⽣成是指根据⽤户的查询⽂本⽣成系统的回复或推荐⽂档。这可以使⽤⼤语⾔模<br>
型来⽣成⾃然语⾔的回复。⽣成的回复可以基于查询⽂本的语义和上下⽂信息，以提供准确和有意义的<br>
回复。<br>
  5. 对话交互：对话交互是指⽤户和系统之间的交互过程。⽤户可以提供查询⽂本，系统根据查询⽂<br>
本⽣成回复，⽤户再根据回复提供进⼀步的查询或反馈。对话交互可以通过迭代和反馈来改进系统的回<br>
复和推荐。<br>
  这些技术共同构成了基于⼤语⾔模型和向量库的⽂档对话系统的核⼼。通过结合这些技术，可以实现<br>
⽂档的检索、推荐和对话交互，提供更智能和个性化的⽂档服务。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 32 页</h2>

<p>2. 基于LLM+向量库的⽂档对话 优化⾯<br>
1. <br>
 痛点1：⽂档切分粒度不好把控，既担⼼噪声太多⼜担⼼语义信息丢失<br>
  构建基于⼤语⾔模型和向量库的⽂档对话的prompt模板可以考虑以下⼏个⽅⾯：<br>
  1. 查询类型：⾸先确定⽤户可能的查询类型，例如问题查询、主题查询、摘要查询等。针对不同的<br>
查询类型，可以构建相应的prompt模板。例如，对于问题查询，可以使⽤"我有⼀个关于XXX的问<br>
题"作为模板；对于主题查询，可以使⽤"我想了解关于XXX的信息"作为模板。<br>
  2. 查询内容：根据⽂档的特点和领域知识，确定⽤户可能会查询的内容。例如，对于新闻⽂档，查<br>
询内容可以包括新闻标题、关键词、时间范围等；对于学术论⽂，查询内容可以包括作者、论⽂标题、<br>
摘要等。根据查询内容，可以构建相应的prompt模板。例如，对于查询新闻标题的情况，可以使⽤"请<br>
问有关于XXX的新闻吗？"作为模板。<br>
  3. 上下⽂信息：考虑上下⽂信息对于查询的影响。⽤户之前的查询或系统的回复可能会影响当前的<br>
查询。可以将上下⽂信息加⼊到prompt模板中，以便更好地理解⽤户的意图。例如，对于上⼀轮的回<br>
复是关于某个主题的，可以使⽤"我还有关于上次谈到的XXX的问题"作为模板。<br>
  4. 可变参数：考虑到⽤户的查询可能有不同的变化，可以在prompt模板中留出⼀些可变的参数，<br>
以便根据具体查询进⾏替换。例如，可以使⽤"我想了解关于XXX的信息"作为模板，其中的XXX可以根<br>
据⽤户的查询进⾏替换。<br>
  通过这些⽅⾯的考虑，可以构建多个不同的prompt模板，以满⾜不同类型和内容的查询需求。在实<br>
际应⽤中，可以根据具体的场景和数据进⾏调整和优化，以提供更准确和有针对性的查询模板。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 33 页</h2>

<p>2. <br>
 痛点2：在基于垂直领域 表现不佳<br>
  在基于⼤语⾔模型和向量库的⽂档对话中，确实需要在⽂档切分的粒度上进⾏权衡。如果切分得太<br>
细，可能会引⼊较多的噪声；如果切分得太粗，可能会丢失⼀些重要的语义信息。以下是⼀些解决⽅<br>
案：<br>
  1. 预处理和过滤：在进⾏⽂档切分之前，可以进⾏⼀些预处理和过滤操作，以减少噪声的影响。例<br>
如，可以去除⽂档中的停⽤词、标点符号、特殊字符等，以及进⾏拼写纠错和词形还原等操作。这样可<br>
以降低噪声的存在，提⾼⽂档切分的质量。<br>
  2. 主题建模：可以使⽤主题建模技术，如LDA（Latent Dirichlet Allocation）等，对⽂档<br>
进⾏主题抽取。通过识别⽂档的主题，可以帮助确定⽂档切分的粒度。例如，将同⼀主题下的⽂档划分<br>
为⼀个切分单元，以保留更多的语义信息。<br>
  3. 上下⽂信息：在进⾏⽂档切分时，考虑上下⽂信息对于语义的影响。例如，将与上⼀⽂档相关联<br>
的⽂档划分为⼀个切分单元，以保留上下⽂的连贯性和语义关联。这样可以更好地捕捉⽂档之间的语义<br>
信息。<br>
  4. 动态切分：可以采⽤动态切分的⽅式，根据⽤户的查询和需要，实时⽣成切分单元。例如，根据<br>
⽤户的关键词或查询意图，动态⽣成包含相关信息的切分单元，以减少噪声和提⾼语义的准确性。<br>
  5. 实验和优化：在实际应⽤中，可以进⾏⼀系列的实验和优化，通过不断调整和评估⽂档切分的效<br>
果。可以尝试不同的切分粒度，评估其噪声和语义信息的平衡。通过实验和优化，逐步找到合适的⽂档<br>
切分策略。<br>
  综上所述，解决⽂档切分粒度的问题需要综合考虑预处理、主题建模、上下⽂信息、动态切分等多个<br>
因素，并通过实验和优化来找到最佳的平衡点，以保留⾜够的语义信息同时减少噪声的影响。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 34 页</h2>

<p>3. <br>
 痛点3：langchain 内置 问答分句效果不佳问题<br>
  如果在垂直领域中，基于LLM（Language Model + Retrieval）和向量库的⽂档对话表现不<br>
佳，可以考虑以下⽅法来改进：<br>
  1. 针对垂直领域进⾏领域特定训练：LLM模型是基于⼤规模通⽤语料库进⾏训练的，可能⽆法充分<br>
捕捉垂直领域的特点和术语。可以使⽤领域特定的语料库对LLM模型进⾏微调或重新训练，以提⾼在垂<br>
直领域的表现。<br>
  2. 增加领域知识：在向量库中，可以添加垂直领域的专业知识，如领域术语、实体名词等。这样可<br>
以提⾼向量库中⽂档的表示能⼒，使其更适应垂直领域的对话需求。<br>
  3. 优化检索算法：在使⽤向量库进⾏⽂档检索时，可以尝试不同的检索算法和相似度计算⽅法。常<br>
⽤的算法包括余弦相似度、BM25等。通过调整参数和算法选择，可以提⾼检索的准确性和相关性。<br>
  4. 数据增强和样本平衡：在训练LLM模型时，可以增加垂直领域的样本数据，以增加模型对垂直领<br>
域的理解和表达能⼒。同时，要注意样本的平衡，确保训练数据中包含各个垂直领域的典型对话场景，<br>
避免偏向某个特定领域。<br>
  5. 引⼊外部知识库：在垂直领域的对话中，可以结合外部的领域知识库，如专业词典、⾏业标准<br>
等，来提供更准确的答案和解决⽅案。通过与外部知识库的结合，可以弥补LLM模型和向量库在垂直领<br>
域中的不⾜。<br>
  6. 收集⽤户反馈和迭代优化：通过收集⽤户的反馈信息，了解⽤户对对话系统的需求和期望，并根<br>
据反馈进⾏迭代优化。持续改进和优化是提⾼垂直领域对话效果的关键。<br>
  总之，通过领域特定训练、增加领域知识、优化检索算法、数据增强和样本平衡、引⼊外部知识库以<br>
及收集⽤户反馈和迭代优化等⽅法，可以改进基于LLM和向量库的⽂档对话在垂直领域中的表现。这些<br>
⽅法可以根据具体情况灵活应⽤，以提⾼对话系统的准确性和适应性。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 35 页</h2>

<p>4. <br>
 痛点4：如何 尽可能召回与query相关的Document 问题<br>
  如果您在使⽤Langchain内置的问答分句功能时发现效果不佳，可以尝试以下⽅法来改善：<br>
  1. 调整输⼊：检查输⼊的⽂本是否符合预期的格式和结构。确保输⼊的句⼦和段落之间有明确的分<br>
隔符，如句号、问号或换⾏符。如果输⼊的⽂本结构不清晰，可能会导致分句效果不佳。<br>
  2. 引⼊标点符号：在⽂本中适当地引⼊标点符号，如句号、问号或感叹号，以帮助模型更好地理解<br>
句⼦的边界。标点符号可以提供明确的分句信号，有助于改善分句的准确性。<br>
  3. 使⽤⾃定义规则：针对特定的⽂本类型或语⾔，可以使⽤⾃定义规则来分句。例如，可以编写正<br>
则表达式或使⽤特定的分句库来处理特定的分句需求。这样可以更好地适应特定的语⾔和⽂本结构。<br>
  4. 结合其他⼯具：除了Langchain内置的问答分句功能，还可以结合其他分句⼯具或库来处理⽂<br>
本。例如，NLTK、spaCy等⾃然语⾔处理⼯具包中提供了强⼤的分句功能，可以与Langchain⼀起使<br>
⽤，以获得更好的分句效果。<br>
  5. 使⽤上下⽂信息：如果上下⽂信息可⽤，可以利⽤上下⽂信息来辅助分句。例如，可以根据上下<br>
⽂中的语境和语义信息来判断句⼦的边界，从⽽提⾼分句的准确性。<br>
  6. 收集反馈和调整模型：如果您发现Langchain内置的问答分句功能在特定场景下效果不佳，可<br>
以收集⽤户反馈，并根据反馈进⾏模型调整和改进。通过不断优化模型，可以逐渐改善分句效果。<br>
  总之，通过调整输⼊、引⼊标点符号、使⽤⾃定义规则、结合其他⼯具、使⽤上下⽂信息以及收集反<br>
馈和调整模型等⽅法，可以改善Langchain内置的问答分句效果。这些⽅法可以根据具体情况灵活使<br>
⽤，以提⾼分句的准确性和效果。<br>
  &lt;/aside&gt;<br>
  要尽可能召回与query相关的Document，可以采取以下⽅法：<br>
  1. 建⽴索引：将Document集合建⽴索引，以便能够快速检索和匹配相关的Document。可以使⽤<br>
搜索引擎或专业的信息检索⼯具，如Elasticsearch、Solr等。<br>
  2. 关键词匹配：通过对query和Document中的关键词进⾏匹配，筛选出包含相关关键词的<br>
Document。可以使⽤TF-IDF、BM25等算法来计算关键词的重要性和匹配程度。<br>
  3. 向量化表示：将query和Document转化为向量表示，通过计算它们之间的相似度来判断相关<br>
性。可以使⽤词嵌⼊模型（如Word2Vec、GloVe）或深度学习模型（如BERT、ELMo）来获取向量表<br>
示。<br>
  4. 上下⽂建模：考虑上下⽂信息，如query的前后⽂、Document的上下⽂等，以更准确地判断相<br>
关性。可以使⽤上下⽂编码器或注意⼒机制来捕捉上下⽂信息。<br>
  5. 扩展查询：根据query的特点，进⾏查询扩展，引⼊相关的同义词、近义词、词根变化等，以扩<br>
⼤相关Document的召回范围。<br>
  6. 语义匹配：使⽤语义匹配模型，如Siamese⽹络、BERT等，来计算query和Document之间的<br>
语义相似度，以更准确地判断相关性。<br>
  7. 实时反馈：利⽤⽤户的反馈信息，如点击、收藏、评分等，来优化召回结果。通过监控⽤户⾏<br>
为，不断调整和优化召回算法，提升相关Document的召回率。<br>
</p>

<h2>第 36 页</h2>

<p>5. <br>
 痛点5：如何让LLM基于query和context得到⾼质量的response<br>
3. 基于LLM+向量库的⽂档对话 ⼯程示例⾯<br>
1. 本地知识库问答系统（Langchain-chatGLM）<br>
1. 避坑记录<br>
  8. 多模态信息利⽤：如果有可⽤的多模态信息，如图像、视频等，可以将其整合到召回模型中，以<br>
提供更丰富、准确的相关Document。通过多模态信息的利⽤，可以增强召回模型的表达能⼒和准确<br>
性。<br>
  总之，通过建⽴索引、关键词匹配、向量化表示、上下⽂建模、查询扩展、语义匹配、实时反馈和多<br>
模态信息利⽤等⽅法，可以尽可能召回与query相关的Document。这些⽅法可以单独使⽤，也可以结<br>
合起来，以提⾼召回的准确性和覆盖率。<br>
  &lt;/aside&gt;<br>
  要让LLM基于query和context得到⾼质量的response，可以采取以下⽅法：<br>
  1. 数据准备：准备⼤量⾼质量的训练数据，包括query、context和对应的⾼质量response。确<br>
保数据的多样性和覆盖性，以提供更好的训练样本。<br>
  2. 模型架构：选择合适的模型架构，如Transformer等，以便提取query和context中的重要信<br>
息，并⽣成相应的⾼质量response。确保模型具有⾜够的容量和复杂性，以适应各种复杂的查询和上<br>
下⽂。<br>
  3. 微调和优化：使⽤预训练的模型作为起点，通过在特定任务上进⾏微调和优化，使模型能够更好<br>
地理解query和context，并⽣成更准确、连贯的response。可以使⽤基于强化学习的⽅法，如强化<br>
对抗学习，来进⼀步提⾼模型的表现。<br>
  4. 上下⽂建模：在LLM中，上下⽂对于⽣成⾼质量的response⾮常重要。确保模型能够准确地理<br>
解和利⽤上下⽂信息，以⽣成与之相关的response。可以使⽤⼀些技术，如注意⼒机制和上下⽂编码<br>
器，来帮助模型更好地建模上下⽂。<br>
  5. 评估和反馈：定期评估模型的性能，使⽤⼀些评估指标，如BLEU、ROUGE等，来衡量⽣成的<br>
response的质量。根据评估结果，及时调整和改进模型的训练策略和参数设置。同时，收集⽤户反馈<br>
和意⻅，以便进⼀步改进模型的性能。<br>
  6. 多模态信息利⽤：如果有可⽤的多模态信息，如图像、视频等，可以将其整合到LLM中，以提供<br>
更丰富、准确的response。利⽤多模态信息可以增强模型的理解能⼒和表达能⼒，从⽽⽣成更⾼质量<br>
的response。<br>
  7. 引⼊外部知识和资源：为了提⾼LLM的质量，可以引⼊外部知识和资源，如知识图谱、预训练的<br>
语⾔模型等。利⽤这些资源可以帮助模型更好地理解和回答query，从⽽⽣成更⾼质量的response。<br>
  总之，通过合适的数据准备、模型架构选择、微调和优化、上下⽂建模、评估和反馈、多模态信息利<br>
⽤以及引⼊外部知识和资源等⽅法，可以帮助LLM基于query和context得到⾼质量的response。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 37 页</h2>

<p>LLM⽂档对话 —— pdf解析关键问题<br>
 <br>
⼀、为什么需要进⾏pdf解析？<br>
⼆、为什么需要 对 pdf 进⾏解析？<br>
三、pdf解析 有哪些⽅法，对应的区别是什么？<br>
四、pdf解析 存在哪些问题？<br>
五、如何 ⻓⽂档（书籍）中关键信息？<br>
六、为什么要提取标题甚⾄是多级标题？<br>
七、如何提取 ⽂章标题？<br>
⼋、如何区分单栏还是双栏pdf？如何重新排序？<br>
九、如何提取表格和图⽚中的数据？<br>
⼗、基于AI的⽂档解析有什么优缺点？<br>
基于LLM+向量库的⽂档对话 经验⾯<br>
 <br>
⼀、基于LLM+向量库的⽂档对话 基础⾯<br>
1.1 为什么 ⼤模型 需要 外挂(向量)知识库？<br>
1.2. 基于LLM+向量库的⽂档对话 思路是怎么样？<br>
1.3. 基于LLM+向量库的⽂档对话 核⼼技术是什么？<br>
1.4. 基于LLM+向量库的⽂档对话 prompt 模板 如何构建？<br>
⼆、基于LLM+向量库的⽂档对话 存在哪些痛点？<br>
三、基于LLM+向量库的⽂档对话 ⼯程示例⾯<br>
⼤模型（LLMs）参数⾼效微调(PEFT) ⾯<br>
 <br>
⼤模型（LLMs）参数⾼效微调(PEFT) ⾯<br>
 <br>
微调⽅法是啥？如何微调？<br>
为什么需要 PEFT？<br>
介绍⼀下 PEFT？<br>
PEFT 有什么优点？<br>
微调⽅法批处理⼤⼩模式GPU显存速度？<br>
Peft 和 全量微调区别？<br>
多种不同的⾼效微调⽅法对⽐<br>
当前⾼效微调技术存在的⼀些问题<br>
⾼效微调技术最佳实践<br>
PEFT 存在问题？<br>
能不能总结⼀下各种参数⾼效微调⽅法？<br>
      ⼤模型（LLMs）参数⾼效微调(PEFT) ⾯<br>
微调⽅法是啥？如何微调？<br>
</p>

<h2>第 38 页</h2>

<p>  微调（Fine-tuning）是⼀种迁移学习的技术，⽤于在⼀个已经预训练好的模型基础上，通过进⼀步训<br>
练来适应特定的任务或数据集。微调可以在具有相似特征的任务之间共享知识，从⽽加快训练速度并提<br>
⾼模型性能。<br>
  以下是⼀般的微调步骤：<br>
1. 选择预训练模型：选择⼀个在⼤规模数据集上预训练好的模型，如ImageNet上的预训练的卷积神经<br>
⽹络（如ResNet、VGG等）。这些模型通常具有良好的特征提取能⼒。<br>
2. 冻结底层权重：将预训练模型的底层权重（通常是卷积层）固定住，不进⾏训练。这是因为底层权<br>
重通常学习到了通⽤的特征，可以被⽤于许多不同的任务。<br>
3. 替换顶层分类器：将预训练模型的顶层分类器（通常是全连接层）替换为适合特定任务的新的分类<br>
器。新的分类器的输出节点数量应该与任务的类别数相匹配。<br>
4. 解冻部分权重（可选）：根据任务的复杂性和可⽤的训练数据量，可以选择解冻⼀些底层权重，以<br>
便更好地适应新的任务。这样可以允许底层权重进⾏微⼩的调整，以更好地适应新任务的特征。<br>
5. 进⾏训练：使⽤特定任务的训练数据集对新的分类器进⾏训练。可以使⽤较⼩的学习率进⾏训练，<br>
以避免对预训练模型的权重进⾏过⼤的更新。<br>
6. 评估和调整：在训练完成后，使⽤验证集或测试集评估模型的性能。根据评估结果，可以进⾏调<br>
整，如调整学习率、调整模型结构等。<br>
  微调的关键是在预训练模型的基础上进⾏训练，从⽽将模型的知识迁移到特定任务上。通过这种⽅<br>
式，可以在较少的数据和计算资源下，快速构建和训练⾼性能的模型。<br>
  <br>
为什么需要 PEFT？<br>
  PEFT（Performance Estimation and Modeling for Fine-Tuning）是⼀种⽤于微调任务的性能估计和建<br>
模⽅法。它的主要⽬的是帮助研究⼈员和从业者在微调过程中更好地理解和预测模型的性能，并进⾏更<br>
有效的模型选择和调优。<br>
  以下是⼀些需要使⽤PEFT的情况：<br>
1. 模型选择：在微调之前，通常需要选择⼀个合适的预训练模型。PEFT可以帮助评估和⽐较不同预训<br>
练模型在特定任务上的性能，从⽽选择最适合的模型。<br>
2. 超参数调优：微调过程中可能涉及到⼀些超参数的选择，如学习率、批量⼤⼩等。PEFT可以帮助预<br>
估不同超参数设置下模型的性能，并指导超参数的调优。<br>
3. 计算资源规划：微调通常需要⼤量的计算资源，如显存、GPU时间等。PEFT可以帮助估计不同模型<br>
和数据集规模下的计算资源需求，以便更好地规划和分配资源。<br>
4. 模型压缩和加速：在⼀些场景下，需要将模型压缩或加速，以便在资源受限的设备上进⾏推理。<br>
PEFT可以帮助评估不同压缩和加速技术对模型性能的影响，并指导模型优化的⽅向。<br>
  PEFT通过模型的性能估计和建模，可以提供更准确的预测和指导，帮助研究⼈员和从业者更好地进⾏<br>
微调任务的设计和优化。<br>
</p>

<h2>第 39 页</h2>

<p>  <br>
介绍⼀下 PEFT？<br>
  PEFT（Performance Estimation and Modeling for Fine-Tuning）是⼀种⽤于微调任务的性能估计和建<br>
模⽅法。它的⽬的是帮助研究⼈员和从业者在微调过程中更好地理解和预测模型的性能，并进⾏更有效<br>
的模型选择和调优。<br>
  PEFT的主要思想是通过预测模型在微调任务上的性能，提供对不同模型和参数设置的性能估计。这样<br>
可以避免在⼤规模数据集上进⾏昂贵的微调实验，从⽽节省时间和计算资源。<br>
  PEFT的关键步骤包括：<br>
1. 数据采样：从原始数据集中采样⼀⼩部分数据⽤于性能估计。这样可以减少计算开销，同时保持采<br>
样数据与原始数据集的分布⼀致性。<br>
2. 特征提取：使⽤预训练模型提取采样数据的特征表示。这些特征通常具有很好的表达能⼒，可以⽤<br>
于性能估计。<br>
3. 性能估计模型：基于采样数据的特征表示，建⽴⼀个性能估计模型。这个模型可以是简单的线性回<br>
归模型，也可以是更复杂的神经⽹络模型。<br>
4. 性能预测：使⽤性能估计模型对未知数据的性能进⾏预测。通过输⼊微调任务的特征表示，模型可<br>
以输出预测的性能指标，如准确率、F1分数等。<br>
  通过PEFT，研究⼈员和从业者可以在微调之前，通过预测模型的性能，选择最佳的预训练模型、超参<br>
数设置和资源规划策略。这样可以加速模型的开发和优化过程，提⾼微调任务的效率和性能。<br>
  <br>
PEFT 有什么优点？<br>
  PEFT具有以下⼏个优点：<br>
1. 节省时间和计算资源：传统的微调⽅法需要在⼤规模数据集上进⾏昂贵的实验，耗费⼤量时间和计<br>
算资源。⽽PEFT通过性能估计和建模，可以避免这些实验，节省时间和计算开销。<br>
2. 提供准确的性能预测：PEFT通过建⽴性能估计模型，可以对未知数据的性能进⾏预测。这样可以提<br>
供准确的性能指标，帮助研究⼈员和从业者更好地理解模型的性能。<br>
3. 辅助模型选择和调优：PEFT可以帮助选择最佳的预训练模型、超参数设置和资源规划策略。通过预<br>
测模型的性能，可以指导模型选择和调优的⽅向，提⾼微调任务的效率和性能。<br>
4. 可解释性和可扩展性：PEFT的性能估计模型可以是简单的线性回归模型，也可以是更复杂的神经⽹<br>
络模型。这使得PEFT具有很好的可解释性和可扩展性，可以适应不同的微调任务和数据集。<br>
5. 适⽤于资源受限的场景：在⼀些资源受限的场景下，如移动设备或边缘计算环境，⽆法进⾏⼤规模<br>
的微调实验。PEFT可以帮助估计模型在这些设备上的性能，并指导模型压缩和加速的⽅向。<br>
</p>

<h2>第 40 页</h2>

<p>  综上所述，PEFT通过性能估计和建模，提供了⼀种⾼效、准确和可解释的⽅法，帮助研究⼈员和从业<br>
者进⾏微调任务的设计和优化。<br>
  <br>
微调⽅法批处理⼤⼩模式GPU显存速度？<br>
  微调⽅法的批处理⼤⼩、模型⼤⼩和GPU显存之间存在⼀定的关系，可以影响微调的速度和性能。下<br>
⾯是⼀些常⻅的情况：<br>
1. 批处理⼤⼩（Batch Size）：批处理⼤⼩是指在每次迭代中同时处理的样本数量。较⼤的批处理⼤<br>
⼩可以提⾼GPU的利⽤率，加快训练速度，但可能会导致显存不⾜的问题。如果批处理⼤⼩过⼤，<br>
⽆法适应GPU显存的限制，可能需要减⼩批处理⼤⼩或使⽤分布式训练等⽅法来解决显存不⾜的问<br>
题。<br>
2. 模型⼤⼩（Model Size）：模型⼤⼩指的是微调任务中使⽤的模型的参数量和内存占⽤。较⼤的模<br>
型通常需要更多的显存来存储参数和激活值，可能会导致显存不⾜的问题。在GPU显存有限的情况<br>
下，可以考虑使⽤轻量级模型或模型压缩等⽅法来减⼩模型⼤⼩，以适应显存限制。<br>
3. GPU显存：GPU显存是指GPU设备上可⽤的内存⼤⼩。如果微调任务所需的显存超过了GPU显存的<br>
限制，会导致显存不⾜的问题。在这种情况下，可以采取⼀些策略来解决显存不⾜，例如减⼩批处<br>
理⼤⼩、减⼩模型⼤⼩、使⽤分布式训练、使⽤混合精度训练等。<br>
  总之，微调⽅法的批处理⼤⼩、模型⼤⼩和GPU显存之间存在相互影响的关系。需要根据具体的情况<br>
来选择合适的参数设置，以在保证性能的同时，充分利⽤GPU资源并避免显存不⾜的问题。<br>
  <br>
Peft 和 全量微调区别？<br>
  PEFT（Performance Estimation for Fine-Tuning）和全量微调（Full Fine-Tuning）是两种不同的微调<br>
⽅法，它们在性能估计和实际微调过程中的数据使⽤上存在⼀些区别。<br>
1. 数据使⽤：全量微调使⽤完整的微调数据集进⾏模型的训练和调优。这意味着需要在⼤规模数据集<br>
上进⾏昂贵的实验，耗费⼤量时间和计算资源。<br>
  ⽽PEFT则通过性能估计和建模的⽅式，避免了在完整数据集上进⾏实验的过程。PEFT使⽤⼀部分样本<br>
数据来训练性能估计模型，然后利⽤该模型对未知数据的性能进⾏预测。<br>
1. 时间和计算开销：全量微调需要在完整数据集上进⾏训练和调优，耗费⼤量时间和计算资源。尤其<br>
是在⼤规模数据集和复杂模型的情况下，全量微调的时间和计算开销会更⼤。<br>
</p>

<h2>第 41 页</h2>

<p>  相⽐之下，PEFT通过性能估计和建模的⽅式，避免了在完整数据集上进⾏实验的过程，从⽽节省了时<br>
间和计算开销。<br>
1. 性能预测准确性：全量微调通过在完整数据集上进⾏训练和调优，可以获得较为准确的性能指标。<br>
因为全量微调是在实际数据上进⾏的，所以能够更好地反映模型在真实场景中的性能。<br>
  PEFT通过性能估计和建模的⽅式，可以预测模型在未知数据上的性能。虽然PEFT的性能预测准确性可<br>
能不如全量微调，但可以提供⼀个相对准确的性能指标，帮助研究⼈员和从业者更好地理解模型的性<br>
能。<br>
  综上所述，PEFT和全量微调在数据使⽤、时间和计算开销以及性能预测准确性等⽅⾯存在⼀些区别。<br>
选择使⽤哪种⽅法应根据具体情况和需求来决定。<br>
  <br>
多种不同的⾼效微调⽅法对⽐<br>
  在⾼效微调⽅法中，有⼏种常⻅的⽅法可以⽐较，包括迁移学习、知识蒸馏和⽹络剪枝。下⾯是对这<br>
些⽅法的简要⽐较：<br>
1. 迁移学习（Transfer Learning）：迁移学习是⼀种通过利⽤预训练模型的知识来加速微调的⽅法。<br>
它可以使⽤在⼤规模数据集上预训练的模型作为初始模型，并在⽬标任务上进⾏微调。迁移学习可<br>
以⼤⼤减少微调所需的训练时间和计算资源，并且通常能够达到较好的性能。<br>
2. 知识蒸馏（Knowledge Distillation）：知识蒸馏是⼀种将⼤型复杂模型的知识转移到⼩型模型中的<br>
⽅法。它通过在预训练模型上进⾏推理，并使⽤其输出作为⽬标标签，来训练⼀个较⼩的模型。知<br>
识蒸馏可以在保持较⼩模型的⾼效性能的同时，获得接近于⼤型模型的性能。<br>
3. ⽹络剪枝（Network Pruning）：⽹络剪枝是⼀种通过减少模型的参数和计算量来提⾼微调效率的⽅<br>
法。它通过对预训练模型进⾏剪枝，去除冗余和不必要的连接和参数，从⽽减少模型的⼤⼩和计算<br>
量。⽹络剪枝可以显著减少微调所需的训练时间和计算资源，并且通常能够保持较好的性能。<br>
  这些⾼效微调⽅法都有各⾃的特点和适⽤场景。迁移学习适⽤于⽬标任务与预训练任务相似的情况，<br>
可以快速获得较好的性能。知识蒸馏适⽤于需要在⼩型模型上进⾏微调的情况，可以在保持⾼效性能的<br>
同时减少模型⼤⼩。⽹络剪枝适⽤于需要进⼀步减少微调所需资源的情况，可以在保持较好性能的同时<br>
减少模型⼤⼩和计算量。<br>
  综上所述，选择适合的⾼效微调⽅法应根据具体任务需求和资源限制来决定。不同⽅法之间也可以结<br>
合使⽤，以进⼀步提⾼微调的效率和性能。<br>
  <br>
当前⾼效微调技术存在的⼀些问题<br>
</p>

<h2>第 42 页</h2>

<p>  尽管⾼效微调技术在提⾼微调效率⽅⾯取得了⼀些进展，但仍然存在⼀些问题和挑战：<br>
1. 性能保持：⼀些⾼效微调技术可能在提⾼效率的同时，对模型性能产⽣⼀定的影响。例如，⽹络剪<br>
枝可能会削减模型的容量，导致性能下降。因此，在使⽤⾼效微调技术时需要权衡效率和性能之间<br>
的关系，并进⾏适当的调整和优化。<br>
2. 通⽤性：⽬前的⾼效微调技术通常是针对特定的模型架构和任务设计的，可能不具备通⽤性。这意<br>
味着对于不同的模型和任务，可能需要重新设计和实现相应的⾼效微调技术。因此，需要进⼀步研<br>
究和开发通⽤的⾼效微调技术，以适应不同场景和需求。<br>
3. 数据依赖性：⼀些⾼效微调技术可能对数据的分布和规模具有⼀定的依赖性。例如，迁移学习通常<br>
需要⽬标任务和预训练任务具有相似的数据分布。这可能限制了⾼效微调技术在⼀些特殊或⼩规模<br>
数据集上的应⽤。因此，需要进⼀步研究和改进⾼效微调技术，使其对数据的依赖性更加灵活和适<br>
应性更强。<br>
4. 可解释性：⼀些⾼效微调技术可能会引⼊⼀些⿊盒操作，使得模型的解释和理解变得困难。例如，<br>
知识蒸馏可能会导致模型的输出不再直接对应于原始数据标签。这可能会影响模型的可解释性和可<br>
信度。因此，需要进⼀步研究和改进⾼效微调技术，以提⾼模型的可解释性和可理解性。<br>
  综上所述，当前⾼效微调技术在性能保持、通⽤性、数据依赖性和可解释性等⽅⾯仍然存在⼀些问题<br>
和挑战。随着研究的深⼊和技术的发展，相信这些问题将逐渐得到解决，并推动⾼效微调技术的进⼀步<br>
发展和应⽤。<br>
  <br>
⾼效微调技术最佳实践<br>
  以下是⼀些⾼效微调技术的最佳实践：<br>
1. 选择合适的预训练模型：预训练模型的选择对于⾼效微调⾄关重要。选择在⼤规模数据集上训练过<br>
的模型，例如ImageNet上的模型，可以获得更好的初始参数和特征表示。<br>
2. 冻结部分层：在微调过程中，可以选择冻结预训练模型的⼀部分层，只微调模型的⼀部分层。通<br>
常，较低层的特征提取层可以被冻结，只微调较⾼层的分类层。这样可以减少微调所需的训练时间<br>
和计算资源。<br>
3. 适当调整学习率：微调过程中，学习率的调整⾮常重要。通常，可以使⽤较⼩的学习率来微调模型<br>
的较⾼层，以避免过⼤的参数更新。同时，可以使⽤较⼤的学习率来微调模型的较低层，以更快地<br>
调整特征表示。<br>
4. 数据增强：数据增强是⼀种有效的⽅法，可以增加训练数据的多样性，提⾼模型的泛化能⼒。在微<br>
调过程中，可以使⽤各种数据增强技术，例如随机裁剪、翻转和旋转等，以增加训练数据的数量和<br>
多样性。<br>
5. 早停策略：在微调过程中，使⽤早停策略可以避免过拟合。可以监测验证集上的性能，并在性能不<br>
再提升时停⽌微调，以避免过多训练导致模型在验证集上的性能下降。<br>
6. 结合其他⾼效微调技术：可以结合多种⾼效微调技术来进⼀步提⾼微调的效率和性能。例如，可以<br>
使⽤知识蒸馏来将⼤型模型的知识转移到⼩型模型中，以减少模型的⼤⼩和计算量。<br>
</p>

<h2>第 43 页</h2>

<p>  综上所述，⾼效微调技术的最佳实践包括选择合适的预训练模型、冻结部分层、适当调整学习率、使<br>
⽤数据增强、使⽤早停策略以及结合其他⾼效微调技术。这些实践可以帮助提⾼微调的效率和性能，并<br>
在资源受限的情况下获得更好的结果。<br>
  <br>
PEFT 存在问题？<br>
  PEFT（Performance Estimation and Modeling for Fine-Tuning）是⼀种⽤于估计和建模微调过程中性<br>
能的⽅法。尽管PEFT在⼀些⽅⾯具有优势，但也存在⼀些问题和挑战：<br>
1. 精度限制：PEFT的性能估计是基于预训练模型和微调数据集的⼀些统计特征进⾏建模的。这种建模<br>
⽅法可能⽆法准确地捕捉到微调过程中的复杂性和不确定性。因此，PEFT的性能估计结果可能存在<br>
⼀定的误差和不确定性，⽆法完全准确地预测微调性能。<br>
2. 数据偏差：PEFT的性能估计和建模依赖于预训练模型和微调数据集的统计特征。如果这些特征与实<br>
际应⽤场景存在显著差异，PEFT的性能估计可能不准确。例如，如果微调数据集与⽬标任务的数据<br>
分布不⼀致，PEFT的性能估计可能会有较⼤的偏差。<br>
3. 模型依赖性：PEFT的性能估计和建模依赖于预训练模型的质量和性能。如果预训练模型本身存在⼀<br>
些问题，例如表示能⼒不⾜或训练偏差等，PEFT的性能估计可能会受到影响。因此，PEFT的性能<br>
估计结果可能在不同的预训练模型之间存在差异。<br>
4. 计算复杂性：PEFT的性能估计和建模可能需要⼤量的计算资源和时间。尤其是在⼤规模模型和数据<br>
集上，PEFT的计算复杂性可能会变得⾮常⾼。这可能限制了PEFT在实际应⽤中的可⾏性和可扩展<br>
性。<br>
  综上所述，尽管PEFT在性能估计和建模⽅⾯具有⼀定的优势，但仍然存在精度限制、数据偏差、模型<br>
依赖性和计算复杂性等问题。在使⽤PEFT时，需要注意这些问题，并进⾏适当的验证和调整，以确保性<br>
能估计的准确性和可靠性。<br>
  <br>
能不能总结⼀下各种参数⾼效微调⽅法？<br>
  当涉及到⾼效微调⽅法时，有⼏个关键的参数和技术可以考虑：<br>
1. 冻结层：在微调过程中，可以选择冻结预训练模型的⼀部分层，只微调模型的⼀部分层。通常，较<br>
低层的特征提取层可以被冻结，只微调较⾼层的分类层。这样可以减少微调所需的训练时间和计算<br>
资源。<br>
2. 学习率调整：微调过程中，学习率的调整⾮常重要。可以使⽤较⼩的学习率来微调模型的较⾼层，<br>
以避免过⼤的参数更新。同时，可以使⽤较⼤的学习率来微调模型的较低层，以更快地调整特征表<br>
示。<br>
3. 数据增强：数据增强是⼀种有效的⽅法，可以增加训练数据的多样性，提⾼模型的泛化能⼒。在微<br>
</p>

<h2>第 44 页</h2>

<p>调过程中，可以使⽤各种数据增强技术，例如随机裁剪、翻转和旋转等，以增加训练数据的数量和<br>
多样性。<br>
4. 早停策略：在微调过程中，使⽤早停策略可以避免过拟合。可以监测验证集上的性能，并在性能不<br>
再提升时停⽌微调，以避免过多训练导致模型在验证集上的性能下降。<br>
5. 知识蒸馏：知识蒸馏是⼀种将⼤型模型的知识转移到⼩型模型中的⽅法，以减少模型的⼤⼩和计算<br>
量。通过将预训练模型的输出作为⽬标标签，可以在微调过程中使⽤知识蒸馏来提⾼⼩型模型的性<br>
能。<br>
  这些参数和技术可以根据具体的任务和数据集进⾏调整和应⽤。综合考虑这些⽅法，可以提⾼微调的<br>
效率和性能，并在资源受限的情况下获得更好的结果。<br>
  <br>
配器微调（Adapter-tuning）篇<br>
 <br>
⼀、为什么 需要 适配器微调（Adapter-tuning）？<br>
⼆、适配器微调（Adapter-tuning）思路？<br>
三、 适配器微调（Adapter-tuning）特点是什么？<br>
四、AdapterFusion 思路 是什么？<br>
五、AdapterDrop 思路 是什么？<br>
六、AdapterDrop 特点 是什么？<br>
七、MAM Adapter 思路 是什么？<br>
⼋、MAM Adapter 特点 是什么？<br>
适配器微调（Adapter-tuning）篇<br>
 <br>
⼀、为什么 需要 适配器微调（Adapter-tuning）？<br>
  适配器微调（Adapter-tuning）是⼀种⽤于微调预训练模型的⽅法，它相⽐于传统的微调⽅法具有⼀些<br>
优势和应⽤场景。以下是⼀些需要适配器微调的情况：<br>
1. 保留预训练模型的知识：在传统的微调⽅法中，通常需要在微调过程中更新整个模型的参数。然<br>
⽽，对于某些任务和应⽤，我们可能希望保留预训练模型的知识，⽽只对特定任务进⾏微调。适配<br>
器微调可以实现这⼀⽬标，它只微调模型的适配器层，⽽不改变预训练模型的参数。<br>
2. 减少微调的计算量和时间：传统的微调⽅法需要更新整个模型的参数，这可能需要⼤量的计算资源<br>
和时间。适配器微调可以显著减少微调的计算量和时间，因为它只需要微调适配器层的参数，⽽不<br>
需要重新训练整个模型。<br>
3. 提⾼模型的可解释性和可复⽤性：适配器微调可以使模型更具可解释性和可复⽤性。通过在适配器<br>
层中添加任务特定的适配器，我们可以更好地理解模型在不同任务上的表现，并且可以将适配器⽤<br>
于其他类似的任务，从⽽提⾼模型的可复⽤性。<br>
</p>

<h2>第 45 页</h2>

<p>4. 避免灾难性遗忘：在传统的微调⽅法中，微调过程可能会导致预训练模型在原任务上的性能下降，<br>
即灾难性遗忘。适配器微调通过只微调适配器层，可以避免对预训练模型的其他部分进⾏⼤幅度的<br>
更新，从⽽减少灾难性遗忘的⻛险。<br>
  总⽽⾔之，适配器微调是⼀种⽤于微调预训练模型的⽅法，它可以保留预训练模型的知识，减少计算<br>
量和时间，提⾼模型的可解释性和可复⽤性，并避免灾难性遗忘。这些优势使得适配器微调在某些任务<br>
和应⽤中成为⼀种有吸引⼒的选择。<br>
  <br>
⼆、适配器微调（Adapter-tuning）思路？<br>
  适配器微调（Adapter-tuning）是⼀种⽤于微调预训练模型的⽅法，其思路可以概括如下：<br>
1. 预训练模型选择：⾸先，选择⼀个适合任务的预训练模型，例如BERT、GPT等。这些预训练模型在<br>
⼤规模数据上进⾏了训练，具有较强的语义表示能⼒。<br>
2. 适配器层添加：在选择的预训练模型中，为⽬标任务添加适配器层。适配器层是⼀个⼩型的任务特<br>
定层，通常由⼀个或多个全连接层组成。适配器层的⽬的是将预训练模型的表示转换为适合⽬标任<br>
务的表示。<br>
3. 冻结其他层：在适配器微调中，通常会冻结预训练模型的其他层，只微调适配器层的参数。这是因<br>
为预训练模型已经在⼤规模数据上进⾏了训练，其低层特征提取层已经具有较好的特征表示能⼒，<br>
不需要进⾏⼤幅度的更新。<br>
4. 学习率调整：在微调过程中，可以使⽤较⼩的学习率来微调适配器层的参数，以避免过⼤的参数更<br>
新。同时，可以使⽤较⼤的学习率来微调预训练模型的其他层，以更快地调整特征表示。<br>
5. 数据增强和训练：为了增加训练数据的多样性，可以使⽤各种数据增强技术，例如随机裁剪、翻转<br>
和旋转等。然后，使⽤⽬标任务的标注数据对适配器层进⾏训练。<br>
6. 验证和调优：在微调过程中，可以使⽤验证集来监测模型的性能，并根据性能表现进⾏调优。可以<br>
根据验证集上的性能选择最佳的模型参数和超参数。<br>
  适配器微调的思路是在预训练模型中添加适配器层，并只微调适配器层的参数，从⽽保留预训练模型<br>
的知识、减少计算量和时间，并提⾼模型的可解释性和可复⽤性。这种⽅法在许多⾃然语⾔处理和计算<br>
机视觉任务中都取得了良好的效果。<br>
  <br>
三、 适配器微调（Adapter-tuning）特点是什么？<br>
  适配器微调（Adapter-tuning）具有以下特点：<br>
1. 保留预训练模型的知识：适配器微调只微调适配器层的参数，⽽不改变预训练模型的其他参数。这<br>
样可以保留预训练模型在⼤规模数据上学到的知识和特征表示能⼒。<br>
</p>

<h2>第 46 页</h2>

<p>2. 减少微调的计算量和时间：相⽐于传统的微调⽅法，适配器微调只需要微调适配器层的参数，⽽不<br>
需要重新训练整个模型。这样可以⼤⼤减少微调的计算量和时间消耗。<br>
3. 提⾼模型的可解释性和可复⽤性：适配器微调在预训练模型中添加了适配器层，这些适配器层可以<br>
理解为任务特定的模块。通过适配器层，模型的性能在不同任务之间可以更好地解释和⽐较，并且<br>
适配器层可以⽤于其他类似的任务，提⾼模型的可复⽤性。<br>
4. 避免灾难性遗忘：传统的微调⽅法可能导致预训练模型在原任务上的性能下降，即灾难性遗忘。适<br>
配器微调只微调适配器层的参数，不对预训练模型的其他部分进⾏⼤幅度的更新，可以减少灾难性<br>
遗忘的⻛险。<br>
5. 灵活性和可扩展性：适配器微调可以在不同的预训练模型和任务中应⽤。适配器层的设计可以根据<br>
任务的特点进⾏调整，以适应不同的任务需求。这种灵活性和可扩展性使得适配器微调成为⼀种通<br>
⽤且⾼效的微调⽅法。<br>
  总⽽⾔之，适配器微调通过保留预训练模型的知识、减少计算量和时间、提⾼模型的可解释性和可复<br>
⽤性、避免灾难性遗忘以及具有灵活性和可扩展性等特点，成为⼀种有吸引⼒的微调⽅法。<br>
  <br>
四、AdapterFusion 思路 是什么？<br>
  AdapterFusion是⼀种⽤于多任务学习的⽅法，其思路可以概括如下：<br>
1. 预训练模型选择：⾸先，选择⼀个适合多任务学习的预训练模型，例如BERT、GPT等。这些预训练<br>
模型在⼤规模数据上进⾏了训练，具有较强的语义表示能⼒。<br>
2. 适配器层添加：在选择的预训练模型中，为每个任务添加适配器层。适配器层是⼀个⼩型的任务特<br>
定层，通常由⼀个或多个全连接层组成。适配器层的⽬的是将预训练模型的表示转换为适合每个任<br>
务的表示。<br>
3. 适配器融合：在AdapterFusion中，适配器融合是关键步骤。适配器融合通过将不同任务的适配器层<br>
的输出进⾏融合，得到⼀个综合的表示。常⻅的融合⽅法包括简单的加权平均、注意⼒机制等。<br>
4. 冻结其他层：在AdapterFusion中，通常会冻结预训练模型的其他层，只微调适配器层的参数。这是<br>
因为预训练模型已经在⼤规模数据上进⾏了训练，其低层特征提取层已经具有较好的特征表示能<br>
⼒，不需要进⾏⼤幅度的更新。<br>
5. 学习率调整：在微调过程中，可以使⽤较⼩的学习率来微调适配器层的参数，以避免过⼤的参数更<br>
新。同时，可以使⽤较⼤的学习率来微调预训练模型的其他层，以更快地调整特征表示。<br>
6. 数据增强和训练：为了增加训练数据的多样性，可以使⽤各种数据增强技术，例如随机裁剪、翻转<br>
和旋转等。然后，使⽤多个任务的标注数据对适配器层进⾏训练。<br>
7. 验证和调优：在微调过程中，可以使⽤验证集来监测模型的性能，并根据性能表现进⾏调优。可以<br>
根据验证集上的性能选择最佳的模型参数和超参数。<br>
  AdapterFusion的思路是在预训练模型中为每个任务添加适配器层，并通过适配器融合将不同任务的表<br>
示进⾏融合，从⽽提⾼多任务学习的性能。这种⽅法可以充分利⽤预训练模型的知识，并通过适配器融<br>
合实现任务之间的信息共享和互补，从⽽提⾼模型的泛化能⼒和效果。<br>
  <br>
</p>

<h2>第 47 页</h2>

<p>五、AdapterDrop 思路 是什么？<br>
  AdapterDrop是⼀种⽤于适配器微调的⽅法，其思路可以概括如下：<br>
1. 适配器层添加：⾸先，在预训练模型中为每个任务添加适配器层。适配器层是⼀个⼩型的任务特定<br>
层，通常由⼀个或多个全连接层组成。适配器层的⽬的是将预训练模型的表示转换为适合每个任务<br>
的表示。<br>
2. 适配器层的随机丢弃：在AdapterDrop中，引⼊了适配器层的随机丢弃机制。具体⽽⾔，对于每个<br>
任务，在训练过程中以⼀定的概率随机丢弃该任务的适配器层。这样，模型在训练过程中会随机选<br>
择使⽤哪些任务的适配器层进⾏微调。<br>
3. 动态适配器选择：在每个训练样本上，通过随机丢弃适配器层，模型会⾃动选择使⽤哪些任务的适<br>
配器层进⾏微调。这种动态的适配器选择机制可以增加模型的鲁棒性和泛化能⼒，使得模型能够适<br>
应不同任务的变化和不确定性。<br>
4. 训练和微调：在训练过程中，使⽤多个任务的标注数据对适配器层进⾏训练。对于每个训练样本，<br>
根据随机丢弃的适配器层进⾏微调，并计算损失函数以更新模型的参数。<br>
5. 推断和预测：在推断和预测阶段，可以选择使⽤所有任务的适配器层进⾏预测，或者根据某种策略<br>
选择部分任务的适配器层进⾏预测。这样可以根据具体应⽤场景的需求进⾏灵活的任务选择和预<br>
测。<br>
  AdapterDrop的思路是通过适配器层的随机丢弃机制，实现动态的适配器选择和微调。这种⽅法可以增<br>
加模型的鲁棒性和泛化能⼒，使得模型能够适应不同任务的变化和不确定性。同时，通过随机丢弃适配<br>
器层，还可以减少模型的计算量和参数数量，提⾼模型的效率和可扩展性。<br>
  <br>
六、AdapterDrop 特点 是什么？<br>
  AdapterDrop具有以下⼏个特点：<br>
1. 动态适配器选择：AdapterDrop引⼊了适配器层的随机丢弃机制，使得模型可以在训练过程中动态<br>
选择使⽤哪些任务的适配器层进⾏微调。这种动态适配器选择机制可以增加模型的鲁棒性和泛化能<br>
⼒，使得模型能够适应不同任务的变化和不确定性。<br>
2. 鲁棒性和泛化能⼒：通过随机丢弃适配器层，AdapterDrop可以让模型在训练过程中随机选择使⽤<br>
哪些任务的适配器层进⾏微调。这种随机性可以增加模型对于噪声和⼲扰的鲁棒性，并提⾼模型的<br>
泛化能⼒。<br>
3. 减少计算量和参数数量：通过随机丢弃适配器层，AdapterDrop可以减少模型的计算量和参数数<br>
量。在训练过程中，只有部分任务的适配器层被使⽤，其他任务的适配器层被丢弃，从⽽减少了模<br>
型的计算量和参数数量，提⾼了模型的效率和可扩展性。<br>
4. 灵活的任务选择和预测：在推断和预测阶段，可以根据具体的需求选择使⽤所有任务的适配器层进<br>
⾏预测，或者选择使⽤部分任务的适配器层进⾏预测。这种灵活的任务选择和预测机制可以根据具<br>
</p>

<h2>第 48 页</h2>

<p>体应⽤场景的需求进⾏灵活调整，提⾼模型的适应性和可⽤性。<br>
  总之，AdapterDrop通过动态适配器选择、增加鲁棒性和泛化能⼒、减少计算量和参数数量以及灵活的<br>
任务选择和预测等特点，提供了⼀种有效的⽅法来进⾏适配器微调，进⼀步提⾼多任务学习的性能。<br>
  <br>
七、MAM Adapter 思路 是什么？<br>
  MAM Adapter（Masked and Masked Adapter for Multi-task Learning）是⼀种⽤于多任务学习的适配<br>
器微调⽅法，其思路可以概括如下：<br>
1. 适配器层添加：⾸先，在预训练模型中为每个任务添加适配器层。适配器层是⼀个⼩型的任务特定<br>
层，通常由⼀个或多个全连接层组成。适配器层的⽬的是将预训练模型的表示转换为适合每个任务<br>
的表示。<br>
2. 掩码机制：在MAM Adapter中，引⼊了掩码机制来增强适配器层的表示能⼒。具体⽽⾔，对于每个<br>
任务，在训练过程中，随机选择⼀部分适配器层的神经元进⾏掩码操作，即将这些神经元的输出置<br>
为0。这样可以使得适配器层的表示更加丰富和多样化。<br>
3. 掩码预测：在训练过程中，除了对任务的预测进⾏优化外，还引⼊了掩码预测任务。具体⽽⾔，对<br>
于每个任务，在适配器层的输出上添加⼀个掩码预测层，⽤于预测哪些神经元应该被掩码。这样，<br>
模型在训练过程中不仅要优化任务的预测准确性，还要同时优化掩码预测任务的准确性。<br>
4. 联合训练：在训练过程中，使⽤多个任务的标注数据对适配器层和掩码预测层进⾏联合训练。通过<br>
最⼩化任务预测的损失和掩码预测的损失，来更新模型的参数。这样可以使得模型能够同时学习任<br>
务的表示和掩码的⽣成，进⼀步提⾼多任务学习的性能。<br>
5. 推断和预测：在推断和预测阶段，可以选择使⽤所有任务的适配器层进⾏预测，或者根据某种策略<br>
选择部分任务的适配器层进⾏预测。根据具体应⽤场景的需求，可以灵活选择适配器层进⾏预测，<br>
从⽽实现多任务学习的⽬标。<br>
  MAM Adapter的思路是通过引⼊掩码机制和掩码预测任务，增强适配器层的表示能⼒，并通过联合训<br>
练优化任务预测和掩码预测的准确性。这种⽅法可以提⾼适配器微调的性能，进⼀步增强多任务学习的<br>
效果。<br>
  <br>
⼋、MAM Adapter 特点 是什么？<br>
  MAM Adapter具有以下⼏个特点：<br>
1. 掩码机制增强表示能⼒：MAM Adapter引⼊了掩码机制，通过随机掩码部分适配器层的神经元，从<br>
⽽增强适配器层的表示能⼒。这种掩码机制可以使得适配器层的表示更加丰富和多样化，有助于提<br>
⾼多任务学习的性能。<br>
</p>

<h2>第 49 页</h2>

<p>2. 联合训练优化任务和掩码预测：MAM Adapter在训练过程中不仅优化任务的预测准确性，还同时优<br>
化掩码预测任务的准确性。通过最⼩化任务预测的损失和掩码预测的损失，来更新模型的参数。这<br>
样可以使得模型能够同时学习任务的表示和掩码的⽣成，进⼀步提⾼多任务学习的性能。<br>
3. 灵活的任务选择和预测：在推断和预测阶段，可以根据具体的需求选择使⽤所有任务的适配器层进<br>
⾏预测，或者选择使⽤部分任务的适配器层进⾏预测。这种灵活的任务选择和预测机制可以根据具<br>
体应⽤场景的需求进⾏灵活调整，提⾼模型的适应性和可⽤性。<br>
4. 提⾼多任务学习性能：MAM Adapter通过增强适配器层的表示能⼒和联合训练优化任务和掩码预<br>
测，可以提⾼多任务学习的性能。适配器层的表示能⼒增强了模型对任务的适应能⼒，⽽掩码预测<br>
任务的优化可以使得模型学习到更加鲁棒的表示。<br>
  总之，MAM Adapter通过掩码机制增强表示能⼒、联合训练优化任务和掩码预测、灵活的任务选择和<br>
预测等特点，提供了⼀种有效的⽅法来进⾏适配器微调，进⼀步提⾼多任务学习的性能。<br>
  <br>
                        <br>
提示学习（Prompting）<br>
 <br>
⼀、为什么需要 提示学习（Prompting）？<br>
⼆、什么是 提示学习（Prompting）？<br>
三、提示学习（Prompting） 有什么优点？<br>
四、提示学习（Prompting）有哪些⽅法，能不能稍微介绍⼀下它们间？<br>
4.1 前缀微调（Preﬁx-tining）篇<br>
4.1.1 为什么需要 前缀微调（Preﬁx-tining）？<br>
4.1.2 前缀微调（Preﬁx-tining）思路是什么？<br>
4.1.3 前缀微调（Preﬁx-tining）的优点是什么？<br>
4.1.4 前缀微调（Preﬁx-tining）的缺点是什么？<br>
4.2 指示微调（Prompt-tuning）篇<br>
4.2.1 为什么需要 指示微调（Prompt-tuning）？<br>
4.2.2 指示微调（Prompt-tuning）思路是什么？<br>
4.2.3 指示微调（Prompt-tuning）优点是什么？<br>
4.2.4 指示微调（Prompt-tuning）缺点是什么？<br>
4.2.5 指示微调（Prompt-tuning）与 Preﬁx-tuning 区别 是什么？<br>
4.2.6 指示微调（Prompt-tuning）与 ﬁne-tuning 区别 是什么？<br>
4.3 P-tuning 篇<br>
4.3.1 为什么需要 P-tuning？<br>
4.3.2 P-tuning 思路是什么？<br>
4.3.3 P-tuning 优点是什么？<br>
4.3.4 P-tuning 缺点是什么？<br>
4.4 P-tuning v2 篇<br>
4.4.1 为什么需要 P-tuning v2？<br>
</p>

<h2>第 50 页</h2>

<p>4.4.2 P-tuning v2 思路是什么？<br>
4.4.3 P-tuning v2 优点是什么？<br>
4.4.4 P-tuning v2 缺点是什么？<br>
提示学习（Prompting）<br>
 <br>
⼀、为什么需要 提示学习（Prompting）？<br>
  提示学习（Prompting）是⼀种在⾃然语⾔处理任务中引⼊⼈类编写的提示或示例来辅助模型⽣成更准<br>
确和有意义的输出的技术。以下是⼀些使⽤提示学习的原因：<br>
1. 解决模糊性：在某些任务中，输⼊可能存在歧义或模糊性，通过提供明确的提示，可以帮助模型更<br>
好地理解任务的要求，避免产⽣错误或不确定的输出。<br>
2. 控制⽣成：在⽣成式任务中，使⽤提示可以指导模型⽣成特定类型的输出。例如，在⽣成新闻标题<br>
的任务中，通过提示指定标题的主题或⻛格，可以使模型⽣成更符合要求的标题。<br>
3. 纠正偏⻅：在⾃然语⾔处理中，模型可能受到社会偏⻅的影响，通过在提示中明确要求模型避免偏<br>
⻅，可以帮助减少模型输出中的偏⻅。<br>
4. 增加⼀致性：通过在多个样本中使⽤相同的提示，可以确保模型⽣成的输出在不同输⼊上具有⼀致<br>
性。这对于任务如翻译或摘要⽣成等涉及多个输⼊的任务尤为重要。<br>
  总的来说，提示学习可以提供额外的信息和指导，帮助模型更好地理解任务和⽣成准确、有意义的输<br>
出。<br>
  <br>
⼆、什么是 提示学习（Prompting）？<br>
  提示学习（Prompting）是⼀种在机器学习中使⽤⼈类编写的提示或示例来辅助模型进⾏学习和推理的<br>
技术。在⾃然语⾔处理任务中，提示通常是⼀段⽂字或问题，⽤于指导模型⽣成或理解特定的输出。<br>
  提示学习可以⽤于各种⾃然语⾔处理任务，包括⽂本分类、命名实体识别、情感分析、机器翻译等。<br>
在这些任务中，模型需要根据输⼊的⽂本来进⾏预测或⽣成输出。通过提供明确的提示，可以引导模型<br>
关注特定的信息或完成特定的任务。<br>
  提示可以采⽤不同的形式，例如：<br>
1. 完整的句⼦或问题：提供⼀个完整的句⼦或问题，要求模型根据输⼊⽣成相应的回答或输出。<br>
2. 部分句⼦或关键词：提供部分句⼦或关键词，要求模型根据提示进⾏补充或扩展。<br>
</p>

<h2>第 51 页</h2>

<p>3. 条件约束：提供条件约束，要求模型⽣成满⾜这些条件的输出。<br>
  通过提示学习，可以改善模型的性能，提⾼其准确性和鲁棒性。同时，提示学习也可以⽤于控制模型<br>
的⽣成，纠正偏⻅以及提供⼀致性的输出。<br>
  <br>
三、提示学习（Prompting） 有什么优点？<br>
  提示学习（Prompting）是⼀种在⾃然语⾔处理任务中使⽤⼈⼯设计的提示或指导来辅助模型⽣成输出<br>
的⽅法。它具有以下⼏个优点：<br>
1. 控制⽣成输出：通过给定合适的提示，可以更好地控制模型⽣成的输出。提示可以引导模型关注特<br>
定的信息、执⾏特定的任务或⽣成特定的⻛格。这种控制使得模型更加可控，能够满⾜特定的需<br>
求。<br>
2. 提⾼⽣成质量：通过合理设计和使⽤提示，可以帮助模型⽣成更准确、更流畅、更有逻辑性的输<br>
出。提示提供了⼀种引导模型⽣成的⽅式，可以避免⼀些常⻅的错误和⽆意义的输出，从⽽提⾼⽣<br>
成质量。<br>
3. 解决数据稀缺问题：在某些任务中，训练数据可能⾮常稀缺，难以覆盖所有可能的输⼊和输出。通<br>
过使⽤提示，可以将模型的知识和经验引导到特定领域或任务中，从⽽提供更好的性能。这种⽅式<br>
可以在数据稀缺的情况下，利⽤有限的数据进⾏更有效的训练和⽣成。<br>
4. 提供可解释性：提示作为⼈⼯设计的输⼊，可以提供对模型⽣成输出的解释和理解。通过分析和调<br>
整提示，可以更好地理解模型在⽣成过程中的决策和⾏为，从⽽提⾼模型的可解释性。<br>
5. 简化训练过程：在某些任务中，模型的训练可能⾮常困难和耗时。通过使⽤提示，可以简化训练过<br>
程，减少模型的训练时间和计算资源的消耗。提示可以提供额外的信息和约束，帮助模型更快地收<br>
敛和学习。<br>
  需要注意的是，提示学习也存在⼀些挑战和限制，如如何设计合适的提示、如何平衡提示和⾃由⽣成<br>
等。因此，在使⽤提示学习时，需要根据具体任务和需求进⾏设计和调整，以获得最佳的效果。<br>
  <br>
四、提示学习（Prompting）有哪些⽅法，能不能稍微介绍⼀下它们间？<br>
  提示学习（Prompting）有多种⽅法和技术，以下是⼀些常⻅的⽅法：<br>
1. ⽂本前缀（Text Preﬁx）：在输⼊⽂本的开头添加⼀个⼈⼯设计的前缀作为提示。这个前缀可以是<br>
⼀个问题、⼀个指令、⼀个关键词等，⽤来引导模型⽣成相关的输出。例如，在⽂本⽣成任务中，<br>
可以在输⼊⽂本前添加⼀个问题，要求模型回答该问题。<br>
2. 控制标记（Control Tokens）：在输⼊⽂本中使⽤特定的控制标记来指示模型⽣成特定的内容。这<br>
些控制标记可以是特殊的标记或标签，⽤来指定⽣成的⻛格、主题、任务等。例如，对于⽂本⽣成<br>
</p>

<h2>第 52 页</h2>

<p>任务，可以使⽤不同的控制标记来指示⽣成正⾯或负⾯情感的⽂本。<br>
3. 问题模板（Question Templates）：设计⼀系列问题模板，⽤于引导模型⽣成回答问题的⽂本。这<br>
些问题模板可以覆盖不同类型的问题，包括事实性问题、推理问题、主观性问题等。模型可以根据<br>
问题模板⽣成对应的回答。<br>
4. 策略优化（Policy Optimization）：通过设计⼀个策略⽹络，引导模型在⽣成过程中做出合适的决<br>
策。策略⽹络可以根据当前的输⼊和上下⽂，选择合适的动作或⽣成⽅式。这种⽅法可以⽤于⽣成<br>
对话系统、机器翻译等任务。<br>
5. 知识引导（Knowledge Guided）：利⽤外部的知识源来辅助模型⽣成输出。这些知识源可以是知识<br>
图谱、数据库、⽂档等，模型可以根据这些知识源进⾏查询、检索和引⽤。这样可以提供更准确、<br>
更丰富的信息来指导模型⽣成。<br>
  这些⽅法可以单独使⽤，也可以组合使⽤，根据具体任务和需求进⾏选择和调整。在实际应⽤中，需<br>
要根据数据集、模型架构和任务⽬标等因素来确定最适合的提示学习⽅法。同时，也需要进⾏实验和调<br>
整，以获得最佳的性能和效果。<br>
  <br>
4.1 前缀微调（Preﬁx-tuning）篇<br>
4.1.1 为什么需要 前缀微调（Preﬁx-tuning）？<br>
  前缀微调（Prefix-tuning）是⼀种在提示学习中使⽤的技术，它通过微调（fine-tuning）预<br>
训练语⾔模型来适应特定的⽣成任务。前缀微调之所以需要，是因为传统的预训练语⾔模型在⽣成任务<br>
中存在⼀些问题和限制，包括以下⼏个⽅⾯：<br>
  1. 缺乏控制：传统的预训练语⾔模型通常是通过⽆监督学习从⼤规模⽂本数据中学习得到的，⽣成<br>
时缺乏对输出的控制。这导致模型往往会⽣成⼀些⽆意义、不准确或不符合要求的内容。<br>
  2. 缺乏指导：传统的预训练语⾔模型在⽣成任务中缺乏指导，⽆法根据特定的任务要求⽣成相关的<br>
内容。例如，在问答任务中，模型需要根据给定的问题⽣成准确的答案，但预训练语⾔模型⽆法直接实<br>
现这⼀点。<br>
  3. 数据偏差：预训练语⾔模型通常是从⼤规模的通⽤数据中训练得到的，⽽特定的⽣成任务往往需<br>
要针对特定领域或任务的数据。由于数据的偏差，预训练语⾔模型在特定任务上的性能可能会受到限<br>
制。<br>
  前缀微调通过在输⼊⽂本的开头添加⼀个⼈⼯设计的前缀，将任务要求或指导信息引⼊到⽣成过程<br>
中，从⽽解决了上述问题。通过给定合适的前缀，可以控制模型⽣成的内容，指导模型关注特定的信<br>
息，并使⽣成结果更加准确和符合要求。前缀微调提供了⼀种简单有效的⽅法，可以在⽣成任务中引⼊<br>
⼈类设计的指导信息，提⾼模型的⽣成质量和可控性。<br>
  &lt;/aside&gt;<br>
- 4.1.2 前缀微调（Prefix-tuning）思路是什么？<br>
</p>

<h2>第 53 页</h2>

<p>  &lt;aside&gt;<br>
  <br>
  前缀微调（Prefix-tuning）的思路是在预训练语⾔模型的基础上，通过微调的⽅式引⼊任务相关<br>
的指导信息，从⽽提⾼模型在特定⽣成任务上的性能和可控性。以下是前缀微调的⼀般思路：<br>
  1. 预训练语⾔模型：⾸先，使⽤⼤规模的⽆监督数据对语⾔模型进⾏预训练。这个预训练过程通常<br>
是通过⾃回归（autoregressive）的⽅式进⾏，模型根据前⾯的⽂本⽣成下⼀个词或字符。<br>
  2. 设计前缀：针对特定的⽣成任务，设计⼀个合适的前缀，作为输⼊⽂本的开头。前缀可以是⼀个<br>
问题、⼀个指令、⼀个关键词等，⽤来引导模型⽣成相关的输出。前缀应该包含任务的要求、指导或关<br>
键信息，以帮助模型⽣成符合任务要求的内容。<br>
  3. 微调预训练模型：使⽤带有前缀的任务数据对预训练语⾔模型进⾏微调。微调的⽬标是让模型在<br>
特定任务上更好地⽣成符合要求的内容。微调的过程中，可以使⽤任务相关的损失函数来指导模型的学<br>
习，以最⼤程度地提⾼⽣成结果的质量和准确性。<br>
  4. ⽣成输出：在实际应⽤中，使⽤微调后的模型来⽣成输出。将任务相关的输⼊⽂本（包含前缀）<br>
输⼊到模型中，模型根据前缀和上下⽂⽣成相应的输出。通过前缀的设计和微调过程，模型能够更好地<br>
理解任务要求，并⽣成符合要求的内容。<br>
  前缀微调通过在预训练语⾔模型的基础上引⼊任务相关的指导信息，使模型更加适应特定的⽣成任<br>
务。这种⽅法不仅提⾼了⽣成结果的质量和准确性，还增加了对⽣成过程的可控性，使模型能够更好地<br>
满⾜任务的需求。<br>
  &lt;/aside&gt;<br>
- 4.1.3 前缀微调（Prefix-tuning）的优点是什么？<br>
  &lt;aside&gt;<br>
  <br>
  前缀微调（Prefix-tuning）具有以下⼏个优点：<br>
  1. 可控性：通过设计合适的前缀，可以引导模型⽣成特定类型的内容，使⽣成结果更加符合任务要<br>
求。前缀提供了对⽣成过程的控制，使得模型能够根据任务需求⽣成相关的内容，从⽽提⾼⽣成结果的<br>
准确性和质量。<br>
  2. 灵活性：前缀微调是⼀种通⽤的⽅法，可以适⽤于各种⽣成任务，包括⽂本摘要、问答、对话⽣<br>
成等。只需针对具体任务设计合适的前缀即可，⽆需重新训练整个模型，提⾼了模型的灵活性和可扩展<br>
性。<br>
  3. 数据效率：相⽐于从零开始训练⼀个⽣成模型，前缀微调利⽤了预训练语⾔模型的知识，可以在<br>
相对较少的任务数据上进⾏微调，从⽽节省了⼤量的训练时间和资源。这对于数据稀缺的任务或领域来<br>
说尤为重要。<br>
  4. 提⾼⽣成效果：通过引⼊任务相关的前缀，前缀微调可以帮助模型更好地理解任务要求，⽣成更<br>
准确、更相关的内容。相⽐于传统的预训练语⾔模型，前缀微调在特定任务上往往能够取得更好的性<br>
能。<br>
</p>

<h2>第 54 页</h2>

<p>4.2 指示微调（Prompt-tuning）篇<br>
4.2.1 为什么需要 指示微调（Prompt-tuning）？<br>
  5. 可解释性：前缀微调中的前缀可以包含任务的要求、指导或关键信息，这使得模型⽣成的结果更<br>
加可解释。通过分析前缀和⽣成结果之间的关系，可以更好地理解模型在任务中的决策过程，从⽽更好<br>
地调试和优化模型。<br>
  综上所述，前缀微调通过引⼊任务相关的前缀，提⾼了⽣成模型的可控性、灵活性和⽣成效果，同时<br>
还具备数据效率和可解释性的优势。这使得前缀微调成为⼀种有效的⽅法，⽤于提升⽣成任务的性能和<br>
可控性。<br>
  &lt;/aside&gt;<br>
- 4.1.4 前缀微调（Prefix-tuning）的缺点是什么？<br>
  &lt;aside&gt;<br>
  <br>
  尽管前缀微调（Prefix-tuning）具有很多优点，但也存在⼀些缺点：<br>
  1. 前缀设计的挑战：前缀的设计需要考虑到任务的要求、指导或关键信息，以便正确引导模型⽣成<br>
相关内容。设计⼀个合适的前缀可能需要领域知识和⼈⼯调整，这可能会增加任务的复杂性和⼯作量。<br>
  2. 任务依赖性：前缀微调是⼀种针对特定任务的⽅法，模型的性能和⽣成效果⾼度依赖于任务数据<br>
和前缀的设计。如果任务数据不⾜或前缀设计不合理，可能会导致模型性能下降或⽣成结果不符合预<br>
期。<br>
  3. 预训练偏差：预训练语⾔模型的偏差可能会在前缀微调中得以保留或放⼤。如果预训练模型在某<br>
些⽅⾯存在偏差或不准确性，前缀微调可能⽆法完全纠正这些问题，导致⽣成结果仍然存在偏差。<br>
  4. 对任务数据的依赖：前缀微调需要特定任务的数据⽤于微调预训练模型，如果任务数据不充分或<br>
不代表性，可能⽆法充分发挥前缀微调的优势。此外，前缀微调可能对不同任务需要单独进⾏微调，这<br>
可能需要更多的任务数据和⼈⼒资源。<br>
  5. 可解释性的限制：虽然前缀微调可以增加⽣成结果的可解释性，但模型的内部决策过程仍然是⿊<br>
盒的。模型在⽣成过程中的具体决策和推理过程可能难以解释，这可能限制了对模型⾏为的深⼊理解和<br>
调试。<br>
  综上所述，前缀微调虽然有很多优点，但也存在⼀些挑战和限制。在实际应⽤中，需要仔细考虑前缀<br>
设计、任务数据和模型的偏差等因素，以充分发挥前缀微调的优势并解决其潜在的缺点。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 55 页</h2>

<p>  指示微调（Prompt-tuning）是⼀种⽤于⽣成任务的微调⽅法，它的出现主要是为了解决前缀微调<br>
（Prefix-tuning）中前缀设计的挑战和限制。以下是需要指示微调的⼏个原因：<br>
  1. 前缀设计的复杂性：前缀微调需要设计合适的前缀来引导模型⽣成相关内容。然⽽，前缀的设计<br>
可能需要领域知识和⼈⼯调整，这增加了任务的复杂性和⼯作量。指示微调通过使⽤简洁的指示语句来<br>
替代复杂的前缀设计，简化了任务的准备过程。<br>
  2. 指导信息的⼀致性：前缀微调中的前缀需要包含任务的要求、指导或关键信息。然⽽，前缀的设<br>
计可能存在主观性和不确定性，导致模型⽣成结果的⼀致性较差。指示微调通过使⽤明确和⼀致的指示<br>
语句来提供指导信息，可以更好地控制模型⽣成的结果，提⾼⼀致性和可控性。<br>
  3. 任务的多样性和灵活性：前缀微调中的前缀是针对特定任务设计的，对于不同的任务需要单独进<br>
⾏微调。这对于多样的任务和领域来说可能需要更多的任务数据和⼈⼒资源。指示微调通过使⽤通⽤的<br>
指示语句，可以适⽤于各种⽣成任务，提⾼了任务的灵活性和可扩展性。<br>
  4. 模型的可解释性：指示微调中的指示语句可以提供对模型⽣成结果的解释和指导。通过分析指示<br>
语句和⽣成结果之间的关系，可以更好地理解模型在任务中的决策过程，从⽽更好地调试和优化模型。<br>
  综上所述，指示微调通过使⽤简洁的指示语句替代复杂的前缀设计，提供明确和⼀致的指导信息，增<br>
加任务的灵活性和可解释性。这使得指示微调成为⼀种有⽤的⽅法，⽤于⽣成任务的微调，尤其适⽤于<br>
多样的任务和领域。<br>
  &lt;/aside&gt;<br>
- 4.2.2 指示微调（Prompt-tuning）思路是什么？<br>
  &lt;aside&gt;<br>
  <br>
  指示微调（Prompt-tuning）的思路是通过微调预训练模型，并使⽤简洁的指示语句来指导模型⽣<br>
成相关内容。以下是指示微调的基本思路：<br>
  1. 预训练模型：⾸先，使⽤⼤规模的⽆监督预训练任务（如语⾔模型、掩码语⾔模型等）来训练⼀<br>
个通⽤的语⾔模型。这个预训练模型能够学习到丰富的语⾔知识和语义表示。<br>
  2. 指示语句的设计：为了指导模型⽣成相关内容，需要设计简洁明确的指示语句。指示语句应该包<br>
含任务的要求、指导或关键信息，以引导模型⽣成符合任务要求的结果。指示语句可以是⼀个完整的句<br>
⼦、⼀个问题、⼀个关键词等，具体的设计取决于任务的需求。<br>
  3. 微调过程：在微调阶段，将预训练模型与任务数据相结合，使⽤指示语句来微调模型。微调的⽬<br>
标是通过优化模型参数，使得模型能够根据指示语句⽣成符合任务要求的结果。微调可以使⽤监督学习<br>
的⽅法，通过最⼩化任务数据的损失函数来更新模型参数。<br>
  4. 模型⽣成：经过微调后，模型可以根据给定的指示语句来⽣成相关内容。模型会利⽤预训练的语<br>
⾔知识和微调的任务导向来⽣成符合指示的结果。⽣成的结果可以是⼀个句⼦、⼀段⽂字、⼀张图⽚<br>
等，具体取决于任务类型。<br>
  通过指示微调，可以在预训练模型的基础上，使⽤简洁明确的指示语句来指导模型⽣成相关内容。这<br>
种⽅法简化了任务的准备过程，提⾼了任务的灵活性和可控性，并增加了模型⽣成结果的⼀致性和可解<br>
释性。<br>
</p>

<h2>第 56 页</h2>

<p>  &lt;/aside&gt;<br>
- 4.2.3 指示微调（Prompt-tuning）优点是什么？<br>
  &lt;aside&gt;<br>
  <br>
  指示微调（Prompt-tuning）具有以下⼏个优点：<br>
  1. 灵活性和可扩展性：指示微调使⽤通⽤的指示语句来指导模型⽣成任务相关内容，⽽不需要针对<br>
每个任务设计特定的前缀。这使得指示微调更加灵活和可扩展，可以适⽤于各种不同的⽣成任务和领<br>
域。<br>
  2. 简化任务准备：相⽐于前缀微调，指示微调减少了任务准备的复杂性。前缀设计可能需要领域知<br>
识和⼈⼯调整，⽽指示语句通常更简洁明确，减少了任务准备的时间和⼯作量。<br>
  3. ⼀致性和可控性：指示微调使⽤明确的指示语句来指导模型⽣成结果，提⾼了⽣成结果的⼀致性<br>
和可控性。指示语句可以提供任务的要求、指导或关键信息，使得模型⽣成的结果更加符合任务需求。<br>
  4. 可解释性：指示微调中的指示语句可以提供对模型⽣成结果的解释和指导。通过分析指示语句和<br>
⽣成结果之间的关系，可以更好地理解模型在任务中的决策过程，从⽽更好地调试和优化模型。<br>
  5. 效果提升：指示微调通过使⽤指示语句来引导模型⽣成任务相关内容，可以提⾼⽣成结果的质量<br>
和准确性。指示语句可以提供更明确的任务要求和指导信息，帮助模型更好地理解任务，并⽣成更符合<br>
要求的结果。<br>
  综上所述，指示微调具有灵活性和可扩展性、简化任务准备、⼀致性和可控性、可解释性以及效果提<br>
升等优点。这使得指示微调成为⼀种有⽤的⽅法，⽤于⽣成任务的微调。<br>
  &lt;/aside&gt;<br>
- 4.2.4 指示微调（Prompt-tuning）缺点是什么？<br>
  &lt;aside&gt;<br>
  <br>
  指示微调（Prompt-tuning）也存在⼀些缺点，包括以下⼏点：<br>
  1. 依赖于设计良好的指示语句：指示微调的效果很⼤程度上依赖于设计良好的指示语句。如果指示<br>
语句不够明确、不够准确或不够全⾯，可能导致模型⽣成的结果不符合任务要求。因此，需要投⼊⼀定<br>
的时间和精⼒来设计和优化指示语句。<br>
  2. 对任务理解的依赖：指示微调要求模型能够准确理解指示语句中的任务要求和指导信息。如果模<br>
型对任务理解存在偏差或困惑，可能会导致⽣成结果的不准确或不符合预期。这需要在微调过程中充分<br>
训练和调整模型，以提⾼任务理解的准确性。<br>
</p>

<h2>第 57 页</h2>

<p>  3. 对⼤规模数据的依赖：指示微调通常需要⼤规模的任务数据来进⾏微调训练。这可能对于某些任<br>
务和领域来说是⼀个挑战，因为获取⼤规模的⾼质量任务数据可能是困难的。缺乏⾜够的任务数据可能<br>
会限制指示微调的效果和泛化能⼒。<br>
  4. 可能导致过度指导：指示微调中使⽤的指示语句可能会过度指导模型⽣成结果，导致⽣成内容过<br>
于机械化或缺乏创造性。过度指导可能会限制模型的多样性和创新性，使得⽣成结果缺乏多样性和惊喜<br>
性。<br>
  5. 难以处理复杂任务：对于⼀些复杂的任务，简单的指示语句可能⽆法提供⾜够的信息来指导模型<br>
⽣成复杂的结果。这可能需要设计更复杂的指示语句或采⽤其他更复杂的⽅法来解决任务。<br>
  综上所述，指示微调虽然具有⼀些优点，但也存在⼀些缺点。需要在设计指示语句、任务理解、数据<br>
获取和处理复杂任务等⽅⾯进⾏充分考虑和优化，以克服这些缺点并提⾼指示微调的效果。<br>
  &lt;/aside&gt;<br>
- 4.2.5 指示微调（Prompt-tuning）与 Prefix-tuning 区别 是什么？<br>
  &lt;aside&gt;<br>
  <br>
  指示微调（Prompt-tuning）和前缀微调（Prefix-tuning）是两种不同的⽅法，⽤于指导⽣成<br>
模型⽣成任务相关内容的技术。它们之间的区别包括以下⼏个⽅⾯：<br>
  1. 输⼊形式：指示微调使⽤通⽤的指示语句来指导模型⽣成结果，这些指示语句通常作为输⼊的⼀<br>
部分。⽽前缀微调则在输⼊⽂本前添加⼀个特定的前缀，⽤于指导模型⽣成结果。<br>
  2. 灵活性：指示微调更加灵活和可扩展，可以适⽤于各种不同的⽣成任务和领域。指示语句可以根<br>
据任务的要求和指导进⾏设计，⽽不需要针对每个任务设计特定的前缀。前缀微调则需要为每个任务设<br>
计特定的前缀，这可能需要领域知识和⼈⼯调整。<br>
  3. 任务准备：前缀微调可能需要更多的任务准备⼯作，包括设计和调整前缀，以及对前缀的领域知<br>
识和语法规则的理解。⽽指示微调的任务准备相对简化，指示语句通常更简洁明确，减少了任务准备的<br>
时间和⼯作量。<br>
  4. ⼀致性和可控性：指示微调使⽤明确的指示语句来指导模型⽣成结果，提⾼了⽣成结果的⼀致性<br>
和可控性。指示语句可以提供任务的要求、指导或关键信息，使得模型⽣成的结果更加符合任务需求。<br>
前缀微调的⼀致性和可控性取决于前缀的设计和使⽤⽅式。<br>
  5. 可解释性：指示微调中的指示语句可以提供对模型⽣成结果的解释和指导。通过分析指示语句和<br>
⽣成结果之间的关系，可以更好地理解模型在任务中的决策过程，从⽽更好地调试和优化模型。前缀微<br>
调的解释性相对较弱，前缀通常只是作为⽣成结果的⼀部分，不提供明确的解释和指导。<br>
  综上所述，指示微调和前缀微调在输⼊形式、灵活性、任务准备、⼀致性和可控性以及可解释性等⽅<br>
⾯存在差异。选择哪种⽅法取决于具体的任务需求和实际应⽤场景。<br>
  &lt;/aside&gt;<br>
- 4.2.6 指示微调（Prompt-tuning）与 fine-tuning 区别 是什么？<br>
</p>

<h2>第 58 页</h2>

<p>4.3 P-tuning 篇<br>
4.3.1 为什么需要 P-tuning？<br>
  &lt;aside&gt;<br>
  <br>
  指示微调（Prompt-tuning）和微调（Fine-tuning）是两种不同的迁移学习⽅法，⽤于对预训<br>
练的⽣成模型进⾏任务特定的调整。它们之间的区别包括以下⼏个⽅⾯：<br>
  1. 调整的⽬标：指示微调主要关注如何通过设计明确的指示语句来指导模型⽣成任务相关内容。指<br>
示语句通常作为输⼊的⼀部分，⽤于引导模型⽣成结果。微调则是通过在预训练模型的基础上对特定任<br>
务进⾏端到端的训练，⽬标是优化模型在特定任务上的性能。<br>
  2. 指导的⽅式：指示微调通过指示语句提供明确的任务指导和要求，以引导模型⽣成结果。指示语<br>
句通常是⼈⼯设计的，并且可以根据任务需求进⾏调整。微调则是通过在特定任务上进⾏训练，使⽤任<br>
务相关的数据来调整模型参数，使其适应任务要求。<br>
  3. 数据需求：指示微调通常需要⼤规模的任务数据来进⾏微调训练。这些数据⽤于⽣成指示语句和<br>
模型⽣成结果之间的对应关系，以及评估模型的性能。微调也需要任务相关的数据来进⾏训练，但相对<br>
于指示微调，微调可能需要更多的任务数据来进⾏端到端的训练。<br>
  4. 灵活性和通⽤性：指示微调更加灵活和通⽤，可以适⽤于各种不同的⽣成任务和领域。指示语句<br>
可以根据任务要求和指导进⾏设计，⽽不需要针对每个任务进⾏特定的微调。微调则是针对特定任务进<br>
⾏的调整，需要在每个任务上进⾏微调训练。<br>
  5. 迁移学习的程度：指示微调可以看作是⼀种迁移学习的形式，通过在预训练模型上进⾏微调，将<br>
模型的知识迁移到特定任务上。微调也是⼀种迁移学习的⽅法，但它更加深⼊，通过在特定任务上进⾏<br>
端到端的训练，调整模型参数以适应任务要求。<br>
  综上所述，指示微调和微调在⽬标、指导⽅式、数据需求、灵活性和通⽤性以及迁移学习的程度等⽅<br>
⾯存在差异。选择哪种⽅法取决于具体的任务需求、数据可⽤性和实际应⽤场景。<br>
  &lt;/aside&gt;<br>
  指示微调（Prompt-tuning，简称P-tuning）提供了⼀种有效的⽅式来指导⽣成模型⽣成任务相<br>
关的内容。以下是⼀些使⽤P-tuning的原因：<br>
  1. 提⾼⽣成结果的⼀致性和可控性：⽣成模型在没有明确指导的情况下可能会产⽣不⼀致或不符合<br>
任务要求的结果。通过使⽤指示语句来指导模型⽣成结果，可以提⾼⽣成结果的⼀致性和可控性。指示<br>
语句可以提供任务的要求、指导或关键信息，使得模型⽣成的结果更加符合任务需求。<br>
  2. 减少⼈⼯设计和调整的⼯作量：在⼀些⽣成任务中，需要设计和调整⽣成模型的输⼊，以使其⽣<br>
成符合任务要求的结果。使⽤P-tuning，可以通过设计明确的指示语句来指导模型⽣成结果，⽽不需<br>
要进⾏复杂的输⼊设计和调整。这减少了⼈⼯设计和调整的⼯作量，提⾼了任务的效率。<br>
</p>

<h2>第 59 页</h2>

<p>  3. ⽀持多样的⽣成任务和领域：P-tuning是⼀种通⽤的⽅法，可以适⽤于各种不同的⽣成任务和<br>
领域。指示语句可以根据任务的要求和指导进⾏设计，从⽽适应不同任务的需求。这种通⽤性使得P-<br>
tuning成为⼀个灵活和可扩展的⽅法，可以应⽤于各种⽣成任务，如⽂本⽣成、图像⽣成等。<br>
  4. 提⾼模型的可解释性：指示语句可以提供对模型⽣成结果的解释和指导。通过分析指示语句和⽣<br>
成结果之间的关系，可以更好地理解模型在任务中的决策过程，从⽽更好地调试和优化模型。这提⾼了<br>
模型的可解释性，使得模型的结果更容易被理解和接受。<br>
  综上所述，P-tuning提供了⼀种有效的⽅式来指导⽣成模型⽣成任务相关的内容，提⾼了⽣成结果<br>
的⼀致性和可控性，减少了⼈⼯设计和调整的⼯作量，并⽀持多样的⽣成任务和领域。这使得P-<br>
tuning成为⼀种重要的技术，被⼴泛应⽤于⽣成模型的任务调整和优化中。<br>
  &lt;/aside&gt;<br>
- 4.3.2 P-tuning 思路是什么？<br>
  &lt;aside&gt;<br>
  <br>
  P-tuning的思路是通过设计明确的指示语句来指导⽣成模型⽣成任务相关的内容。下⾯是P-<br>
tuning的基本思路：<br>
  1. 设计指示语句：根据任务的要求和指导，设计明确的指示语句，⽤于引导⽣成模型⽣成符合任务<br>
要求的结果。指示语句可以包含任务的要求、关键信息、约束条件等。<br>
  2. 构建输⼊：将指示语句与任务相关的输⼊进⾏组合，构建⽣成模型的输⼊。⽣成模型的输⼊通常<br>
由指示语句和任务相关的上下⽂信息组成。<br>
  3. 模型⽣成：将构建好的输⼊输⼊到⽣成模型中，⽣成任务相关的结果。⽣成模型可以是预训练的<br>
语⾔模型，如GPT、BERT等。<br>
  4. 评估⽣成结果：根据任务的评估指标，对⽣成的结果进⾏评估。评估可以是⾃动评估，如<br>
BLEU、ROUGE等，也可以是⼈⼯评估。<br>
  5. 调整指示语句：根据评估结果，对指示语句进⾏调整和优化。可以调整指示语句的内容、⻓度、<br>
语⾔⻛格等，以提⾼⽣成结果的质量和符合度。<br>
  6. 迭代优化：反复进⾏上述步骤，不断优化指示语句和⽣成模型，以达到更好的⽣成结果。<br>
  P-tuning的关键在于设计明确的指示语句，它起到了指导⽣成模型⽣成结果的作⽤。指示语句可以<br>
通过⼈⼯设计、规则抽取、⾃动搜索等⽅式得到。通过不断优化指示语句和⽣成模型，可以提⾼⽣成结<br>
果的⼀致性、可控性和质量。<br>
  需要注意的是，P-tuning是⼀种迁移学习的⽅法，通常是在预训练的⽣成模型上进⾏微调。微调的<br>
⽬的是将模型的知识迁移到特定任务上，使其更适应任务要求。P-tuning可以看作是⼀种迁移学习的<br>
形式，通过在预训练模型上进⾏微调来指导⽣成模型⽣成任务相关的内容。<br>
  &lt;/aside&gt;<br>
- 4.3.3 P-tuning 优点是什么？<br>
</p>

<h2>第 60 页</h2>

<p>  &lt;aside&gt;<br>
  <br>
  P-tuning具有以下⼏个优点：<br>
  1. 提⾼⽣成结果的⼀致性和可控性：通过使⽤指示语句来指导⽣成模型⽣成结果，可以提⾼⽣成结<br>
果的⼀致性和可控性。指示语句可以提供任务的要求、指导或关键信息，使得模型⽣成的结果更加符合<br>
任务需求。这样可以减少⽣成结果的偏差和不符合任务要求的情况。<br>
  2. 减少⼈⼯设计和调整的⼯作量：使⽤P-tuning，可以通过设计明确的指示语句来指导模型⽣成<br>
结果，⽽不需要进⾏复杂的输⼊设计和调整。这减少了⼈⼯设计和调整的⼯作量，提⾼了任务的效率。<br>
同时，P-tuning还可以减少⼈⼯设计指示语句的⼯作量，通过⾃动搜索或规则抽取等⽅式来获取指示<br>
语句。<br>
  3. 适⽤于多样的⽣成任务和领域：P-tuning是⼀种通⽤的⽅法，可以适⽤于各种不同的⽣成任务<br>
和领域。指示语句可以根据任务的要求和指导进⾏设计，从⽽适应不同任务的需求。这种通⽤性使得P-<br>
tuning成为⼀个灵活和可扩展的⽅法，可以应⽤于各种⽣成任务，如⽂本⽣成、图像⽣成等。<br>
  4. 提⾼模型的可解释性：指示语句可以提供对模型⽣成结果的解释和指导。通过分析指示语句和⽣<br>
成结果之间的关系，可以更好地理解模型在任务中的决策过程，从⽽更好地调试和优化模型。这提⾼了<br>
模型的可解释性，使得模型的结果更容易被理解和接受。<br>
  综上所述，P-tuning通过设计明确的指示语句来指导⽣成模型⽣成任务相关的内容，提⾼了⽣成结<br>
果的⼀致性和可控性，减少了⼈⼯设计和调整的⼯作量，并⽀持多样的⽣成任务和领域。这使得P-<br>
tuning成为⼀种重要的技术，被⼴泛应⽤于⽣成模型的任务调整和优化中。<br>
  &lt;/aside&gt;<br>
- 4.3.4 P-tuning 缺点是什么？<br>
  &lt;aside&gt;<br>
  <br>
</p>

<h2>第 61 页</h2>

<p>4.4 P-tuning v2 篇<br>
4.4.1 为什么需要 P-tuning v2？<br>
  虽然P-tuning有⼀些优点，但也存在以下⼏个缺点：<br>
  1. 需要⼤量的⼈⼯设计和调整：尽管P-tuning可以减少⼈⼯设计和调整的⼯作量，但仍然需要⼈<br>
⼯设计明确的指示语句来指导⽣成模型。这需要领域专家或任务设计者具有⼀定的专业知识和经验，以<br>
确保⽣成结果的质量和符合度。此外，如果⽣成任务涉及多个⽅⾯或多个约束条件，指示语句的设计可<br>
能会变得更加复杂和困难。<br>
  2. 需要⼤量的训练数据和计算资源：P-tuning通常需要⼤量的训练数据来微调预训练的⽣成模<br>
型。这可能会对数据的收集和标注造成困难，尤其是对于某些特定领域或任务⽽⾔。此外，P-tuning<br>
还需要⼤量的计算资源来进⾏模型的微调和优化，这可能对计算资源有⼀定的要求。<br>
  3. 可能存在指示语句与任务需求不匹配的问题：指示语句的设计可能会受到⼈为因素的影响，导致<br>
与任务需求不匹配。如果指示语句没有准确地表达任务的要求或关键信息，⽣成模型可能会⽣成不符合<br>
任务需求的结果。因此，设计准确和有效的指示语句是⼀个挑战。<br>
  4. ⽣成结果的质量和多样性平衡问题：P-tuning的⽬标是⽣成符合任务要求的结果，但有时候可<br>
能会牺牲⽣成结果的多样性。由于指示语句的引导，⽣成模型可能会过度关注任务要求，导致⽣成结果<br>
过于单⼀和刻板。这可能会降低⽣成结果的创新性和多样性。<br>
  综上所述，P-tuning虽然有⼀些优点，但也存在⼀些缺点。需要权衡⼈⼯设计和调整的⼯作量、训<br>
练数据和计算资源的需求，以及⽣成结果的质量和多样性平衡等问题。这些缺点需要在实际应⽤中进⾏<br>
考虑和解决，以提⾼P-tuning的效果和性能。<br>
  &lt;/aside&gt;<br>
  P-tuning v2是对P-tuning⽅法的改进和升级，主要出于以下⼏个原因：<br>
  1. 解决指示语句与任务需求不匹配的问题：在P-tuning中，指示语句的设计可能存在与任务需求<br>
不匹配的问题，导致⽣成结果不符合预期。P-tuning v2可以通过引⼊更加灵活和智能的指示语句⽣<br>
成机制，使得指示语句更准确地表达任务的要求和关键信息，从⽽提⾼⽣成结果的符合度。<br>
  2. 提⾼⽣成结果的多样性：在P-tuning中，由于指示语句的引导，⽣成结果可能会过于单⼀和刻<br>
板，导致多样性不⾜。P-tuning v2可以通过引⼊新的⽣成策略和技术，如多样性增强机制、多模态<br>
⽣成等，来提⾼⽣成结果的多样性，使得⽣成结果更具创新性和丰富性。<br>
  3. 减少⼈⼯设计和调整的⼯作量：在P-tuning中，⼈⼯设计和调整指示语句是⼀项耗时且困难的<br>
任务。P-tuning v2可以通过引⼊⾃动化的指示语句⽣成和优化⽅法，如基于强化学习的⾃动指导⽣<br>
成、迁移学习等，来减少⼈⼯设计和调整的⼯作量，提⾼任务的效率和可扩展性。<br>
  4. ⽀持更多的⽣成任务和领域：P-tuning v2可以扩展到更多的⽣成任务和领域，如⾃然语⾔处<br>
理、计算机视觉、语⾳合成等。通过设计适应不同任务和领域的指示语句⽣成机制和模型结构，P-<br>
tuning v2可以适⽤于更⼴泛的应⽤场景，提供更加定制化和专业化的⽣成结果。<br>
</p>

<h2>第 62 页</h2>

<p>  综上所述，P-tuning v2的出现是为了解决P-tuning⽅法存在的问题，并提供更加准确、多样和<br>
⾼效的⽣成结果。通过引⼊新的技术和策略，P-tuning v2可以进⼀步提升⽣成模型的性能和应⽤范<br>
围，满⾜不同任务和领域的需求。<br>
  &lt;/aside&gt;<br>
- 4.4.2 P-tuning v2 思路是什么？<br>
  &lt;aside&gt;<br>
  <br>
  P-tuning v2的思路主要包括以下⼏个⽅⾯：<br>
  1. ⾃动化指示语句⽣成：P-tuning v2致⼒于减少⼈⼯设计和调整指示语句的⼯作量。为此，可<br>
以引⼊⾃动化⽅法来⽣成指示语句。例如，可以使⽤基于强化学习的⽅法，在给定任务需求和⽣成模型<br>
的情况下，⾃动学习⽣成合适的指示语句。这样可以减少⼈⼯参与，并提⾼指示语句的准确性和效率。<br>
  2. 多样性增强机制：为了提⾼⽣成结果的多样性，P-tuning v2可以引⼊多样性增强机制。例<br>
如，可以在⽣成过程中引⼊随机性，通过对⽣成模型的采样和扰动，⽣成多个不同的结果。此外，还可<br>
以使⽤多模态⽣成的⽅法，结合不同的输⼊模态（如⽂本、图像、⾳频等），⽣成更加多样化和丰富的<br>
结果。<br>
  3. 模型结构和优化改进：P-tuning v2可以通过改进⽣成模型的结构和优化⽅法，提升⽣成结果<br>
的质量和效率。例如，可以设计更加复杂和强⼤的⽣成模型，如使⽤深度神经⽹络或注意⼒机制来捕捉<br>
更多的语义信息和上下⽂关联。此外，还可以引⼊迁移学习的⽅法，利⽤预训练的模型进⾏初始化和参<br>
数共享，加速模型的训练和优化过程。<br>
  4. ⾯向特定任务和领域的优化：P-tuning v2可以针对特定任务和领域进⾏优化。通过深⼊了解<br>
任务需求和领域特点，可以设计针对性的指示语句⽣成机制和模型结构。例如，在⾃然语⾔处理任务<br>
中，可以设计专⻔的语法和语义约束，以⽣成符合语法规则和语义关系的结果。这样可以提⾼⽣成结果<br>
的准确性和可理解性。<br>
  综上所述，P-tuning v2的思路是通过⾃动化指示语句⽣成、多样性增强机制、模型结构和优化改<br>
进，以及⾯向特定任务和领域的优化，来提升⽣成模型的性能和应⽤范围。通过这些改进，P-tuning <br>
v2可以更好地满⾜不同任务和领域的需求，⽣成更准确、多样和⾼效的结果。<br>
  &lt;/aside&gt;<br>
- 4.4.3 P-tuning v2 优点是什么？<br>
  &lt;aside&gt;<br>
  <br>
  P-tuning v2相⽐于P-tuning具有以下⼏个优点：<br>
</p>

<h2>第 63 页</h2>

<p>  1. 提⾼⽣成结果的准确性：P-tuning v2通过改进指示语句⽣成机制和模型结构，可以⽣成更准<br>
确符合任务需求的结果。⾃动化指示语句⽣成和优化⽅法可以减少⼈⼯设计和调整的⼯作量，提⾼指示<br>
语句的准确性和效率。此外，引⼊更复杂和强⼤的⽣成模型，如深度神经⽹络和注意⼒机制，可以捕捉<br>
更多的语义信息和上下⽂关联，进⼀步提⾼⽣成结果的准确性。<br>
  2. 增加⽣成结果的多样性：P-tuning v2通过引⼊多样性增强机制，可以⽣成更多样化和丰富的<br>
结果。随机性和多模态⽣成的⽅法可以在⽣成过程中引⼊变化和多样性，⽣成多个不同的结果。这样可<br>
以提⾼⽣成结果的创新性和多样性，满⾜⽤户对多样性结果的需求。<br>
  3. 减少⼈⼯设计和调整的⼯作量：P-tuning v2通过⾃动化指示语句⽣成和优化⽅法，可以减少<br>
⼈⼯设计和调整指示语句的⼯作量。⾃动化⽅法可以根据任务需求和⽣成模型⾃动学习⽣成合适的指示<br>
语句，减少了⼈⼯参与的需求。这样可以提⾼任务的效率和可扩展性，减轻⼈⼯⼯作负担。<br>
  4. 适应更多的⽣成任务和领域：P-tuning v2可以扩展到更多的⽣成任务和领域，提供更加定制<br>
化和专业化的⽣成结果。通过针对特定任务和领域进⾏优化，设计适应性更强的指示语句⽣成机制和模<br>
型结构，P-tuning v2可以适⽤于不同的应⽤场景，满⾜不同任务和领域的需求。<br>
  综上所述，P-tuning v2相⽐于P-tuning具有提⾼⽣成结果准确性、增加⽣成结果多样性、减少<br>
⼈⼯⼯作量和适应更多任务和领域的优点。这些优点使得P-tuning v2在⽣成任务中具有更⾼的性能<br>
和应⽤价值。<br>
  &lt;/aside&gt;<br>
- 4.4.4 P-tuning v2 缺点是什么？<br>
  &lt;aside&gt;<br>
  <br>
</p>

<h2>第 64 页</h2>

<p>LoRA 系列篇<br>
 <br>
⼀、LoRA篇<br>
1.1 什么是 LoRA？<br>
1.2 LoRA 的思路是什么？<br>
1.3 LoRA 的特点是什么？<br>
⼆、QLoRA篇<br>
2.1 QLoRA 的思路是怎么样的？<br>
2.2 QLoRA 的特点是什么？<br>
三、AdaLoRA篇<br>
3.1 AdaLoRA 的思路是怎么样的？<br>
四、LoRA权重是否可以合⼊原模型？<br>
五、ChatGLM-6B LoRA后的权重多⼤？<br>
六、LoRA 微调优点是什么？<br>
七、LoRA微调⽅法为啥能加速训练？<br>
⼋、如何在已有LoRA模型上继续训练？<br>
九、LoRA 缺点是什么？<br>
  P-tuning v2的⼀些潜在缺点包括：<br>
  1. 训练和优化复杂度⾼：P-tuning v2通过引⼊更复杂和强⼤的⽣成模型、多样性增强机制和优<br>
化⽅法来提升性能。然⽽，这也会增加训练和优化的复杂度和计算资源需求。训练⼀个复杂的⽣成模型<br>
可能需要更⻓的时间和更⾼的计算资源，⽽优化过程可能需要更多的迭代和调试。<br>
  2. 指示语句⽣成的准确性限制：P-tuning v2依赖于⾃动化指示语句⽣成，从⽽减少了⼈⼯设计<br>
和调整的⼯作量。然⽽，⾃动化⽣成的指示语句可能存在准确性的限制。⽣成的指示语句可能⽆法完全<br>
准确地描述任务需求，导致⽣成结果的不准确性。因此，需要对⽣成的指示语句进⾏验证和调整，以确<br>
保⽣成结果的质量。<br>
  3. 多样性增强可能导致⽣成结果的不稳定性：P-tuning v2引⼊了多样性增强机制来⽣成更多样<br>
化和丰富的结果。然⽽，这种多样性增强可能会导致⽣成结果的不稳定性。不同的采样和扰动可能导致<br>
⽣成结果的差异较⼤，难以保持⼀致性和可控性。因此，在使⽤多样性增强机制时需要注意结果的稳定<br>
性和可控性。<br>
  4. 需要⼤量的训练数据和标注：P-tuning v2的性能往往受限于训练数据的质量和数量。为了训<br>
练和优化复杂的⽣成模型，通常需要⼤量的训练数据和标注。然⽽，获取⼤规模的⾼质量训练数据是⼀<br>
项挑战。此外，如果任务和领域特定的训练数据不⾜，可能会影响P-tuning v2在特定任务和领域的<br>
性能。<br>
  综上所述，P-tuning v2的⼀些潜在缺点包括训练和优化复杂度⾼、指示语句⽣成的准确性限制、<br>
多样性增强可能导致结果的不稳定性以及对⼤量训练数据和标注的需求。这些缺点需要在使⽤P-<br>
tuning v2时注意，并根据具体情况进⾏权衡和调整。<br>
  &lt;/aside&gt;<br>
</p>

<h2>第 65 页</h2>

<p>⼗、LoRA这种微调⽅法和全参数⽐起来有什么劣势吗？<br>
LoRA 系列篇<br>
 <br>
⼀、LoRA篇<br>
 **什么是low-rank adaptation of large language models？**<br>
1.1 什么是 LoRA？<br>
1.2 LoRA 的思路是什么？<br>
"low-rank adaptation of large language models" 是⼀种针对⼤型语⾔模型进⾏低秩适应<br>
的技术。⼤型语⾔模型通常具有数⼗亿个参数，这使得它们在计算和存储⽅⾯⾮常昂贵。低秩适应的⽬<br>
标是通过将语⾔模型的参数矩阵分解为低秩近似，来减少模型的复杂度和计算资源的需求。<br>
低秩适应的⽅法可以通过使⽤矩阵分解技术，如奇异值分解（Singular Value Decomposition，<br>
SVD）或特征值分解（Eigenvalue Decomposition），将语⾔模型的参数矩阵分解为较低秩的近似<br>
矩阵。通过这种⽅式，可以减少模型的参数量和计算复杂度，同时保留模型的关键特征和性能。<br>
低秩适应的技术可以⽤于加速⼤型语⾔模型的推理过程，减少模型的存储需求，并提⾼在资源受限环境<br>
下的模型效率。它是在⼤型语⾔模型优化和压缩领域的⼀个重要研究⽅向。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 66 页</h2>

<p>1.3 LoRA 的特点是什么？<br>
LoRA（Low-Rank Adaptation）是⼀种⽤于⼤规模语⾔模型的低秩适应⽅法，旨在减少模型的计算<br>
和存储开销。它的核⼼思想是通过对模型参数矩阵进⾏低秩分解，以达到降低模型复杂度和提⾼效率的<br>
⽬的。<br>
具体⽽⾔，LoRA的思路可以分为以下⼏个步骤：<br>
1. 原始模型：⾸先，我们有⼀个⼤规模的语⾔模型，其参数矩阵往往⾮常庞⼤，包含数⼗亿个参数。<br>
2. 参数矩阵分解：LoRA通过对模型参数矩阵进⾏低秩分解，将其分解为两个或多个较⼩的矩阵的乘<br>
积。常⽤的矩阵分解⽅法包括奇异值分解（SVD）和特征值分解（Eigenvalue Decomposition）<br>
等。<br>
3. 低秩适应：在参数矩阵分解之后，我们可以选择保留较低秩的近似矩阵，⽽舍弃⼀些对模型性能影<br>
响较⼩的细微变化。这样可以减少模型的参数量和计算复杂度，同时保留模型的关键特征和性能。<br>
4. 重构模型：使⽤低秩适应后的近似矩阵重新构建语⾔模型，该模型具有较低的参数量和计算需求，<br>
但仍能保持相对较⾼的性能。<br>
通过LoRA的低秩适应⽅法，我们可以在不显著损失模型性能的情况下，降低⼤型语⾔模型的复杂度和<br>
资源需求。这对于在计算资源受限的环境下部署和使⽤语⾔模型⾮常有帮助。<br>
&lt;/aside&gt;<br>
LoRA（Low-Rank Adaptation）具有以下⼏个特点：<br>
1. 低秩适应：LoRA通过对⼤型语⾔模型的参数矩阵进⾏低秩分解，将其分解为较⼩的矩阵的乘积。这<br>
样可以减少模型的参数量和计算复杂度，提⾼模型的效率和速度。<br>
2. 保持关键特征：虽然低秩适应会舍弃⼀些对模型性能影响较⼩的细节，但它会尽量保留模型的关键<br>
特征。通过选择合适的低秩近似矩阵，可以最⼤限度地保持模型的性能。<br>
3. 减少存储需求：低秩适应可以显著减少⼤型语⾔模型的参数量，从⽽降低模型的存储需求。这对于<br>
在资源受限的设备上使⽤语⾔模型⾮常有益。<br>
4. 加速推理过程：由于低秩适应减少了模型的计算复杂度，因此可以加速模型的推理过程。这对于实<br>
时应⽤和⼤规模部署⾮常重要。<br>
5. 可扩展性：LoRA的低秩适应⽅法可以应⽤于各种⼤型语⾔模型，包括预训练的Transformer模型<br>
等。它是⼀种通⽤的技术，可以适应不同的模型架构和任务。<br>
总之，LoRA通过低秩适应的⽅法，可以在减少存储需求和加速推理过程的同时，保持模型的关键特征<br>
和性能。这使得它成为在资源受限环境下使⽤⼤型语⾔模型的有效策略。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 67 页</h2>

<p>⼆、QLoRA篇<br>
2.1 QLoRA 的思路是怎么样的？<br>
2.2 QLoRA 的特点是什么？<br>
QLoRA（Quantized Low-Rank Adaptation）是⼀种结合了量化和低秩适应的⽅法，⽤于进⼀步<br>
减少⼤规模语⾔模型的计算和存储开销。它的思路可以概括如下：<br>
1. 量化参数：⾸先，对⼤规模语⾔模型的参数进⾏量化。量化是⼀种将浮点数参数转换为固定位数的<br>
整数或更简单表示的⽅法。通过减少参数位数，可以显著减少模型的存储需求和计算复杂度。<br>
2. 参数矩阵分解：在量化参数之后，QLoRA使⽤低秩分解的⽅法对量化参数矩阵进⾏分解。低秩分解<br>
将参数矩阵分解为较⼩的矩阵的乘积，从⽽进⼀步减少模型的参数量和计算复杂度。<br>
3. 低秩适应：在参数矩阵分解之后，选择保留较低秩的近似矩阵，并舍弃⼀些对模型性能影响较⼩的<br>
细节。这样可以进⼀步减少模型的计算需求，同时保持模型的关键特征和性能。<br>
4. 重构模型：使⽤低秩适应后的近似矩阵和量化参数重新构建语⾔模型。这样得到的模型既具有较低<br>
的参数量和计算需求，⼜能保持相对较⾼的性能。<br>
通过结合量化和低秩适应的思路，QLoRA能够进⼀步减少⼤型语⾔模型的计算和存储开销。它在资源受<br>
限的环境下，尤其是移动设备等场景中，具有重要的应⽤价值。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 68 页</h2>

<p>三、AdaLoRA篇<br>
3.1 AdaLoRA 的思路是怎么样的？<br>
QLoRA（Quantized Low-Rank Adaptation）具有以下⼏个特点：<br>
1. 量化降低存储需求：通过将参数进⾏量化，将浮点数参数转换为固定位数的整数或更简单的表示，<br>
从⽽显著减少模型的存储需求。这对于在资源受限的设备上使⽤⼤型语⾔模型⾮常有益。<br>
2. 低秩适应减少计算复杂度：通过低秩适应的⽅法，将量化参数矩阵分解为较⼩的矩阵的乘积，进⼀<br>
步减少模型的参数量和计算复杂度。这可以加速模型的推理过程，提⾼模型的效率。<br>
3. 保持关键特征和性能：虽然量化和低秩适应会舍弃⼀些对模型性能影响较⼩的细节，但它们会尽量<br>
保留模型的关键特征和性能。通过选择合适的量化位数和低秩近似矩阵，可以最⼤限度地保持模型的性<br>
能。<br>
4. 可扩展性和通⽤性：QLoRA的量化和低秩适应⽅法可以应⽤于各种⼤型语⾔模型，包括预训练的<br>
Transformer模型等。它是⼀种通⽤的技术，可以适应不同的模型架构和任务。<br>
5. 综合优化：QLoRA综合考虑了量化和低秩适应的优势，通过量化降低存储需求，再通过低秩适应减<br>
少计算复杂度，从⽽实现了更⾼效的模型。这使得QLoRA成为在资源受限环境下使⽤⼤型语⾔模型的有<br>
效策略。<br>
总之，QLoRA通过量化和低秩适应的⽅法，可以在减少存储需求和计算复杂度的同时，保持模型的关键<br>
特征和性能。它具有⾼效、通⽤和可扩展的特点，适⽤于各种⼤型语⾔模型的优化。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 69 页</h2>

<p>四、LoRA权重是否可以合⼊原模型？<br>
  是的，LoRA权重可以合并到原模型中。在使⽤LoRA进⾏低秩适应时，原始模型的参数矩阵会被分解<br>
为较⼩的矩阵的乘积。这些较⼩的矩阵可以表示为低秩矩阵的形式，其中包含了原始模型的权重信息。<br>
  合并LoRA权重到原模型的过程通常涉及将低秩矩阵重新组合成原始模型的参数矩阵。这可以通过矩阵<br>
乘法等操作来实现。合并后的模型将包含原始模型的权重信息，同时也融⼊了低秩适应的优化，从⽽在<br>
减少计算和存储开销的同时保持模型性能。<br>
  需要注意的是，合并LoRA权重到原模型时，可能会有⼀些微⼩的性能损失。这是因为低秩适应过程中<br>
对参数进⾏了量化和近似处理，可能会损失⼀些细节信息。然⽽，通过合适的低秩适应⽅法和参数设<br>
置，可以最⼩化这种性能损失，同时获得较⾼的效率和较低的资源开销。<br>
  <br>
五、ChatGLM-6B LoRA后的权重多⼤？<br>
六、LoRA 微调优点是什么？<br>
  LoRA微调具有以下⼏个优点：<br>
AdaLoRA（Adaptive Low-Rank Adaptation）是⼀种⾃适应的低秩适应⽅法，⽤于进⼀步减少⼤<br>
规模语⾔模型的计算和存储开销。它的思路可以概括如下：<br>
1. 初始低秩适应：⾸先，对⼤规模语⾔模型的参数进⾏低秩适应。低秩适应是⼀种将参数矩阵分解为<br>
较⼩的矩阵的乘积的⽅法，从⽽减少模型的参数量和计算复杂度。初始低秩适应的⽬的是在不损失太多<br>
性能的情况下，尽可能地减少模型的计算需求。<br>
2. 评估性能和复杂度：在进⾏初始低秩适应之后，评估模型的性能和计算复杂度。性能可以通过模型<br>
在验证集上的准确率等指标来衡量，⽽计算复杂度可以通过模型的计算量来衡量。<br>
3. ⾃适应调整：根据评估的结果，如果模型的性能满⾜要求，那么适应低秩矩阵可以作为最终模型的<br>
参数。如果模型的性能不满⾜要求，那么可以考虑增加低秩适应的程度，即进⼀步减少参数量和计算复<br>
杂度。这个过程可以通过增加低秩适应的迭代次数或增加低秩矩阵的秩来实现。<br>
4. 重构模型：使⽤⾃适应调整后的低秩矩阵重新构建语⾔模型。这样得到的模型既具有较低的参数量<br>
和计算需求，⼜能保持相对较⾼的性能。<br>
通过⾃适应的低秩适应⽅法，AdaLoRA能够根据模型的性能和计算需求进⾏灵活调整，从⽽进⼀步减少<br>
⼤型语⾔模型的计算和存储开销。它可以根据具体任务和资源限制，⾃动找到⼀个平衡点，使模型在性<br>
能和效率之间达到最佳的平衡。<br>
&lt;/aside&gt;<br>
</p>

<h2>第 70 页</h2>

<p>1. 保留原模型的知识：LoRA微调是在原模型的基础上进⾏的，因此可以保留原模型所学到的知识和表<br>
示能⼒。这意味着LoRA微调的模型可以继承原模型在⼤规模数据上训练得到的特征提取能⼒和语⾔<br>
模型知识，从⽽在微调任务上表现更好。<br>
2. 减少微调时间和资源开销：由于LoRA已经对原模型进⾏了低秩适应，减少了参数量和计算复杂度，<br>
因此LoRA微调所需的时间和资源开销相对较⼩。这对于⼤规模语⾔模型的微调任务来说是⾮常有益<br>
的，可以加快模型的训练和推理速度，降低资源消耗。<br>
3. 提⾼模型泛化能⼒：LoRA微调通过低秩适应，对原模型进⾏了⼀定程度的正则化。这种正则化可以<br>
帮助模型更好地泛化到新的任务和数据上，减少过拟合的⻛险。LoRA微调的模型通常具有更好的泛<br>
化能⼒，能够适应不同领域和任务的需求。<br>
4. 可扩展性和灵活性：LoRA微调⽅法的设计可以根据具体任务和资源限制进⾏调整和优化。可以通过<br>
调整低秩适应的程度、迭代次数和参数设置等来平衡性能和效率。这种灵活性使得LoRA微调适⽤于<br>
不同规模和需求的语⾔模型，具有较⾼的可扩展性。<br>
  综上所述，LoRA微调具有保留知识、减少资源开销、提⾼泛化能⼒和灵活性等优点，使得它成为⼤规<br>
模语⾔模型微调的⼀种有效⽅法。<br>
  <br>
七、LoRA微调⽅法为啥能加速训练？<br>
  LoRA微调⽅法能够加速训练的原因主要有以下⼏点：<br>
1. 低秩适应减少了参数量：LoRA微调使⽤低秩适应⽅法对原模型的参数进⾏分解，将原始的参数矩阵<br>
分解为较⼩的矩阵的乘积形式。这样可以⼤幅度减少参数量，从⽽减少了模型的存储需求和计算复<br>
杂度。减少的参数量意味着更少的内存占⽤和更快的计算速度，从⽽加速了训练过程。<br>
2. 降低了计算复杂度：由于LoRA微调减少了参数量，每个参数的计算量也相应减少。在训练过程中，<br>
计算参数更新和梯度传播的时间会显著减少，从⽽加速了训练过程。特别是在⼤规模语⾔模型中，<br>
参数量巨⼤，计算复杂度很⾼，LoRA微调可以显著减少计算开销，提⾼训练效率。<br>
3. 加速收敛速度：LoRA微调通过低秩适应对原模型进⾏了正则化，使得模型更容易收敛到较好的解。<br>
低秩适应过程中的正则化可以帮助模型更好地利⽤数据进⾏训练，减少过拟合的⻛险。这样可以加<br>
快模型的收敛速度，从⽽加速训练过程。<br>
4. 提⾼了计算效率：LoRA微调⽅法通过低秩适应减少了模型的参数量和计算复杂度，从⽽提⾼了计算<br>
效率。这意味着在相同的计算资源下，LoRA微调可以处理更⼤规模的数据和更复杂的任务。同时，<br>
也可以利⽤较少的计算资源来训练模型，从⽽减少了时间和成本。<br>
  综上所述，LoRA微调⽅法通过减少参数量、降低计算复杂度、加速收敛速度和提⾼计算效率等⽅式，<br>
能够显著加速训练过程，特别适⽤于⼤规模语⾔模型的微调任务。<br>
  <br>
⼋、如何在已有LoRA模型上继续训练？<br>
</p>

<h2>第 71 页</h2>

<p>  在已有LoRA模型上继续训练可以按照以下步骤进⾏：<br>
1. 加载已有的LoRA模型：⾸先，需要加载已经训练好的LoRA模型，包括原始模型的参数和低秩适应<br>
所得到的参数。可以使⽤相应的深度学习框架提供的函数或⽅法来加载模型。<br>
2. 准备微调数据集：根据需要进⾏微调的任务，准备相应的微调数据集。这些数据集可以是新的标注<br>
数据，也可以是从原始训练数据中选择的⼦集。确保微调数据集与原始训练数据集具有⼀定的相似<br>
性，以便模型能够更好地泛化到新的任务上。<br>
3. 设置微调参数：根据任务需求，设置微调的超参数，包括学习率、批⼤⼩、训练轮数等。这些参数<br>
可以根据经验或者通过实验进⾏调整。注意，由于LoRA已经对原模型进⾏了低秩适应，可能需要调<br>
整学习率等参数来适应新的微调任务。<br>
4. 定义微调⽬标函数：根据任务类型，定义微调的⽬标函数。这可以是分类任务的交叉熵损失函数，<br>
回归任务的均⽅误差损失函数等。根据具体任务需求，可以选择合适的损失函数。<br>
5. 进⾏微调训练：使⽤微调数据集和定义的⽬标函数，对已有的LoRA模型进⾏微调训练。根据设定的<br>
超参数进⾏迭代训练，通过反向传播和优化算法更新模型参数。可以使⽤批量梯度下降、随机梯度<br>
下降等优化算法来进⾏模型参数的更新。<br>
6. 评估和调整：在微调训练过程中，定期评估模型在验证集上的性能。根据评估结果，可以调整超参<br>
数、微调数据集等，以进⼀步优化模型的性能。<br>
7. 保存微调模型：在微调训练完成后，保存微调得到的模型参数。这样就可以在后续的推理任务中使<br>
⽤微调后的模型。<br>
  需要注意的是，在进⾏微调训练时，需要根据具体任务和数据集的特点进⾏调整和优化。可能需要尝<br>
试不同的超参数设置、微调数据集的选择等，以获得更好的微调效果。<br>
  <br>
 <br>
⼤模型（LLMs）推理⾯<br>
 <br>
⼤模型（LLMs）推理⾯<br>
 <br>
1. 为什么⼤模型推理时显存涨的那么多还⼀直占着？<br>
2. ⼤模型在gpu和cpu上推理速度如何？<br>
3. 推理速度上，int8和fp16⽐起来怎么样？<br>
4. ⼤模型有推理能⼒吗？<br>
5. ⼤模型⽣成时的参数怎么设置？<br>
6. 有哪些省内存的⼤语⾔模型训练/微调/推理⽅法？<br>
6.1 如何 估算模型所需的RAM？<br>
6.2 Fp16-mixed precision<br>
6.3 Int8-bitsandbytes<br>
6.4 LoRA<br>
6.5 Gradient Checkpointing<br>
6.6 Torch FSDP+CPU oﬄoad<br>
</p>

<h2>第 72 页</h2>

<p>7. 如何让⼤模型输出合规化<br>
8. 应⽤模式变更<br>
⼤模型（LLMs）推理⾯<br>
 <br>
1. <br>
 为什么⼤模型推理时显存涨的那么多还⼀直占着？<br>
   ⼤语⾔模型进⾏推理时，显存涨得很多且⼀直占着显存不释放的原因主要有以下⼏点：<br>
1. 模型参数占⽤显存：⼤语⾔模型通常具有巨⼤的参数量，这些参数需要存储在显存中以供推理使<br>
⽤。因此，在推理过程中，模型参数会占⽤相当⼤的显存空间。<br>
2. 输⼊数据占⽤显存：进⾏推理时，需要将输⼊数据加载到显存中。对于⼤语⾔模型⽽⾔，输⼊数据<br>
通常也会占⽤较⼤的显存空间，尤其是对于较⻓的⽂本输⼊。<br>
3. 中间计算结果占⽤显存：在推理过程中，模型会进⾏⼀系列的计算操作，⽣成中间结果。这些中间<br>
结果也需要存储在显存中，以便后续计算使⽤。对于⼤语⾔模型⽽⾔，中间计算结果可能会占⽤较<br>
多的显存空间。<br>
4. 内存管理策略：某些深度学习框架在推理时采⽤了⼀种延迟释放显存的策略，即显存不会⽴即释<br>
放，⽽是保留⼀段时间以备后续使⽤。这种策略可以减少显存的分配和释放频率，提⾼推理效率，<br>
但也会导致显存⼀直占⽤的现象。<br>
   需要注意的是，显存的占⽤情况可能会受到硬件设备、深度学习框架和模型实现的影响。不同的环境<br>
和设置可能会导致显存占⽤的差异。如果显存占⽤过多导致资源不⾜或性能下降，可以考虑调整模型的<br>
批量⼤⼩、优化显存分配策略或使⽤更⾼性能的硬件设备来解决问题。<br>
   <br>
2. <br>
 ⼤模型在gpu和cpu上推理速度如何？<br>
   ⼤语⾔模型在GPU和CPU上进⾏推理的速度存在显著差异。⼀般情况下，GPU在进⾏深度学习推理任<br>
务时具有更⾼的计算性能，因此⼤语⾔模型在GPU上的推理速度通常会⽐在CPU上更快。<br>
   以下是GPU和CPU在⼤语⾔模型推理速度⽅⾯的⼀些特点：<br>
1. GPU推理速度快：GPU具有⼤量的并⾏计算单元，可以同时处理多个计算任务。对于⼤语⾔模型⽽<br>
⾔，GPU可以更⾼效地执⾏矩阵运算和神经⽹络计算，从⽽加速推理过程。<br>
2. CPU推理速度相对较慢：相较于GPU，CPU的计算能⼒较弱，主要⽤于通⽤计算任务。虽然CPU也<br>
可以执⾏⼤语⾔模型的推理任务，但由于计算能⼒有限，推理速度通常会较慢。<br>
3. 使⽤GPU加速推理：为了充分利⽤GPU的计算能⼒，通常会使⽤深度学习框架提供的GPU加速功<br>
能，如CUDA或OpenCL。这些加速库可以将计算任务分配给GPU并利⽤其并⾏计算能⼒，从⽽加<br>
快⼤语⾔模型的推理速度。<br>
</p>

<h2>第 73 页</h2>

<p>   需要注意的是，推理速度还受到模型⼤⼩、输⼊数据⼤⼩、计算操作的复杂度以及硬件设备的性能等<br>
因素的影响。因此，具体的推理速度会因具体情况⽽异。⼀般来说，使⽤GPU进⾏⼤语⾔模型的推理可<br>
以获得更快的速度。<br>
   <br>
3. <br>
 推理速度上，int8和fp16⽐起来怎么样？<br>
   在⼤语⾔模型的推理速度上，使⽤INT8（8位整数量化）和FP16（半精度浮点数）相对于FP32（单精<br>
度浮点数）可以带来⼀定的加速效果。这是因为INT8和FP16的数据类型在表示数据时所需的内存和计算<br>
资源较少，从⽽可以加快推理速度。<br>
   具体来说，INT8在相同的内存空间下可以存储更多的数据，从⽽可以在相同的计算资源下进⾏更多的<br>
并⾏计算。这可以提⾼每秒推理操作数（Operations Per Second，OPS）的数量，加速推理速度。<br>
   FP16在相对较⼩的数据范围内进⾏计算，因此在相同的计算资源下可以执⾏更多的计算操作。虽然<br>
FP16的精度相对较低，但对于某些应⽤场景，如图像处理和语⾳识别等，FP16的精度已经⾜够满⾜需<br>
求。<br>
   需要注意的是，INT8和FP16的加速效果可能会受到硬件设备的⽀持程度和具体实现的影响。某些硬件<br>
设备可能对INT8和FP16有更好的优化⽀持，从⽽进⼀步提⾼推理速度。<br>
   综上所述，使⽤INT8和FP16数据类型可以在⼤语⾔模型的推理过程中提⾼推理速度，但需要根据具体<br>
场景和硬件设备的⽀持情况进⾏评估和选择。<br>
   <br>
4. <br>
 ⼤模型有推理能⼒吗？<br>
   是的，⼤语⾔模型具备推理能⼒。推理是指在训练阶段之后，使⽤已经训练好的模型对新的输⼊数据<br>
进⾏预测、⽣成或分类等任务。⼤语⾔模型可以通过输⼊⼀段⽂本或问题，然后⽣成相应的回答或补全<br>
⽂本。<br>
   ⼤语⾔模型通常基于循环神经⽹络（RNN）或变种（如⻓短时记忆⽹络LSTM或⻔控循环单元GRU）<br>
等结构构建，通过学习⼤量的⽂本数据，模型可以捕捉到语⾔的规律和模式。这使得⼤语⾔模型能够对<br>
输⼊的⽂本进⾏理解和推理，⽣成合理的回答或补全。<br>
   例如，GPT（Generative Pre-trained Transformer）模型是⼀种⼤型的预训练语⾔模型，它通过预训<br>
练的⽅式学习⼤规模的⽂本数据，然后可以在推理阶段⽣成连贯、合理的⽂本。这种模型可以⽤于⾃然<br>
语⾔处理任务，如⽂本⽣成、机器翻译、对话系统等。<br>
   需要注意的是，⼤语⾔模型的推理能⼒是基于其训练数据的统计规律和模式，因此在⾯对新颖、复杂<br>
或特殊的输⼊时，可能会出现推理错误或⽣成不准确的结果。此外，⼤语⾔模型的推理能⼒也受到模型<br>
的⼤⼩、训练数据的质量和数量、推理算法等因素的影响。<br>
   <br>
</p>

<h2>第 74 页</h2>

<p>5. <br>
 ⼤模型⽣成时的参数怎么设置？<br>
   在⼤语⾔模型进⾏推理时，参数设置通常包括以下⼏个⽅⾯：<br>
1. 模型选择：选择适合推理任务的模型，如循环神经⽹络（RNN）、⻓短时记忆⽹络（LSTM）、⻔<br>
控循环单元（GRU）或变种的Transformer等。不同的模型在推理任务上可能有不同的效果。<br>
2. 模型加载：加载预训练好的模型参数，这些参数可以是在⼤规模⽂本数据上进⾏预训练得到的。预<br>
训练模型的选择应根据任务和数据集的特点来确定。<br>
3. 推理算法：选择合适的推理算法，如贪婪搜索、束搜索（beam search）或采样⽅法等。贪婪搜索<br>
只考虑当前最有可能的输出，束搜索会考虑多个候选输出，采样⽅法会根据概率分布进⾏随机采<br>
样。<br>
4. 温度参数：在⽣成⽂本时，可以通过调整温度参数来控制⽣成的⽂本的多样性。较⾼的温度会增加<br>
⽣成⽂本的随机性和多样性，⽽较低的温度会使⽣成⽂本更加确定和⼀致。<br>
5. 推理⻓度：确定⽣成⽂本的⻓度限制，可以设置⽣成的最⼤⻓度或⽣成的最⼩⻓度等。<br>
6. 其他参数：根据具体任务和需求，可能还需要设置其他参数，如⽣成的起始⽂本、⽣成的批次⼤⼩<br>
等。<br>
   以上参数设置需要根据具体任务和数据集的特点进⾏调整和优化。通常情况下，可以通过实验和调参<br>
来找到最佳的参数组合，以获得较好的推理效果。同时，还可以通过⼈⼯评估和⾃动评估指标来评估⽣<br>
成⽂本的质量和准确性，进⼀步优化参数设置。<br>
   <br>
6. <br>
 有哪些省内存的⼤语⾔模型训练/微调/推理⽅法？<br>
   有⼀些⽅法可以帮助省内存的⼤语⾔模型训练、微调和推理，以下是⼀些常⻅的⽅法：<br>
1. 参数共享（Parameter Sharing）：通过共享模型中的参数，可以减少内存占⽤。例如，可以在不同<br>
的位置共享相同的嵌⼊层或注意⼒机制。<br>
2. 梯度累积（Gradient Accumulation）：在训练过程中，将多个⼩批次的梯度累积起来，然后进⾏⼀<br>
次参数更新。这样可以减少每个⼩批次的内存需求，特别适⽤于GPU内存较⼩的情况。<br>
3. 梯度裁剪（Gradient Clipping）：通过限制梯度的⼤⼩，可以避免梯度爆炸的问题，从⽽减少内存<br>
使⽤。<br>
4. 分布式训练（Distributed Training）：将训练过程分布到多台机器或多个设备上，可以减少单个设<br>
备的内存占⽤。分布式训练还可以加速训练过程。<br>
5. 量化（Quantization）：将模型参数从⾼精度表示（如FP32）转换为低精度表示（如INT8或<br>
FP16），可以减少内存占⽤。量化⽅法可以通过减少参数位数或使⽤整数表示来实现。<br>
6. 剪枝（Pruning）：通过去除冗余或不重要的模型参数，可以减少模型的内存占⽤。剪枝⽅法可以根<br>
据参数的重要性进⾏选择，从⽽保持模型性能的同时减少内存需求。<br>
7. 蒸馏（Knowledge Distillation）：使⽤较⼩的模型（教师模型）来指导训练较⼤的模型（学⽣模<br>
型），可以从教师模型中提取知识，减少内存占⽤。<br>
8. 分块处理（Chunking）：将输⼊数据或模型分成较⼩的块进⾏处理，可以减少内存需求。例如，在<br>
推理过程中，可以将较⻓的输⼊序列分成多个较短的⼦序列进⾏处理。<br>
</p>

<h2>第 75 页</h2>

<p>   这些⽅法可以结合使⽤，根据具体场景和需求进⾏选择和调整。同时，不同的⽅法可能对不同的模型<br>
和任务有不同的效果，因此需要进⾏实验和评估。<br>
   <br>
7. <br>
 如何让⼤模型输出合规化<br>
   要让⼤模型输出合规化，可以采取以下⽅法：<br>
1. 数据清理和预处理：在进⾏模型训练之前，对输⼊数据进⾏清理和预处理，以确保数据符合合规要<br>
求。这可能包括去除敏感信息、匿名化处理、数据脱敏等操作。<br>
2. 引⼊合规性约束：在模型训练过程中，可以引⼊合规性约束，以确保模型输出符合法律和道德要<br>
求。例如，可以在训练过程中使⽤合规性指标或损失函数来约束模型的输出。<br>
3. 限制模型访问权限：对于⼀些特定的应⽤场景，可以通过限制模型的访问权限来确保输出的合规<br>
性。只允许授权⽤户或特定⻆⾊访问模型，以保护敏感信息和确保合规性。<br>
4. 解释模型决策过程：为了满⾜合规性要求，可以对模型的决策过程进⾏解释和解释。通过提供透明<br>
的解释，可以使⽤户或相关⽅了解模型是如何做出决策的，并评估决策的合规性。<br>
5. 审查和验证模型：在模型训练和部署之前，进⾏审查和验证以确保模型的输出符合合规要求。这可<br>
能涉及到法律专业⼈⼠、伦理专家或相关领域的专业⼈⼠的参与。<br>
6. 监控和更新模型：持续监控模型的输出，并根据合规要求进⾏必要的更新和调整。及时发现和解决<br>
合规性问题，确保模型的输出⼀直保持合规。<br>
7. 合规培训和教育：为使⽤模型的⼈员提供合规培训和教育，使其了解合规要求，并正确使⽤模型以<br>
确保合规性。<br>
   需要注意的是，合规性要求因特定领域、应⽤和地区⽽异，因此在实施上述⽅法时，需要根据具体情<br>
况进⾏调整和定制。同时，合规性是⼀个动态的过程，需要与法律、伦理和社会要求的变化保持同步。<br>
   <br>
8. <br>
 应⽤模式变更<br>
   ⼤语⾔模型的应⽤模式变更可以包括以下⼏个⽅⾯：<br>
1. 任务定制化：将⼤语⾔模型应⽤于特定的任务或领域，通过对模型进⾏微调或迁移学习，使其适应<br>
特定的应⽤场景。例如，将⼤语⾔模型⽤于⾃动⽂本摘要、机器翻译、对话系统等任务。<br>
2. 个性化交互：将⼤语⾔模型应⽤于个性化交互，通过对⽤户输⼊进⾏理解和⽣成相应的回复，实现<br>
更⾃然、智能的对话体验。这可以应⽤于智能助⼿、在线客服、社交媒体等场景。<br>
3. 内容⽣成与创作：利⽤⼤语⾔模型的⽣成能⼒，将其应⽤于内容⽣成和创作领域。例如，⾃动⽣成<br>
新闻报道、创意⽂案、诗歌等内容，提供创作灵感和辅助创作过程。<br>
4. 情感分析与情绪识别：通过⼤语⾔模型对⽂本进⾏情感分析和情绪识别，帮助企业或个⼈了解⽤户<br>
的情感需求和反馈，以改善产品、服务和⽤户体验。<br>
5. 知识图谱构建：利⽤⼤语⾔模型的⽂本理解能⼒，将其应⽤于知识图谱的构建和更新。通过对海量<br>
⽂本进⾏分析和提取，⽣成结构化的知识表示，为知识图谱的建设提供⽀持。<br>
6. 法律和合规应⽤：⼤语⾔模型可以⽤于法律和合规领域，例如⾃动⽣成法律⽂件、合同条款、隐私<br>
政策等内容，辅助法律专业⼈⼠的⼯作。<br>
</p>

<h2>第 76 页</h2>

<p>7. 教育和培训应⽤：将⼤语⾔模型应⽤于教育和培训领域，例如智能辅导系统、在线学习平台等，为<br>
学⽣提供个性化的学习辅助和教学资源。<br>
8. 创新应⽤场景：探索和创造全新的应⽤场景，结合⼤语⾔模型的能⼒和创新思维，开拓新的商业模<br>
式和服务⽅式。例如，结合增强现实技术，实现智能导览和语⾳交互；结合虚拟现实技术，创建沉<br>
浸式的交互体验等。<br>
   应⽤模式变更需要充分考虑数据安全、⽤户隐私、道德和法律等因素，确保在合规和可持续发展的前<br>
提下进⾏应⽤创新。同时，与领域专家和⽤户进⾏密切合作，不断优化和改进应⽤模式，以满⾜⽤户需<br>
求和市场竞争。<br>
   <br>
⼤模型（LLMs）预训练⾯<br>
 <br>
⼤模型（LLMs）增量预训练篇<br>
 <br>
1. 为什么要增量预训练？<br>
2. 进⾏ 增量预训练 需要做哪些准备⼯作？<br>
3. 增量预训练 所⽤ 训练框架？<br>
4. 增量预训练 训练流程 是怎么样？<br>
 <br>
⼤模型（LLMs）评测⾯<br>
 <br>
1. ⼤模型怎么评测？<br>
2. ⼤模型的honest原则是如何实现的？模型如何判断回答的知识是训练过的已知的知识，怎么训练这<br>
种能⼒？<br>
3. 如何衡量⼤模型⽔平？<br>
4. ⼤模型评估⽅法 有哪些？<br>
5. ⼤模型评估⼯具 有哪些？<br>
⼤模型（LLMs）评测⾯<br>
 <br>
1. <br>
 ⼤模型怎么评测？<br>
   ⼤语⾔模型的评测通常涉及以下⼏个⽅⾯：<br>
1. 语法和流畅度：评估模型⽣成的⽂本是否符合语法规则，并且是否流畅⾃然。这可以通过⼈⼯评估<br>
或⾃动评估指标如困惑度（perplexity）来衡量。<br>
2. 语义准确性：评估模型⽣成的⽂本是否准确传达了所需的含义，并且是否避免了歧义或模棱两可的<br>
表达。这需要通过⼈⼯评估来判断，通常需要领域专家的参与。<br>
</p>

<h2>第 77 页</h2>

<p>3. 上下⽂⼀致性：评估模型在⽣成⻓篇⽂本时是否能够保持⼀致的上下⽂逻辑和连贯性。这需要通过<br>
⼈⼯评估来检查模型⽣成的⽂本是否与前⽂和后⽂相衔接。<br>
4. 信息准确性：评估模型⽣成的⽂本中所包含的信息是否准确和可靠。这可以通过⼈⼯评估或与已知<br>
信息进⾏对⽐来判断。<br>
5. 创造性和多样性：评估模型⽣成的⽂本是否具有创造性和多样性，是否能够提供不同的观点和表达<br>
⽅式。这需要通过⼈⼯评估来判断。<br>
   评测⼤语⾔模型是⼀个复杂的过程，需要结合⼈⼯评估和⾃动评估指标来进⾏综合评价。由于⼤语⾔<br>
模型的规模和复杂性，评测结果往往需要多个评估者的共识，并且需要考虑到评估者的主观因素和评估<br>
标准的⼀致性。<br>
   <br>
2. <br>
 ⼤模型的honest原则是如何实现的？<br>
   ⼤语⾔模型的"honest"原则是指模型在⽣成⽂本时应该保持诚实和真实，不应该编造虚假信息或误导<br>
⽤户。实现"honest"原则可以通过以下⼏种⽅式：<br>
1. 数据训练：使⽤真实和可靠的数据进⾏模型的训练，确保模型学习到的知识和信息与真实世界相<br>
符。数据的来源和质量对于模型的"honest"性⾮常重要。<br>
2. 过滤和审查：在训练数据中，可以通过过滤和审查来排除不真实或不可靠的内容。这可以通过⼈⼯<br>
审核或⾃动筛选算法来实现，以确保训练数据的可信度。<br>
3. 监督和调整：对模型的⽣成结果进⾏监督和调整，及时发现和纠正可能的误导或虚假信息。这可以<br>
通过⼈⼯审核、⽤户反馈或者⾃动监测来实现。<br>
4. 透明度和解释性：提供模型⽣成⽂本的解释和可追溯性，使⽤户能够了解模型⽣成⽂本的依据和过<br>
程。这可以通过展示模型的输⼊数据、模型的结构和参数等⽅式来实现。<br>
5. 遵循道德和法律准则：确保模型的设计和使⽤符合道德和法律的准则，不违背伦理和法律规定。这<br>
需要在模型的开发和应⽤过程中考虑到社会和伦理的因素。<br>
   需要注意的是，尽管⼤语⾔模型可以尽⼒遵循"honest"原则，但由于其是基于训练数据进⾏⽣成，仍<br>
然存在可能⽣成不准确或误导性的⽂本。因此，⽤户在使⽤⼤语⾔模型⽣成的⽂本时，仍需保持批判性<br>
思维，并结合其他信息和验证渠道进⾏判断。<br>
   <br>
3. <br>
 模型如何判断回答的知识是训练过的已知的知识，怎么训练这种能⼒？<br>
   ⼤语⾔模型判断回答的知识是否为训练过的已知知识，通常可以通过以下⼏种⽅式来实现：<br>
1. 训练数据：在训练⼤语⾔模型时，可以使⽤包含已知知识的真实数据。这些数据可以来⾃于可靠的<br>
来源，如百科全书、学术⽂献等。通过训练模型时接触到这些知识，模型可以学习到⼀定的知识表<br>
示和模式。<br>
2. 监督学习：可以使⽤⼈⼯标注的数据来进⾏监督学习，将已知知识标注为正确答案。在训练模型<br>
时，通过最⼤化与标注答案的匹配程度，模型可以学习到回答问题的知识表示和模式。<br>
3. 开放域知识库：可以利⽤开放域知识库，如维基百科，作为额外的训练数据。通过将知识库中的信<br>
</p>

<h2>第 78 页</h2>

<p>息与模型进⾏交互，模型可以学习到知识的表示和检索能⼒。<br>
4. 过滤和筛选：在训练数据中，可以通过过滤和筛选来排除不准确或不可靠的信息。这可以通过⼈⼯<br>
审核或⾃动筛选算法来实现，以提⾼模型对已知知识的准确性。<br>
   训练这种能⼒需要充分的训练数据和有效的训练⽅法。同时，还需要进⾏模型的评估和调优，以确保<br>
模型能够正确理解和回答已知的知识问题。此外，定期更新训练数据和模型，以跟进新的知识和信息，<br>
也是保持模型知识更新和准确性的重要步骤。<br>
   <br>
⼤模型（LLMs）强化学习⾯<br>
 <br>
1. 简单介绍强化学习？<br>
2. 简单介绍⼀下 RLHF？<br>
3. 奖励模型需要和基础模型⼀致吗？<br>
4. RLHF 在实践过程中存在哪些不⾜？<br>
5. 如何解决 ⼈⼯产⽣的偏好数据集成本较⾼，很难量产问题？<br>
6. 如何解决三个阶段的训练（SFT-&gt;RM-&gt;PPO）过程较⻓，更新迭代较慢问题？<br>
7. 如何解决 PPO 的训练过程同时存在4个模型（2训练，2推理），对计算资源的要求较⾼ 问题？<br>
⼤模型（LLMs）强化学习⾯<br>
 <br>
1. <br>
 奖励模型需要和基础模型⼀致吗？<br>
2. <br>
 RLHF 在实践过程中存在哪些不⾜？<br>
 奖励模型和基础模型在训练过程中可以是⼀致的，也可以是不同的。这取决于你的任务需求和优化⽬<br>
标。<br>
 如果你希望优化⼀个包含多个⼦任务的复杂任务，那么你可能需要为每个⼦任务定义⼀个奖励模型，<br>
然后将这些奖励模型整合到⼀个统⼀的奖励函数中。这样，你可以根据任务的具体情况调整每个⼦任务<br>
的权重，以实现更好的性能。<br>
 另⼀⽅⾯，如果你的任务是单任务的，那么你可能只需要⼀个基础模型和⼀个对应的奖励模型，这两<br>
个模型可以共享相同的参数。在这种情况下，你可以通过调整奖励模型的权重来控制任务的优化⽅向。<br>
 总之，奖励模型和基础模型的⼀致性取决于你的任务需求和优化⽬标。在实践中，你可能需要尝试不<br>
同的模型结构和奖励函数，以找到最适合你任务的解决⽅案。<br>
 &lt;/aside&gt;<br>
</p>

<h2>第 79 页</h2>

<p>3. <br>
 如何解决 ⼈⼯产⽣的偏好数据集成本较⾼，很难量产问题？<br>
 RLHF（Reinforcement Learning from Human Feedback）是⼀种通过⼈类反馈进⾏增强学习<br>
的⽅法，尽管具有⼀定的优势，但在实践过程中仍然存在以下⼏个不⾜之处：<br>
 1. ⼈类反馈的代价⾼昂：获取⾼质量的⼈类反馈通常需要⼤量的⼈⼒和时间成本。⼈类专家需要花<br>
费时间来评估模型的⾏为并提供准确的反馈，这可能限制了RLHF⽅法的可扩展性和应⽤范围。<br>
 2. ⼈类反馈的主观性：⼈类反馈往往是主观的，不同的专家可能会有不同的意⻅和判断。这可能导<br>
致模型在不同专家之间的反馈上存在差异，从⽽影响模型的训练和性能。<br>
 3. 反馈延迟和稀疏性：获取⼈类反馈可能存在延迟和稀疏性的问题。⼈类专家不可能实时监控和评<br>
估模型的每⼀个动作，因此模型可能需要等待⼀段时间才能收到反馈，这可能会导致训练的效率和效果<br>
下降。<br>
 4. 错误反馈的影响：⼈类反馈可能存在错误或误导性的情况，这可能会对模型的训练产⽣负⾯影<br>
响。如果模型在错误的反馈指导下进⾏训练，可能会导致模型产⽣错误的⾏为策略。<br>
 5. 缺乏探索与利⽤的平衡：在RLHF中，⼈类反馈通常⽤于指导模型的⾏为，但可能会导致模型过于<br>
依赖⼈类反馈⽽缺乏探索的能⼒。这可能限制了模型发现新策略和优化性能的能⼒。<br>
 针对这些不⾜，研究⼈员正在探索改进RLHF⽅法，如设计更⾼效的⼈类反馈收集机制、开发更准确的<br>
反馈评估⽅法、结合⾃适应探索策略等，以提⾼RLHF⽅法的实⽤性和性能。<br>
 &lt;/aside&gt;<br>
 解决⼈⼯产⽣偏好数据集成本⾼、难以量产的问题，可以考虑以下⼏种⽅法：<br>
 1. 引⼊模拟数据：使⽤模拟数据来代替或辅助⼈⼯产⽣的数据。模拟数据可以通过模拟环境或模型<br>
⽣成，以模拟⼈类⽤户的⾏为和反馈。这样可以降低数据收集的成本和难度，并且可以⼤规模⽣成数<br>
据。<br>
 2. 主动学习：采⽤主动学习的⽅法来优化数据收集过程。主动学习是⼀种主动选择样本的⽅法，通<br>
过选择那些对模型训练最有帮助的样本进⾏标注，从⽽减少标注的⼯作量。可以使⽤⼀些算法，如不确<br>
定性采样、多样性采样等，来选择最有价值的样本进⾏⼈⼯标注。<br>
 3. 在线学习：采⽤在线学习的⽅法进⾏模型训练。在线学习是⼀种增量学习的⽅法，可以在模型运<br>
⾏的同时进⾏训练和优化。这样可以利⽤实际⽤户的交互数据来不断改进模型，减少对⼈⼯标注数据的<br>
依赖。<br>
 4. 众包和协作：利⽤众包平台或协作机制来收集⼈⼯产⽣的偏好数据。通过将任务分发给多个⼈参<br>
与，可以降低每个⼈的负担，并且可以通过众包平台的规模效应来提⾼数据收集的效率。<br>
 5. 数据增强和迁移学习：通过数据增强技术，如数据合成、数据扩增等，来扩充有限的⼈⼯产⽣数<br>
据集。此外，可以利⽤迁移学习的⽅法，将从其他相关任务或领域收集的数据应⽤于当前任务，以减少<br>
对⼈⼯产⽣数据的需求。<br>
 综合运⽤上述⽅法，可以有效降低⼈⼯产⽣偏好数据的成本，提⾼数据的量产能⼒，并且保证数据的<br>
质量和多样性。<br>
 &lt;/aside&gt;<br>
</p>

<h2>第 80 页</h2>

<p>4. <br>
 如何解决三个阶段的训练（SFT-&gt;RM-&gt;PPO）过程较⻓，更新迭代较慢问题？<br>
5. <br>
 如何解决 PPO 的训练过程同时存在4个模型（2训练，2推理），对计算资源的要求较⾼ 问题？<br>
 要解决三个阶段训练过程较⻓、更新迭代较慢的问题，可以考虑以下⼏种⽅法：<br>
 1. 并⾏化训练：利⽤多个计算资源进⾏并⾏化训练，可以加速整个训练过程。可以通过使⽤多个CPU<br>
核⼼或GPU来并⾏处理不同的训练任务，从⽽提⾼训练的效率和速度。<br>
 2. 分布式训练：将训练任务分发到多台机器或多个节点上进⾏分布式训练。通过将模型和数据分布<br>
在多个节点上，并进⾏并⾏计算和通信，可以加快训练的速度和更新的迭代。<br>
 3. 优化算法改进：针对每个阶段的训练过程，可以考虑改进优化算法来加速更新迭代。例如，在SFT<br>
（Supervised Fine-Tuning）阶段，可以使⽤更⾼效的优化算法，如⾃适应学习率⽅法<br>
（Adaptive Learning Rate）或者剪枝技术来减少模型参数；在RM（Reward Modeling）阶<br>
段，可以使⽤更快速的模型训练算法，如快速梯度法（Fast Gradient Method）等；在PPO<br>
（Proximal Policy Optimization）阶段，可以考虑使⽤更⾼效的采样和优化⽅法，如并⾏采<br>
样、多步采样等。<br>
 4. 迁移学习和预训练：利⽤迁移学习和预训练技术，可以利⽤已有的模型或数据进⾏初始化或预训<br>
练，从⽽加速训练过程。通过将已有模型的参数或特征迁移到⽬标模型中，可以减少⽬标模型的训练时<br>
间和样本需求。<br>
 5. 参数调优和超参数搜索：对于每个阶段的训练过程，可以进⾏参数调优和超参数搜索，以找到更<br>
好的参数设置和配置。通过系统地尝试不同的参数组合和算法设定，可以找到更快速和⾼效的训练⽅<br>
式。<br>
 综合运⽤上述⽅法，可以加速三个阶段训练过程，提⾼更新迭代的速度和效率，从⽽减少训练时间和<br>
资源消耗。<br>
 &lt;/aside&gt;<br>
</p>

<h2>第 81 页</h2>

<p>⼤模型（LLMs）软硬件配置⾯<br>
 <br>
1. 建议的软件环境是什么？<br>
⼤模型（LLMs）软硬件配置⾯<br>
 <br>
1. <br>
 建议的软件环境是什么？<br>
   在⼤语⾔模型的开发中，建议使⽤以下软件环境：<br>
1. Python：作为主要的开发语⾔，Python具有丰富的第三⽅库和⼯具，适⽤于⼤语⾔模型的开发和实<br>
验。<br>
2. 深度学习框架：选择适合⼤语⾔模型开发的深度学习框架，如TensorFlow、PyTorch等。这些框架<br>
提供了⾼效的计算图和⾃动求导功能，⽅便进⾏模型的搭建和训练。<br>
3. GPU⽀持：由于⼤语⾔模型通常需要处理⼤规模的数据和复杂的计算，使⽤GPU进⾏加速是必要<br>
的。确保计算机上安装了适当的GPU驱动程序，并在深度学习框架中启⽤GPU⽀持。<br>
4. 数据处理⼯具：对于⼤语⾔模型的开发，需要进⾏⼤规模数据的处理和预处理。常⽤的数据处理⼯<br>
具包括NumPy、Pandas等，⽤于数据的读取、处理和转换。<br>
5. ⽂本处理库：对于⾃然语⾔处理任务，需要使⽤⽂本处理库来进⾏⽂本的分词、词向量表示等操<br>
作。常⽤的⽂本处理库包括NLTK、spaCy等。<br>
6. 存储和缓存：对于⼤规模的数据集，需要合适的存储和缓存技术来提⾼数据的读取和访问效率。可<br>
 要解决PPO训练过程中对计算资源要求较⾼的问题，可以考虑以下⼏种⽅法：<br>
 1. 减少模型规模：通过减少模型的规模和参数量，可以降低对计算资源的需求。可以使⽤模型压缩<br>
技术、剪枝算法等⽅法来减少模型的参数数量，从⽽降低计算资源的使⽤量。<br>
 2. 降低训练频率：可以降低PPO训练的频率，减少每个训练周期的次数。例如，可以增加每个训练周<br>
期的时间间隔，或者减少每个周期中的训练步数。这样可以减少训练过程中对计算资源的占⽤。<br>
 3. 模型并⾏化：利⽤多个计算资源进⾏模型并⾏化训练，可以加速PPO的训练过程。可以将模型参数<br>
分布到多个GPU上，并进⾏并⾏计算和通信，以提⾼训练的效率和速度。<br>
 4. 异步训练：采⽤异步训练的⽅式，可以在多个计算资源上同时进⾏PPO的训练。可以使⽤异步优化<br>
算法，如A3C（Asynchronous Advantage Actor-Critic）等，将训练任务分发到多个线程或进<br>
程中进⾏并⾏训练，从⽽提⾼训练的效率。<br>
 5. 云计算和分布式训练：利⽤云计算平台或分布式系统进⾏PPO的训练，可以充分利⽤⼤规模计算资<br>
源。可以将训练任务分发到多个计算节点上进⾏分布式训练，以加速训练过程。<br>
 6. 参数共享和模型缓存：对于有多个模型的情况，可以考虑共享部分参数或缓存已计算的模型输<br>
出。通过共享参数和缓存计算结果，可以减少重复计算和存储，从⽽降低对计算资源的要求。<br>
 综合运⽤上述⽅法，可以有效降低PPO训练过程中对计算资源的要求，提⾼训练的效率和速度。<br>
 &lt;/aside&gt;<br>
</p>

<h2>第 82 页</h2>

<p>以使⽤数据库（如MySQL、MongoDB）或分布式存储系统（如Hadoop、HDFS）来管理和存储数<br>
据。<br>
7. 可视化⼯具：在模型开发和实验过程中，可视化⼯具可以帮助理解模型的结构和训练过程。常⽤的<br>
可视化⼯具包括TensorBoard、Matplotlib等。<br>
8. 开发环境：选择适合⾃⼰的开发环境，如Jupyter Notebook、PyCharm等。这些开发环境提供了⽅<br>
便的代码编辑、调试和实验管理功能。<br>
   此外，根据具体需求，还可以考虑使⽤分布式计算、云计算平台等来提供更强⼤的计算资源和存储能<br>
⼒，以⽀持⼤语⾔模型的开发和训练。<br>
   <br>
⼤模型（LLMs）训练集⾯<br>
 <br>
1. SFT（有监督微调）的数据集格式？<br>
2. RM（奖励模型）的数据格式？<br>
3. PPO（强化学习）的数据格式？<br>
4. 找数据集哪⾥找？<br>
5. 微调需要多少条数据？<br>
6. 有哪些⼤模型的训练集？<br>
7. 进⾏领域⼤模型预训练应⽤哪些数据集⽐较好？<br>
⼤模型（LLMs）训练集⾯<br>
 <br>
1. <br>
 SFT（有监督微调）的数据集格式？<br>
   对于⼤语⾔模型的训练中，SFT（Supervised Fine-Tuning）的数据集格式可以采⽤以下⽅式：<br>
1. 输⼊数据：输⼊数据是⼀个⽂本序列，通常是⼀个句⼦或者⼀个段落。每个样本可以是⼀个字符串<br>
或者是⼀个tokenized的⽂本序列。<br>
2. 标签数据：标签数据是与输⼊数据对应的标签或类别。标签可以是单个类别，也可以是多个类别的<br>
集合。对于多分类任务，通常使⽤one-hot编码或整数编码来表示标签。<br>
3. 数据集划分：数据集通常需要划分为训练集、验证集和测试集。训练集⽤于模型的训练，验证集⽤<br>
于调整模型的超参数和监控模型的性能，测试集⽤于评估模型的最终性能。<br>
4. 数据集格式：数据集可以以⽂本⽂件（如CSV、JSON等）或数据库的形式存储。每个样本包含输<br>
⼊数据和对应的标签。可以使⽤表格形式存储数据，每⼀列代表⼀个特征或标签。<br>
   下⾯是⼀个示例数据集的格式：<br>
</p>

<h2>第 83 页</h2>

<p>   在这个示例中，输⼊数据是⼀个句⼦，标签是⼀个⼆分类的标签（1代表正例，0代表负例）。每⼀⾏<br>
代表⼀个样本，第⼀列是输⼊数据，第⼆列是对应的标签。<br>
   需要注意的是，具体的数据集格式可能会因任务类型、数据来源和使⽤的深度学习框架⽽有所不同。<br>
因此，在进⾏SFT训练时，建议根据具体任务和框架的要求来定义和处理数据集格式。<br>
   <br>
2. <br>
 RM（奖励模型）的数据格式？<br>
   在⼤语⾔模型训练中，RM（Reward Model，奖励模型）的数据格式可以采⽤以下⽅式：<br>
1. 输⼊数据：输⼊数据是⼀个⽂本序列，通常是⼀个句⼦或者⼀个段落。每个样本可以是⼀个字符串<br>
或者是⼀个tokenized的⽂本序列。<br>
2. 奖励数据：奖励数据是与输⼊数据对应的奖励或评分。奖励可以是⼀个实数值，表示对输⼊数据的<br>
评价。也可以是⼀个离散的标签，表示对输⼊数据的分类。奖励数据可以是⼈⼯标注的，也可以是<br>
通过其他⽅式（如⼈⼯评估、强化学习等）得到的。<br>
3. 数据集格式：数据集可以以⽂本⽂件（如CSV、JSON等）或数据库的形式存储。每个样本包含输<br>
⼊数据和对应的奖励数据。可以使⽤表格形式存储数据，每⼀列代表⼀个特征或标签。<br>
   下⾯是⼀个示例数据集的格式：<br>
   在这个示例中，输⼊数据是⼀个句⼦，奖励数据是⼀个实数值，表示对输⼊数据的评价。每⼀⾏代表<br>
⼀个样本，第⼀列是输⼊数据，第⼆列是对应的奖励数据。<br>
   需要注意的是，具体的数据集格式可能会因任务类型、数据来源和使⽤的深度学习框架⽽有所不同。<br>
因此，在使⽤RM进⾏⼤语⾔模型训练时，建议根据具体任务和框架的要求来定义和处理数据集格式。<br>
   <br>
3. <br>
 PPO（强化学习）的数据格式？<br>
   Input,Label<br>
   "This is a sentence.",1<br>
   "Another sentence.",0<br>
   ...<br>
   Input,Reward<br>
   "This is a sentence.",0.8<br>
   "Another sentence.",0.2<br>
   ...<br>
</p>

<h2>第 84 页</h2>

<p>   在⼤语⾔模型训练中，PPO（Proximal Policy Optimization，近端策略优化）是⼀种常⽤的强化学习<br>
算法。PPO的数据格式可以采⽤以下⽅式：<br>
1. 输⼊数据：输⼊数据是⼀个⽂本序列，通常是⼀个句⼦或者⼀个段落。每个样本可以是⼀个字符串<br>
或者是⼀个tokenized的⽂本序列。<br>
2. 奖励数据：奖励数据是与输⼊数据对应的奖励或评分。奖励可以是⼀个实数值，表示对输⼊数据的<br>
评价。也可以是⼀个离散的标签，表示对输⼊数据的分类。奖励数据可以是⼈⼯标注的，也可以是<br>
通过其他⽅式（如⼈⼯评估、模型评估等）得到的。<br>
3. 动作数据：动作数据是模型在给定输⼊数据下的输出动作。对于语⾔模型，动作通常是⽣成的⽂本<br>
序列。动作数据可以是⼀个字符串或者是⼀个tokenized的⽂本序列。<br>
4. 状态数据：状态数据是模型在给定输⼊数据和动作数据下的状态信息。对于语⾔模型，状态数据可<br>
以是模型的隐藏状态或其他中间表示。状态数据的具体形式可以根据具体任务和模型结构进⾏定<br>
义。<br>
5. 数据集格式：数据集可以以⽂本⽂件（如CSV、JSON等）或数据库的形式存储。每个样本包含输<br>
⼊数据、奖励数据、动作数据和状态数据。可以使⽤表格形式存储数据，每⼀列代表⼀个特征或标<br>
签。<br>
   下⾯是⼀个示例数据集的格式：<br>
   在这个示例中，输⼊数据是⼀个句⼦，奖励数据是⼀个实数值，动作数据是⽣成的句⼦，状态数据是<br>
模型的隐藏状态。每⼀⾏代表⼀个样本，第⼀列是输⼊数据，第⼆列是对应的奖励数据，第三列是⽣成<br>
的动作数据，第四列是状态数据。<br>
   需要注意的是，具体的数据集格式可能会因任务类型、数据来源和使⽤的深度学习框架⽽有所不同。<br>
因此，在使⽤PPO进⾏⼤语⾔模型训练时，建议根据具体任务和框架的要求来定义和处理数据集格式。<br>
   <br>
4. <br>
 找数据集哪⾥找？<br>
   在训练⾃⼰的⼤语⾔模型时，可以从以下⼏个途径找到合适的数据集：<br>
1. 公开数据集：有许多公开可⽤的数据集可供使⽤，涵盖了各种领域和任务。例如，Common <br>
Crawl、Wikipedia、OpenWebText、BookCorpus等都是常⽤的⼤规模⽂本数据集，可以⽤于语⾔<br>
模型的训练。<br>
2. 开放数据平台：许多组织和机构提供了开放的数据平台，可以获取各种类型的数据。例如，<br>
Kaggle、UCI Machine Learning Repository、Google Dataset Search等平台都提供了丰富的数据集<br>
   Input,Reward,Action,State<br>
   "This is a sentence.",0.8,"This is a generated sentence.",[0.1, 0.2, <br>
0.3, ...]<br>
   "Another sentence.",0.2,"Another generated sentence.",[0.4, 0.5, 0.6, <br>
...]<br>
   ...<br>
</p>

<h2>第 85 页</h2>

<p>资源。<br>
3. 学术界研究：许多学术研究项⽬会公开其使⽤的数据集，可以通过相关论⽂或项⽬⻚⾯找到这些数<br>
据集。例如，NLP领域的⼀些会议和竞赛（如ACL、EMNLP、CoNLL、GLUE等）提供了公开的数<br>
据集供研究使⽤。<br>
4. 数据收集和爬取：如果没有合适的公开数据集，您可以⾃⼰进⾏数据收集和爬取。这可以通过爬⾍<br>
技术从互联⽹上收集相关的⽂本数据。需要注意的是，在进⾏数据收集和爬取时，需要遵守法律法<br>
规和⽹站的使⽤条款，并确保获得数据的合法使⽤权。<br>
5. 数据增强：如果您已经有了⼀些初始的数据集，但觉得数量不够，可以考虑使⽤数据增强技术来扩<br>
充数据。数据增强可以通过对原始数据进⾏⼀些变换、替换、合成等操作来⽣成新的样本。<br>
   ⽆论从哪个途径获取数据集，都需要注意数据的质量、版权和隐私等问题。确保您有合法的使⽤权，<br>
并遵守相关的法律和伦理规范。<br>
   <br>
5. <br>
 微调需要多少条数据？<br>
   在⼤语⾔模型训练中，微调所需的数据量可以有很⼤的变化，取决于多个因素，包括模型的规模、任<br>
务的复杂性和数据的多样性等。以下是⼀些常⻅的微调数据量的指导原则：<br>
1. ⼩规模模型：对于⼩规模的语⾔模型，通常需要较少的数据量进⾏微调。⼀般来说，⼏千到⼏万条<br>
数据可能已经⾜够。这些数据可以包括⼈⼯标注的数据、从其他来源收集的数据或者通过数据增强<br>
技术⽣成的数据。<br>
2. ⼤规模模型：对于⼤规模的语⾔模型，通常需要更多的数据量进⾏微调。数⼗万到数百万条数据可<br>
能是常⻅的范围。⼤规模模型的训练需要更多的数据来覆盖更⼴泛的语⾔知识和模式。<br>
3. 数据多样性：数据的多样性也是微调所需数据量的⼀个重要因素。如果任务的数据分布与微调数据<br>
不匹配，可能需要更多的数据来进⾏微调。例如，如果微调的任务是⽣成新闻标题，但微调数据主<br>
要是社交媒体的⽂本，可能需要更多的数据来覆盖新闻领域的语⾔模式。<br>
   需要注意的是，以上只是⼀些常⻅的指导原则，并不是绝对的规则。实际上，微调所需的数据量是⼀<br>
个经验性问题，需要根据具体任务、模型和数据情况进⾏调整。可以通过实验和验证来确定合适的数据<br>
量，以达到预期的性能和效果。<br>
   <br>
6. <br>
 有哪些⼤模型的训练集？<br>
   以下是⼀些常⽤的⼤语⾔模型训练集的示例：<br>
1. Common Crawl：这是⼀个由互联⽹上抓取的⼤规模⽂本数据集，包含了来⾃各种⽹站的⽂本内<br>
容。它是⼀个常⽤的数据集，可⽤于语⾔模型的训练。<br>
2. Wikipedia：维基百科是⼀个包含⼤量结构化⽂本的在线百科全书。维基百科的内容丰富多样，涵盖<br>
了各种领域的知识，可以作为语⾔模型训练的数据集。<br>
3. OpenWebText：这是⼀个从互联⽹上抓取的开放⽂本数据集，类似于Common Crawl。它包含了⼤<br>
量的⽹⻚⽂本，可以作为语⾔模型的训练数据。<br>
</p>

<h2>第 86 页</h2>

<p>4. BookCorpus：这是⼀个包含了⼤量图书⽂本的数据集，⽤于语⾔模型的训练。它包括了各种类型<br>
的图书，涵盖了⼴泛的主题和领域。<br>
5. News articles：新闻⽂章是另⼀个常⽤的语⾔模型训练集。可以通过从新闻⽹站、新闻API或新闻数<br>
据库中收集新闻⽂章来构建训练集。<br>
6. 其他领域特定数据集：根据具体任务和应⽤，可以使⽤特定领域的数据集来训练语⾔模型。例如，<br>
在医学领域，可以使⽤医学⽂献或医疗记录作为训练数据；在法律领域，可以使⽤法律⽂书或法律<br>
条款作为训练数据。<br>
   需要注意的是，使⽤这些数据集时，应该遵守数据的版权和使⽤规定，确保合法的使⽤权。此外，还<br>
可以通过数据增强技术，如数据合成、数据变换等，来扩充训练集的规模和多样性。<br>
   <br>
7. <br>
 进⾏领域⼤模型预训练应⽤哪些数据集⽐较好？<br>
   进⾏领域⼤模型预训练时，可以使⽤以下⼏种数据集来获得更好的效果：<br>
1. 领域特定⽂本数据集：收集与⽬标领域相关的⽂本数据集，例如专业领域的论⽂、报告、⽂档、书<br>
籍等。这些数据集可以提供领域内的专业术语、上下⽂和特定领域的知识。<br>
2. 领域内的⽹⻚内容：从⽬标领域相关的⽹⻚抓取⽂本内容。可以通过爬⾍技术从相关⽹站上获取与<br>
⽬标领域相关的⽹⻚⽂本数据。<br>
3. 领域内的新闻⽂章：收集与⽬标领域相关的新闻⽂章。新闻⽂章通常包含了领域内的最新信息和事<br>
件，可以帮助模型了解领域内的动态和趋势。<br>
4. ⾏业报告和⽩⽪书：获取与⽬标领域相关的⾏业报告、⽩⽪书和研究⽂献。这些⽂献通常包含了领<br>
域内的专业分析、统计数据和趋势预测，可以帮助模型了解⾏业背景和发展趋势。<br>
5. 社交媒体数据：收集与⽬标领域相关的社交媒体数据，如推特、微博、论坛等。社交媒体上的内容<br>
通常反映了⼈们在⽬标领域中的讨论、观点和问题，可以帮助模型了解领域内的热点和⽤户需求。<br>
6. 领域内的对话数据：获取与⽬标领域相关的对话数据，如客服对话、问答平台数据等。这些对话数<br>
据可以帮助模型学习领域内的常⻅问题、解决⽅案和⽤户需求。<br>
   在选择数据集时，应该确保数据的质量和合法性，并遵守相关的法律和伦理规范。同时，还可以考虑<br>
使⽤数据增强技术，如数据合成、数据变换等，来扩充训练集的规模和多样性。<br>
   <br>
⼤模型（LLMs）显存问题⾯<br>
 <br>
1. ⼤模型⼤概有多⼤，模型⽂件有多⼤?<br>
2. 能否⽤4 * v100 32G训练vicuna 65b？<br>
3. 如果就是想要试试65b模型，但是显存不多怎么办？<br>
4. nB模型推理需要多少显存？<br>
5. nB模型训练需要多少显存？<br>
6. 如何 估算模型所需的RAM？<br>
</p>

<h2>第 87 页</h2>

<p>7. 如何评估你的显卡利⽤率?<br>
8. 测试你的显卡利⽤率 实现细节篇<br>
1. 如何查看多机训练时的⽹速？<br>
2. 如何查看服务器上的多卡之间的NVLINK topo？<br>
3. 如何查看服务器上显卡的具体型号?<br>
4. 如何查看训练时的ﬂops？（也就是每秒的计算量）<br>
5. 如何查看对deepspeed的环境配置是否正确？<br>
6. tf32格式有多⻓？<br>
7. 哪⾥看各类显卡算⼒⽐较？<br>
8. （torch proﬁler）如何查看⾃⼰的训练中通信开销？<br>
⼤模型（LLMs）分布式训练⾯<br>
 <br>
⼤模型（LLMs）分布式训练⾯<br>
 <br>
1. 理论篇<br>
1.1 训练 ⼤语⾔模型 存在问题？<br>
1.2 什么是 点对点通信？<br>
1.3 什么是 集体通信？<br>
1.4 什么是 数据并⾏？<br>
1.5 数据并⾏ 如何 提升效率？<br>
1.6 什么是 流⽔线并⾏？<br>
1.7 什么是 张量并⾏ (intra-layer)？<br>
1.8 数据并⾏ vs 张量并⾏ vs 流⽔线并⾏?<br>
1.9 什么是 3D并⾏？<br>
1.10 想要训练1个LLM，如果只想⽤1张显卡，那么对显卡的要求是什么？<br>
1.11 如果有N张显存⾜够⼤的显卡，怎么加速训练？<br>
1.12 如果显卡的显存不够装下⼀个完整的模型呢？<br>
1.13 PP推理时，是⼀个串⾏的过程，1个GPU计算，其他空闲，有没有其他⽅式？<br>
1.14 3种并⾏⽅式可以叠加吗？<br>
1.15 Colossal-AI 有1D/2D/2.5D/3D，是什么情况？<br>
1.16 除了3D并⾏有没有其他⽅式⼤规模训练？<br>
1.17 有了ZeRO系列，为什么还需要3D并⾏？<br>
1.18 平⺠适不适合玩3D并⾏？<br>
1.19 平⺠适不适合直接上多机多卡的ZeRO3（万兆⽹）？<br>
1.20 分布式并⾏及显存优化技术并⾏技术有哪⼀些，都有什么特点？<br>
1.21 显存优化技术有哪⼀些，都有什么特点？<br>
1.22 常⻅的分布式训练框架哪⼀些，都有什么特点？<br>
2. 实践篇<br>
2.1 假如有超多的8卡A100节点（DGX A100），如何应⽤3D并⾏策略？<br>
2.2 如果想构这样⼀个⼤规模并⾏训练系统，训练框架如何选？<br>
2.3 训练框架如何选？<br>
3. 并⾏化策略选择篇<br>
3.1 如何选择⼀款分布式训练框架？<br>
</p>

<h2>第 88 页</h2>

<p>3.2 如何选择⼀款分布式训练框架？<br>
3.3 单GPU<br>
3.4 单节点多卡<br>
3.5 多节点多卡<br>
4. 问题篇<br>
4.1 推理速度验证<br>
4.2 并⾏化训练加速<br>
4.3 deepspeed 训练过程，报找不主机<br>
4.4 为什么 多机训练效率不如单机？<br>
4.5 多机训练不通，DeepSPeed配置问题<br>
图解分布式训练（⼀） —— 流⽔线并⾏（Pipeline Parallelism）⾯<br>
 <br>
为什么需要流⽔线并⾏（Pipeline Parallelism）？<br>
⼀、流⽔线并⾏（Pipeline Parallelism） 优化⽬标是什么？<br>
⼆、图解 流⽔线并⾏（Pipeline Parallelism）模型并⾏ 必要性？<br>
三、流⽔线并⾏（Pipeline Parallelism） 图解？<br>
四、流⽔线并⾏（Pipeline Parallelism）优缺点？<br>
图解分布式训练（⼆） —— nn.DataParallel⾯<br>
 <br>
为什么需要nn.DataParallel？<br>
⼀、pytorch中的GPU操作默认是什么样？<br>
⼆、介绍⼀下 nn.DataParallel 函数？<br>
三、nn.DataParallel 函数 处理逻辑 介绍⼀下？<br>
四、nn.DataParallel 函数 常⻅问题及解答 有哪些？<br>
4.1 多GPU计算减少了程序运⾏的时间？<br>
4.2 如何保存和加载多GPU训练模型呢？<br>
4.3 为什么第⼀块卡的显存会占⽤的更多⼀些？<br>
4.4 直接使⽤nn.DataParallel的时候，训练采⽤多卡训练，会出现⼀个warning？<br>
4.5 device_ids 0 被占⽤问题<br>
五、nn.DataParallel 函数 参数更新⽅式 ？<br>
六、nn.DataParallel 函数 优点 介绍⼀下？<br>
七、nn.DataParallel 函数 缺点 介绍⼀下？<br>
⼋、nn.DataParallel 函数 实战？<br>
图解分布式训练（三） —— nn.parallel.DistributedDataParallel<br>
 <br>
为什么需要 nn.parallel.DistributedDataParallel ？<br>
⼀、什么是 DistributedDataParallel 核⼼ —— Ring-AllReduce？<br>
⼆、nn.parallel.DistributedDataParallel 函数 介绍⼀下？<br>
三、nn.parallel.DistributedDataParallel 函数 如何多卡加速训练？<br>
四、nn.parallel.DistributedDataParallel 实现流程介绍⼀下？<br>
</p>

<h2>第 89 页</h2>

<p>五、nn.parallel.DistributedDataParallel 参数更新介绍⼀下？<br>
六、nn.DataParallel(以下简称DP) vs DistributedDataParallel(以下简称DDP)介绍⼀下？<br>
七、DistributedDataParallel(以下简称DDP) 优点有哪些？<br>
⼋、DistributedDataParallel(以下简称DDP) 缺点有哪些？<br>
图解分布式训练（四） —— torch.multiprocessing 详细解析<br>
 <br>
⼀、torch.multiprocessing 函数介绍⼀下？<br>
⼆、torch.multiprocessing 函数如何使⽤？<br>
三、介绍⼀下 共享CUDA张量？<br>
四、介绍⼀下 共享策略？<br>
五、torch.multiprocessing 函数使⽤<br>
图解分布式训练（五） —— AMP混合精度训练 详细解析<br>
 <br>
为什么需要 AMP混合精度训练？<br>
⼀、什么是⾃动混合精度训练(AMP)<br>
⼆、为什么需要⾃动混合精度？<br>
三、混合精度训练的优点是什么？<br>
四、混合精度训练的缺点是什么？<br>
五、混合精度训练的关键技术是什么？<br>
六、介绍⼀下 混合精度训练 动态损失缩放？<br>
七、如何在PyTorch中使⽤⾃动混合精度？<br>
⼋、如何使⽤ AMP混合精度训练 ？<br>
图解分布式训练（六） —— Pytorch的 DeepSpeed 详细解析<br>
 <br>
⼀、为什么需要 Deepspeed？<br>
⼆、DeepSpeed 基本概念 介绍⼀下？<br>
三、DeepSpeed 通信策略 介绍⼀下？<br>
四、DeepSpeed 如何使⽤？<br>
五、DeepSpeed 代码实现？<br>
七、训练精度 介绍⼀下？<br>
⼋、获取模型参数 介绍⼀下？<br>
图解分布式训练（七）—— accelerate 分布式训练 详细解析<br>
 <br>
⼀、为什么需要 accelerate 分布式训练？<br>
⼆、什么是 accelerate 分布式训练?<br>
三、accelerate 分布式训练 原理讲解？<br>
四、accelerate 分布式训练 如何实践？<br>
图解分布式训练（⼋）—— ZeRO 学习<br>
 <br>
⼀、什么是 3D 并⾏？<br>
⼆、3D 并⾏ 策略有哪些？<br>
</p>

<h2>第 90 页</h2>

<p>三、为什么需要 ZeRO？<br>
四、ZeRO 的 核⼼思想是什么？<br>
五、ZeRO 显存如何分配？<br>
六、ZeRO 优化策略是怎么样？<br>
七、ZeRO Oﬄoad后的计算流程是怎么样？<br>
⼤模型（LLMs）agent ⾯<br>
 <br>
1. 如何给LLM注⼊领域知识？<br>
2. 如果想要快速体验各种模型，该怎么办？<br>
⼤模型（LLMs）agent ⾯<br>
 <br>
1. <br>
 如何给LLM注⼊领域知识？<br>
给LLM（低层次模型，如BERT、GPT等）注⼊领域知识的⽅法有很多。以下是⼀些建议：<br>
1. 数据增强：在训练过程中，可以通过添加领域相关的数据来增强模型的训练数据。这可以包括<br>
从领域相关的⽂本中提取示例、对现有数据进⾏扩充或⽣成新的数据。<br>
2. 迁移学习：使⽤预训练的LLM模型作为基础，然后在特定领域的数据上进⾏微调。这样可以利<br>
⽤预训练模型学到的通⽤知识，同时使其适应新领域。<br>
3. 领域专家标注：与领域专家合作，对模型的输出进⾏监督式标注。这可以帮助模型学习到更准<br>
确的领域知识。<br>
4. 知识图谱：将领域知识表示为知识图谱，然后让LLM模型通过学习知识图谱中的实体和关系来<br>
理解领域知识。<br>
5. 规则和启发式⽅法：编写领域特定的规则和启发式⽅法，以指导模型的学习过程。这些⽅法可<br>
以是基于规则的、基于案例的或基于实例的。<br>
6. 模型融合：将多个LLM模型的预测结果结合起来，以提⾼模型在特定领域的性能。这可以通过<br>
投票、加权平均或其他集成⽅法来实现。<br>
7. 元学习：训练⼀个元模型，使其能够在少量领域特定数据上快速适应新领域。这可以通过在线<br>
学习、模型蒸馏或其他元学习⽅法来实现。<br>
8. 模型解释性：使⽤模型解释⼯具（如LIME、SHAP等）来理解模型在特定领域的预测原因，从<br>
⽽发现潜在的知识缺失并加以补充。<br>
9. 持续学习：在模型部署后，持续收集领域特定数据并更新模型，以保持其在新数据上的性能。<br>
10. 多任务学习：通过同时训练模型在多个相关任务上的表现，可以提⾼模型在特定领域的泛化能<br>
⼒。<br>
2. <br>
 如果想要快速体验各种模型，该怎么办？<br>
如果想要快速体验各种⼤语⾔模型，可以考虑以下⼏种⽅法：<br>
1. 使⽤预训练模型：许多⼤语⾔模型已经在⼤规模数据上进⾏了预训练，并提供了预训练好的模<br>
型参数。可以直接使⽤这些预训练模型进⾏推理，以快速体验模型的性能。常⻅的预训练模型<br>
包括GPT、BERT、XLNet等。<br>
2. 使⽤开源实现：许多⼤语⾔模型的开源实现已经在GitHub等平台上公开发布。可以根据⾃⼰的<br>
需求选择合适的开源实现，并使⽤提供的示例代码进⾏快速体验。这些开源实现通常包含了模<br>
</p>

<h2>第 91 页</h2>

<p>型的训练和推理代码，可以直接使⽤。<br>
3. 使⽤云平台：许多云平台（如Google Cloud、Microsoft Azure、Amazon Web Services等）提<br>
供了⼤语⾔模型的服务。可以使⽤这些云平台提供的API或SDK来快速体验各种⼤语⾔模型。这<br>
些云平台通常提供了简单易⽤的接⼝，可以直接调⽤模型进⾏推理。<br>
4. 使⽤在线演示：⼀些⼤语⾔模型的研究团队或公司提供了在线演示平台，可以在⽹⻚上直接体<br>
验模型的效果。通过输⼊⽂本或选择预定义的任务，可以快速查看模型的输出结果。这种⽅式<br>
可以快速了解模型的性能和功能。<br>
⽆论使⽤哪种⽅法，都可以快速体验各种⼤语⾔模型的效果。可以根据⾃⼰的需求和时间限制选择<br>
合适的⽅法，并根据体验结果进⼀步选择和优化模型。<br>
Token及模型参数准备篇<br>
 <br>
1. 预训练数据 Token 重复 是否影响 模型性能？<br>
2. SFT需要训练Token数？<br>
LLMs 位置编码篇<br>
 <br>
1 什么是位置编码？<br>
2 什么是绝对位置编码？<br>
3 什么是相对位置编码？<br>
4 旋转位置编码 RoPE篇<br>
4.1 旋转位置编码 RoPE 思路是什么？<br>
4.2 推导⼀下 旋转位置编码 RoPE ？<br>
4.3 旋转位置编码 RoPE 有什么优点？<br>
4.4 旋转位置编码 RoPE 被哪些 LLMs 应⽤？<br>
5 ⻓度外推问题篇<br>
5.1 什么是 ⻓度外推问题？<br>
5.2 ⻓度外推问题 的 解决⽅法 有哪些？<br>
6 ALiBi (Attention with Linear Biases)篇<br>
6.1 ALiBi (Attention with Linear Biases) 思路是什么？<br>
6.2 ALiBi (Attention with Linear Biases) 的偏置矩阵是什么？有什么作⽤？<br>
6.3 ALiBi (Attention with Linear Biases) 有什么优点？<br>
6.4 ALiBi (Attention with Linear Biases)  被哪些 LLMs 应⽤？<br>
LLMs Tokenizer 篇<br>
 <br>
LLMs Tokenizer 篇<br>
 <br>
Byte-Pair Encoding(BPE)篇<br>
1 Byte-Pair Encoding(BPE) 如何构建词典？<br>
WordPiece 篇<br>
1 WordPiece 与 BPE 异同点是什么？<br>
</p>

<h2>第 92 页</h2>

<p>SentencePiece 篇<br>
简单介绍⼀下 SentencePiece 思路？<br>
对⽐篇<br>
1 举例 介绍⼀下 不同 ⼤模型LLMs 的分词⽅式？<br>
2 介绍⼀下 不同 ⼤模型LLMs 的分词⽅式 的区别？<br>
怎么让英⽂⼤语⾔模型⽀持中⽂？（⼀） —— 构建中⽂tokenization<br>
 <br>
⼀、为什么需要 构建中⽂tokenization？<br>
⼆、如何对 原始数据预处理？<br>
三、如何构建中⽂的词库？<br>
四、如何使⽤transformers库加载sentencepiece模型？<br>
五、如何合并英⽂词表和中⽂词表？<br>
六、怎么使⽤修改后的词表？<br>
总结⼀下 构建中⽂tokenization？<br>
怎么让英⽂⼤语⾔模型⽀持中⽂？（⼆） —— 继续预训练篇<br>
 <br>
⼀、为什么需要进⾏继续预训练？<br>
⼆、如何对 继续预训练 数据预处理？<br>
三、如何 构建模型？<br>
四、如何 使⽤模型？<br>
怎么让英⽂⼤语⾔模型⽀持中⽂？（三） —— 对预训练模型进⾏指令微调<br>
 <br>
⼀、为什么需要对预训练模型进⾏指令微调？<br>
⼆、对预训练模型进⾏指令微调 数据 如何处理？<br>
三、对预训练模型进⾏指令微调 tokenization 如何构建？<br>
四、对预训练模型进⾏指令微调 模型 如何构建？<br>
五、是否可以结合 其他库 使⽤？<br>
Layer normalization 篇<br>
 <br>
Layer normalization-⽅法篇<br>
Layer Norm 篇<br>
Layer Norm 的计算公式写⼀下？<br>
RMS Norm 篇 （均⽅根 Norm）<br>
RMS Norm 的计算公式写⼀下？<br>
RMS Norm 相⽐于 Layer Norm 有什么特点？<br>
Deep Norm 篇<br>
Deep Norm 思路？<br>
写⼀下 Deep Norm 代码实现？<br>
Deep Norm 有什么优点？<br>
</p>

<h2>第 93 页</h2>

<p>Layer normalization-位置篇<br>
1 LN 在 LLMs 中的不同位置 有什么区别么？如果有，能介绍⼀下区别么？<br>
Layer normalization 对⽐篇<br>
LLMs 各模型分别⽤了 哪种 Layer normalization？<br>
LLMs 激活函数篇<br>
 <br>
1 介绍⼀下 FFN 块 计算公式？<br>
2 介绍⼀下 GeLU 计算公式？<br>
3 介绍⼀下 Swish 计算公式？<br>
4 介绍⼀下 使⽤ GLU 线性⻔控单元的 FFN 块 计算公式？<br>
5 介绍⼀下 使⽤ GeLU 的 GLU 块 计算公式？<br>
6 介绍⼀下 使⽤ Swish 的 GLU 块 计算公式？<br>
各LLMs 都使⽤哪种激活函数？<br>
LLMs 激活函数篇<br>
 <br>
1 介绍⼀下 FFN 块 计算公式？<br>
2 介绍⼀下 GeLU 计算公式？<br>
3 介绍⼀下 Swish 计算公式？<br>
4 介绍⼀下 使⽤ GLU 线性⻔控单元的 FFN 块 计算公式？<br>
5 介绍⼀下 使⽤ GeLU 的 GLU 块 计算公式？<br>
6 介绍⼀下 使⽤ Swish 的 GLU 块 计算公式？<br>
各LLMs 都使⽤哪种激活函数？<br>
⼤模型（LLMs）加速篇<br>
 <br>
⼤模型（LLMs）加速篇<br>
 <br>
1. 当前优化模型最主要技术⼿段有哪些？<br>
2. 推理加速框架有哪⼀些？都有什么特点？<br>
3 vLLM 篇<br>
3.1 vLLM 的 功能有哪些？<br>
3.2 vLLM 的 优点有哪些？<br>
3.3 vLLM 的 缺点有哪些？<br>
3.4 vLLM 离线批量推理？<br>
3.5 vLLM API Server？<br>
4 Text generation inference 篇<br>
4.1 介绍⼀下 Text generation inference？<br>
4.2 Text generation inference 的 功能有哪些？<br>
4.3 Text generation inference 的 优点有哪些？<br>
4.4 Text generation inference 的 缺点有哪些？<br>
4.5 Text generation inference 的 使⽤docker运⾏web server？<br>
</p>

<h2>第 94 页</h2>

<p>LLM（⼤语⾔模型）部署加速⽅法——PagedAttention篇<br>
 <br>
⼀、vLLM ⽤于⼤模型并⾏推理加速 存在什么问题？<br>
⼆、vLLM 如何 优化 ⼤模型并⾏推理加速？<br>
三、什么是 PagedAttention？<br>
四、 PagedAttention 如何存储 连续的key和value？<br>
五、 PagedAttention 技术细节？<br>
六、 PagedAttention 如何 实现安全共享？<br>
七、 PagedAttention 源码介绍？<br>
⼤模型推理加速⼯具 —— vLLM<br>
 <br>
⼀、引⾔<br>
1.1 前⾔<br>
1.2 为什么 需要 vLLM ?<br>
1.3 vLLM 具有哪些特点 ?<br>
1.4 vLLM ⽀持哪些 Huggingface 模型 ?<br>
⼆、vLLM 性能如何？<br>
三、vLLM 依赖包<br>
四、vLLM 如何安装？<br>
五、vLLM 如何使⽤？<br>
六、vLLM 分布式推理与服务<br>
LLM（⼤语⾔模型）部署加速⽅法——Faster Transformer篇<br>
 <br>
⼀、为什么需要 FasterTransformer？<br>
⼆、FasterTransformer 介绍⼀下？<br>
三、FasterTransformer 核⼼是什么？<br>
四、FasterTransformer 优化？<br>
纯Python超轻量⾼性能LLM推理框架 —— LightLLM<br>
 <br>
⼀、引⾔<br>
1.1 前⾔<br>
1.2 为什么 需要 LightLLM ?<br>
1.3 ⽬前 LLM推理框架 有 哪些?<br>
⼆、LightLLM 介绍⼀下？<br>
2.1 什么是 LightLLM ？<br>
2.2 Token Attention 介绍？<br>
2.3 Eﬃcient Router 介绍？<br>
三、LightLLM 性能表现 介绍？<br>
四、LightLLM 依赖包 有哪些？<br>
五、LightLLM  如何安装？<br>
</p>

<h2>第 95 页</h2>

<p>5.1 下载 LightLLM<br>
5.2 安装 LightLLM 依赖<br>
5.3 安装 LightLLM<br>
六、LightLLM 如何使⽤？<br>
6.1 启动 LightLLM 服务<br>
填坑笔记<br>
LightLLM ⽀持模型 LLMs 模型？<br>
Attention 升级⾯<br>
 <br>
1 传统 Attention 存在哪些问题？<br>
2 Attention 优化⽅向<br>
3 Attention 变体有哪些？<br>
4 Multi-Query Attention 篇<br>
4.1 Multi-head Attention 存在什么问题？<br>
4.2 介绍⼀下 Multi-Query Attention？<br>
4.3 对⽐⼀下 Multi-head Attention 和 Multi-Query Attention？<br>
4.4 Multi-Query Attention 这样做的好处是什么？<br>
4.5 有 哪些模型 是 使⽤ Multi-Query Attention？<br>
5 Grouped-query Attention<br>
5.1 什么是 Grouped-query Attention？<br>
5.2 有哪些⼤模型使⽤ Grouped-query Attention？<br>
6 FlashAttention 介绍⼀下<br>
7 并⾏ transformer block 介绍⼀下？<br>
⼤模型幻觉（LLM Hallucination）⾯<br>
 <br>
⼤模型幻觉（LLM Hallucination）⾯<br>
 <br>
⼀、什么是⼤模型幻觉？<br>
⼆、为什么LLM会产⽣幻觉？<br>
三、为什么需要解决LLM的幻觉问题？<br>
四、幻觉⼀定是有害的吗？<br>
五、幻觉有哪些不同类型？<br>
六、如何度量幻觉？<br>
七、如何缓解LLM幻觉？<br>
7.1 通过使⽤外部知识验证主动检测和减轻幻觉<br>
7.2 事实核⼼采样<br>
7.3 SelfCheckGPT<br>
⼋、LLMs什么时候最容易产⽣幻觉？<br>
</p>

<h2>第 96 页</h2>

<p>⼤模型的幻觉问题篇<br>
 <br>
⼀、什么是 ⼤模型幻觉问题？<br>
⼆、为什么 会 出现 ⼤模型幻觉问题？<br>
三、如何 评估 ⼤模型幻觉问题？<br>
四、如何 缓解 ⼤模型幻觉问题？<br>
⼤模型的幻觉问题篇<br>
 <br>
⼀、为什么 会 出现 ⼤模型幻觉？<br>
⼆、如何 缓解 ⼤模型幻觉？<br>
LLMs 对⽐篇<br>
 <br>
LLMs 对⽐篇<br>
 <br>
LLMs 训练数据 和 数据量 对⽐如何？<br>
百川智能baichuan7B、13B、53B、baichuan2 总结篇<br>
 <br>
⼀、baichuan-7B篇<br>
1. 你了解baichuan-7B解构么？介绍⼀下？<br>
2. baichuan-7B 如何 收集原始数据并 构建 训练数据？<br>
3. baichuan-7B 如何 提⾼ 训练稳定性和吞吐？<br>
⼆、baichuan-13B篇<br>
1. 相⽐于 baichuan-7B，baichuan-13B 的 特点体现在哪⾥？<br>
2. 如何 对 baichuan-13B 进⾏推理和部署？<br>
3. 如何 对 baichuan-13B 进⾏微调？<br>
三、baichuan-53B篇<br>
3.1 baichuan-53B 相⽐于 baichuan-7B 和 baichuan-13B 有哪些优势？<br>
3.2 baichuan-53B 如何对 预训练数据 做处理？<br>
3.3 baichuan-53B 如何进⾏ 搜索增强？<br>
四、baichuan2篇<br>
4.1 baichuan2 与 其他⼤模型 对⽐<br>
五、baichuan 数据构建篇<br>
5.1 baichuan 进⾏微调时，领域数据：通⽤数据配⽐？<br>
思维链 Chain-of-Thought（COT）篇<br>
 <br>
思维链 Chain-of-Thought（COT）篇<br>
 <br>
⼀、什么是思维链提示？<br>
⼆、思维链提示本质是什么？<br>
三、思维链提示 与 标准的提示学习⽅法有什么不同?<br>
</p>

<h2>第 97 页</h2>

<p>四、思维链提示 为什么可以提⾼语⾔模型的复杂推理能⼒?它的优势在哪⾥?<br>
五、思维链提示 适⽤场景 有 哪些？<br>
六、思维链提示 ⽬前还存在哪些不⾜点？<br>
七、思维链提示 对推动语⾔模型复杂推理能⼒研究有哪些启发和影响?<br>
⼋、思维链提示 对实现真正的通⽤⼈⼯智能仍⾯临哪些挑战?<br>
九、如何通过增加模型规模来获得语⾔模型强⼤的思路链推理能⼒的?这与模型获得的哪些能⼒有<br>
关?<br>
⼗、你认为可以在哪些其他⽅⾯应⽤“思路链提示”这⼀思路来提升语⾔模型的能⼒?<br>
⼗⼀、如果需要你对 思维链提示 进⾏改进，你觉得你会改进哪些地⽅？<br>
⼗⼆、思维链提示 未来研究⽅向？<br>
思维链 Chain-of-Thought（COT）变体篇<br>
 <br>
思维链 Chain-of-Thought（COT）：思维链的启蒙<br>
1. 什么是 思维链 Chain-of-Thought（COT）？<br>
2. 思维链 Chain-of-Thought（COT）是思路是什么？<br>
3. 思维链 Chain-of-Thought（COT）存在问题？<br>
思维树 Tree of Thoughts（TOT）：⼀种⽤树结构解决复杂问题的⽅法<br>
1. 为什么需要 思维树 Tree of Thoughts（TOT）？<br>
2. 什么是 思维树 Tree of Thoughts（TOT）？<br>
3. 思维树 Tree of Thoughts（TOT）涉及问题有哪些？<br>
思维图 Graph of Thoughts（GOT）：⼀种把思维链过程建模层图结构的⽅法<br>
1. 为什么 需要 思维图 Graph of Thoughts（GOT）？<br>
2. 什么是 思维图 Graph of Thoughts（GOT） ？<br>
3. 思维图 Graph of Thoughts（GOT）核⼼思想是什么 ？<br>
思维算法 Algorithm of Thoughts（AOT）：⼀种⽤DFS/BFS示例解决问题的⽅法<br>
1. 为什么 需要 思维算法 Algorithm of Thoughts（AOT）？<br>
2. 思维算法 Algorithm of Thoughts（AOT）思路是什么？<br>
3. 思维算法 Algorithm of Thoughts（AOT） vs 其他 COT 的 区别？<br>
思维链 Chain-of-Thought（COT） 有哪些 应⽤场景？<br>
思维链 Chain-of-Thought（COT） 有哪些 局限性？<br>
思维链 Chain-of-Thought（COT）变体篇<br>
 <br>
⼀、为什么需要 Graph RAG？<br>
⼆、什么是 Graph RAG？<br>
三、Graph RAG 思路介绍？<br>
四、⽤代码 介绍 Graph RAG ？<br>
五、⽤ 示例 介绍 Graph RAG ？<br>
六、Graph RAG 排序优化⽅式？<br>
 <br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:56</p>
        </div>
    </div>
</body>
</html>