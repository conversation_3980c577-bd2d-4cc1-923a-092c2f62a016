<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>54-pytorch 分布式计算 坑-bug 梳理篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>54-pytorch 分布式计算 坑-bug 梳理篇</h1>
        <h2>第 1 页</h2>

<p>pytorch 分布式计算 坑/bug 梳理篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:44<br>
动机<br>
pytorch用的人越来越多，大的模型都需要用gpu或者多张gpu甚至多节点多卡进行分布式计算，但是坑也很多，<br>
本文主要 介绍 读者 在 进行 pytorch 分布式计算 所遇到的 坑/bug 的 梳理 及 填坑记录。<br>
一、使用 DistributedDataParallel（分布式并行）时，显存分布不均衡问题<br>
如果用 DistributedDataParallel （分布式并行）的时候，每个进程单独跑在一个 GPU 上，多个卡的显存占用用<br>
该是均匀的，比如像这样的：<br>
注：在 Distributed 模式下，相当于你的代码分别在多个 GPU 上独立的运行，代码都是设备无关<br>
的。比如你写 t = torch.zeros(100, 100).cuda()，在4个进程上运行的程序会分别在4个 GPUs 上<br>
初始化 t。所以显存的占用会是均匀的。<br>
• pytorch 分布式计算 坑/bug 梳理篇<br>
• 动机<br>
• 一、使用 DistributedDataParallel（分布式并行）时，显存分布不均衡问题<br>
• 二、如果是用pytorch实现同步梯度更新，自研 数据接口，出现 第一个epoch结尾处程序卡死问题<br>
• 三、在微调大模型的时候，单机2卡的时候正常训练，但是采用4卡及其以上，就会卡住，卡在读完数<br>
据和开始训练之间？<br>
• 问题描述：<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>然而，有时会发现另外几个进程会在0卡上占一部分显存，导致0卡显存出现瓶颈，可能会导致cuda-out-of-<br>
memory 错误。比如这样的：<br>
该问题主要由 以下代码导致：<br>
注：上述代码运行后，程序 load 一个 pretrained model 的时候，torch.load() 会默认把load进来的数据放到0卡<br>
上，这样4个进程全部会在0卡占用一部分显存。<br>
把load进来的数据map到cpu上：<br>
二、如果是用pytorch实现同步梯度更新，自研 数据接口，出现 第一个epoch结尾处程序卡死问题<br>
如果是用pytorch实现同步梯度更新，然后数据接口是自己写的话一定要注意保证每张卡分配的batch数是一样<br>
的。因为如果某张卡少了一个batch的话，其他卡就会等待，从而程序卡在torch.all_reduce()上。最后的情况就会<br>
出现在第一个epoch结尾处程序卡住，而且没有报错信息。<br>
三、在微调大模型的时候，单机2卡的时候正常训练，但是采用4卡及其以上，就会卡住，卡在读完数据和开始训<br>
练之间？<br>
先确认几张卡都能正常使用和通信，然后看看是不是batchsize分配之类的问题导致无限等待某一张卡了。再就是<br>
只留4条数据，每张卡只跑一条数据试试看。<br>
• 问题定位：<br>
    checkpoint = torch.load("checkpoint.pth")<br>
    model.load_state_dict(checkpoint["state_dict"])<br>
• 解决方法：<br>
    checkpoint = torch.load("checkpoint.pth", map_location=torch.device('cpu'))<br>
    model.load_state_dict(checkpoint["state_dict"])<br>
</p>

<h2>第 3 页</h2>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:56</p>
        </div>
    </div>
</body>
</html>