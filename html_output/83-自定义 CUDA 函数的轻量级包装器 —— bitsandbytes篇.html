<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>83-自定义 CUDA 函数的轻量级包装器 —— bitsandbytes篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>83-自定义 CUDA 函数的轻量级包装器 —— bitsandbytes篇</h1>
        <h2>第 1 页</h2>

<p>自定义 CUDA 函数的轻量级包装器 —— bitsandbytes篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:14<br>
一、什么是 bitsandbytes?<br>
bitsandbytes 是自定义 CUDA 函数的轻量级包装器，特别是 8 比特优化器、矩阵乘法和量化函数。 主要特征如<br>
下：<br>
二、如何才能使用 bitsandbytes？<br>
量化模型的唯一条件是包含 torch.nn.Linear 层，因此量化对于任何模态都可以实现开箱即用。用户可以开箱即用<br>
地加载诸如 Whisper、ViT、Blip2 之类的 8 比特或 4 比特(FP4/NF4)模型。<br>
三、如何使用 bitsandbytes？<br>
使用 NF4 量化加载 4 比特模型的示例：<br>
使用 FP4 量化加载 4 比特模型的示例：<br>
• 自定义 CUDA 函数的轻量级包装器 —— bitsandbytes篇<br>
• 一、什么是 bitsandbytes?<br>
• 二、如何才能使用 bitsandbytes？<br>
• 三、如何使用 bitsandbytes？<br>
• 致谢<br>
• 具有混合精度分解的 8 比特矩阵乘法<br>
• LLM.int8() 推理<br>
• 8 比特优化器：Adam、AdamW、RMSProp、LARS、LAMB、Lion（节省 75% 内存）<br>
• 稳定的嵌入层：通过更好的初始化和标准化提高稳定性<br>
• 8 比特量化：分位数、线性和动态量化<br>
• 快速的分位数估计：比其他算法快 100 倍<br>
from transformers import BitsAndBytesConfig<br>
nf4_config = BitsAndBytesConfig(<br>
   load_in_4bit=True,<br>
   bnb_4bit_quant_type="nf4",<br>
   bnb_4bit_use_double_quant=True,<br>
   bnb_4bit_compute_dtype=torch.bfloat16<br>
)<br>
model_nf4 = AutoModelForCausalLM.from_pretrained(model_id, <br>
quantization_config=nf4_config)<br>
import torch<br>
from transformers import BitsAndBytesConfig<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>知识星球<br>
quantization_config = BitsAndBytesConfig(<br>
   load_in_4bit=True,<br>
   bnb_4bit_compute_dtype=torch.bfloat16<br>
)<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:57</p>
        </div>
    </div>
</body>
</html>