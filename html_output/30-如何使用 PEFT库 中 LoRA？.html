<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>30-如何使用 PEFT库 中 LoRA？</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>30-如何使用 PEFT库 中 LoRA？</h1>
        <h2>第 1 页</h2>

<p>如何使用 PEFT库 中 LoRA？<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月28日 10:12<br>
一、前言<br>
本文章 主要介绍 使用 LoRA 对 大模型进行 高效参数微调，涉及内容：<br>
涉及框架<br>
• 如何使用 PEFT库 中 LoRA？<br>
• 一、前言<br>
• 二、如何 配置 LoraConfig？<br>
• 三、模型 加入PEFT策略<br>
• 3.1 模型加载 策略有哪些？<br>
• 3.2 模型显存占用的部分有哪些？<br>
• 3.3 模型显存占用 优化策略？<br>
• 3.3.1 8bit量化 优化策略？<br>
• 3.3.2 梯度检查 优化策略？<br>
• 3.4 如何 向 模型 加入PEFT策略？<br>
• 四、PEFT库 中 LoRA 模块 代码介绍<br>
• 4.1 PEFT库 中 LoRA 模块 整体实现思路<br>
• 4.2 PEFT库 中 LoRA 模块 _find_and_replace() 实现思路<br>
• 4.3 PEFT库 中 Lora层的 实现思路<br>
• 4.3.1 基类 LoraLayer 实现<br>
• 4.3.2 Linear 实现<br>
• 五、使用 LoRA 对 大模型进行 高效参数微调，如何进行存储？<br>
• 六、使用 LoRA 对 大模型进行 推理，如何进行加载？<br>
• 七、huggingface大模型如何加载多个LoRA并随时切换？<br>
• 参考<br>
1. PEFT库 中 LoRA 模块使用；<br>
2. PEFT库 中 LoRA 模块 代码介绍；<br>
3. 在推理时如何先进行weight的合并在加载模型进行推理；<br>
# 以下配置可能会随时间变化，出了问题就去issue里面刨吧<br>
# 要相信你不是唯一一个大冤种！<br>
accelerate<br>
appdirs<br>
loralib<br>
bitsandbytes<br>
black<br>
black[jupyter]<br>
datasets<br>
fire<br>
transformers&gt;=4.28.0<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>二、如何 配置 LoraConfig？<br>
注意：target_modules中的作用目标名在不同模型中的名字是不一样的。query_key_value是在<br>
ChatGLM中的名字<br>
三、模型 加入PEFT策略<br>
3.1 模型加载 策略有哪些？<br>
模型加载虽然很简单，这里涉及到2个时间换空间的大模型显存压缩技巧，主要说下load_in_8bit和<br>
prepare_model_for_int8_training。<br>
git+https://github.com/huggingface/peft.git<br>
sentencepiece<br>
gradio<br>
wandb<br>
cpm-kernel<br>
  # 设置超参数及配置<br>
  LORA_R = 8<br>
  LORA_ALPHA = 16<br>
  LORA_DROPOUT = 0.05<br>
  TARGET_MODULES = [<br>
      "q_proj",<br>
      "v_proj",<br>
  ]<br>
  config = LoraConfig(<br>
      r=LORA_R,<br>
      lora_alpha=LORA_ALPHA,<br>
      target_modules=TARGET_MODULES,<br>
      lora_dropout=LORA_DROPOUT,<br>
      bias="none",<br>
      task_type="CAUSAL_LM",<br>
  )<br>
• 参数介绍：<br>
• r：lora的秩，矩阵A和矩阵B相连接的宽度，r&lt;&lt;d；<br>
• lora_alpha：归一化超参数，lora参数 ΔWx 被以 α/r 归一化，以便减少改变r rr时需要重新训练的计算<br>
量；<br>
• target_modules：lora的目标位置；<br>
• merge_weights:eval模式中，是否将lora矩阵的值加到原有 W0 的值上;<br>
• lora_dropout：lora层的dropout比率；<br>
• fan_in_fan_out：只有应用在Conv1D层时置为True，其他情况False；<br>
• bias: 是否可训练bias，none：均不可；all：均可；lora_only：只有lora部分的bias可训练；<br>
• task_type：这是LoraConfig的父类PeftConfig中的参数，设定任务的类型；<br>
• modules_to_save：除了lora部分之外，还有哪些层可以被训练，并且需要保存；<br>
</p>

<h2>第 3 页</h2>

<p>3.2 模型显存占用的部分有哪些？<br>
这里需要介绍一下 两个模型显存占用的部分：<br>
3.3 模型显存占用 优化策略？<br>
模型显存占用 有以下两种方式：<br>
3.3.1 8bit量化 优化策略？<br>
参考：https://huggingface.co/blog/hf-bitsandbytes-integration<br>
from_pretrained中的load_in_8bit参数是bitsandbytes库赋予的能力，会把加载模型转化成混合8bit的量化模型，<br>
注意这里的8bit模型量化只用于模型推理，通过量化optimizer state降低训练时显存的时8bit优化器是另一个功能<br>
不要搞混哟~<br>
模型量化本质是对浮点参数进行压缩的同时，降低压缩带来的误差。 8-bit quantization是把原始FP32（4字节）<br>
压缩到Int8（1字节）也就是1/4的显存占用。如上加载后会发现除lora层外的多数层被转化成int类型如下<br>
    from peft import get_peft_model, LoraConfig, prepare_model_for_int8_training, <br>
set_peft_model_state_dict<br>
    from transformers import AutoTokenizer, AutoModel<br>
    model = AutoModel.from_pretrained(<br>
        "THUDM/chatglm3-6b", load_in_8bit=True, torch_dtype=torch.float16, <br>
trust_remote_code=True, device_map="auto"<br>
    )<br>
    tokenizer = AutoTokenizer.from_pretrained(<br>
        "THUDM/chatglm3-6b", trust_remote_code=True<br>
    )<br>
    model = prepare_model_for_int8_training(model)<br>
1. 静态显存基本由模型参数量级决定；<br>
2. 动态显存在向前传播的过程中每个样本的每个神经元都会计算激活值并存储，用于向后传播时的梯度计算，<br>
这部分和batchsize以及参数量级相关；<br>
1. 8bit量化优化。该方式只要用于优化 静态显存；<br>
2. 梯度检查优化。该方式只要用于优化 动态显存；<br>
</p>

<h2>第 4 页</h2>

<p>当然压缩方式肯定不是直接四舍五入，那样会带来巨大的精度压缩损失。常见的量化方案有absolute-maximum<br>
和zero-point，它们的差异只是rescale的方式不同，这里简单说下absmax，如下<br>
先寻找tensor矩阵的绝对值的最大值，并计算最大值到127的缩放因子，然后使用该缩放因子对整个tensor进行缩<br>
放后，再round到整数。这样就把浮点数映射到了INT8,逆向回到float的原理相同。<br>
当然以上的缩放方案依旧存在精度损失，以及当矩阵中存在outlier时，这个精度损失会被放大，例如当tensor中<br>
绝大部分取值在1以下，有几个值在100+，则缩放后，所有1以下的tensor信息都会被round抹去。因此LLM.int8()<br>
的实现对outlier做了进一步的优化，把outlier和非outlier的矩阵分开计算，再把结果进行合并来降低outlier对精度<br>
的影响<br>
</p>

<h2>第 5 页</h2>

<p>prepare_model_for_int8_training是对在Lora微调中使用LLM.int8()进行了适配用来提高训练的稳定性，主要包括<br>
3.3.2 梯度检查 优化策略？<br>
参考：https://medium.com/tensorflow/fitting-larger-networks-into-memory-583e3c758ff9<br>
prepare_model_for_int8_training函数还做了一件事就是设置gradient_checkpointing=True，这是另一个时间换<br>
空间的技巧。<br>
gradient checkpoint的实现是在向前传播的过程中使用torch.no_grad()不去存储中间激活值，降低动态显存的占<br>
用。而只是保存输入和激活函数，当进行反向传播的时候，会重新获取输入和激活函数计算激活值用于梯度计<br>
算。因此向前传播会计算两遍，所以需要更多的训练时间。<br>
3.4 如何 向 模型 加入PEFT策略？<br>
其实lora微调的代码本身并不复杂，相反是如何加速大模型训练，降低显存占用的一些技巧大家可能不太熟悉。<br>
模型初始化代码如下，get_peft_model会初始化PeftModel把原模型作为base模型，并在各个self-attention层加<br>
入lora层，同时改写模型forward的计算方式。<br>
注：use_cache设置为False，是因为和gradient checkpoint存在冲突。因为use_cache是对解码<br>
速度的优化，在解码器解码时，存储每一步输出的hidden-state用于下一步的输入，而因为开启<br>
了gradient checkpoint，中间激活值不会存储，因此use_cahe=False。其实#21737已经加入了<br>
参数检查，这里设置只是为了不输出warning。<br>
四、PEFT库 中 LoRA 模块 代码介绍<br>
4.1 PEFT库 中 LoRA 模块 整体实现思路<br>
具体 PEFT 包装 包装，结合PEFT模块的源码，来看一下LORA是如何实现的。<br>
在PEFT模块中，peft_model.py中的PeftModel类是一个总控类，用于模型的读取保存等功能，继承了<br>
transformers中的Mixin类，我们主要来看LORA的实现：<br>
• layer norm层保留FP32精度<br>
• 输出层保留FP32精度保证解码时随机sample的差异性<br>
  # 加入PEFT策略<br>
  model = get_peft_model(model, config)<br>
  model = model.to(device)<br>
  model.config.use_cache = False<br>
</p>

<h2>第 6 页</h2>

<p>代码位置：https://github.com/huggingface/peft/blob/main/src/peft/tuners/lora.py<br>
从构造方法可以看出，这个类在创建的时候主要做了两步：<br>
4.2 PEFT库 中 LoRA 模块 _find_and_replace() 实现思路<br>
_find_and_replace() 实现思路：<br>
注：其中这个replace的方法并不复杂，就是把原来的weight和bias赋给新创建的module，然后再分配到指定的设<br>
备上：<br>
class LoraModel(torch.nn.Module):<br>
    def __init__(self, config, model):<br>
        super().__init__()<br>
        self.peft_config = config<br>
        self.model = model<br>
        self._find_and_replace()<br>
        mark_only_lora_as_trainable(self.model, self.peft_config.bias)<br>
        self.forward = self.model.forward<br>
• 第一步：self._find_and_replace()。找到所有需要加入lora策略的层，例如q_proj，把它们替换成lora模式；<br>
• 第二步：mark_only_lora_as_trainable(self.model, self.peft_config.bias)。保留lora部分的参数可训练，其余<br>
参数全都固定下来不动；<br>
1. 找到需要的做lora的层：<br>
  # 其中的target_modules在上面的例子中就是"q_proj"，"v_proj"<br>
  # 这一步就是找到模型的各个组件中，名字里带"q_proj"，"v_proj"的<br>
  target_module_found = re.fullmatch(self.peft_config.target_modules, key)<br>
2. 对于每一个找到的目标层，创建一个新的lora层：<br>
  # 注意这里的Linear是在该py中新建的类，不是torch的Linear<br>
  new_module = Linear(target.in_features, target.out_features, bias=bias, **kwargs)<br>
3. 调用_replace_module方法替换掉原来的linear：<br>
  self._replace_module(parent, target_name, new_module, target)<br>
    def _replace_module(self, parent_module, child_name, new_module, old_module):<br>
        setattr(parent_module, child_name, new_module)<br>
        new_module.weight = old_module.weight<br>
        if old_module.bias is not None:<br>
            new_module.bias = old_module.bias<br>
        if getattr(old_module, "state", None) is not None:<br>
            new_module.state = old_module.state<br>
            new_module.to(old_module.weight.device)<br>
        # dispatch to correct device<br>
        for name, module in new_module.named_modules():<br>
            if "lora_" in name:<br>
                module.to(old_module.weight.device)<br>
</p>

<h2>第 7 页</h2>

<p>4.3 PEFT库 中 Lora层的 实现思路<br>
4.3.1 基类 LoraLayer 实现<br>
Lora的基类，可以看出这个类就是用来构造Lora的各种超参数用：<br>
4.3.2 Linear 实现<br>
上文中所提到的Linear类，也就是Lora的具体实现，它同时继承了nn.Linear和LoraLayer：<br>
class LoraLayer:<br>
    def __init__(<br>
        self,<br>
        r: int,<br>
        lora_alpha: int,<br>
        lora_dropout: float,<br>
        merge_weights: bool,<br>
    ):<br>
        self.r = r<br>
        self.lora_alpha = lora_alpha<br>
        # Optional dropout<br>
        if lora_dropout &gt; 0.0:<br>
            self.lora_dropout = nn.Dropout(p=lora_dropout)<br>
        else:<br>
            self.lora_dropout = lambda x: x<br>
        # Mark the weight as unmerged<br>
        self.merged = False<br>
        self.merge_weights = merge_weights<br>
        self.disable_adapters = False<br>
class Linear(nn.Linear, LoraLayer):<br>
    # Lora implemented in a dense layer<br>
    def __init__(<br>
        self,<br>
        in_features: int,<br>
        out_features: int,<br>
        r: int = 0,<br>
        lora_alpha: int = 1,<br>
        lora_dropout: float = 0.0,<br>
        fan_in_fan_out: bool = False,  # Set this to True if the layer to replace <br>
stores weight like (fan_in, fan_out)<br>
        merge_weights: bool = True,<br>
        **kwargs,<br>
    ):<br>
        nn.Linear.__init__(self, in_features, out_features, **kwargs)<br>
        LoraLayer.__init__(self, r=r, lora_alpha=lora_alpha, <br>
lora_dropout=lora_dropout, merge_weights=merge_weights)<br>
        self.fan_in_fan_out = fan_in_fan_out<br>
</p>

<h2>第 8 页</h2>

<p>在构造方法中，除了对各个超参数进行配置之外，还对所有参数进行了初始化，定义如下：<br>
其中lora的A矩阵采用了kaiming初始化，是Xavier初始化针对非线性激活函数的一种优化；B矩阵采用了零初始<br>
化，以确保在初始状态 ΔW=BA 为零。（值得注意的是在LORA的论文中，A采用的是Gaussian初始化）。<br>
对于train和eval方法，放在一起介绍，它主要是需要对merge状态进行记录：<br>
        # Actual trainable parameters<br>
        if r &gt; 0:<br>
            self.lora_A = nn.Linear(in_features, r, bias=False)<br>
            self.lora_B = nn.Linear(r, out_features, bias=False)<br>
            self.scaling = self.lora_alpha / self.r<br>
            # Freezing the pre-trained weight matrix<br>
            self.weight.requires_grad = False<br>
        self.reset_parameters()<br>
        if fan_in_fan_out:<br>
            self.weight.data = self.weight.data.T<br>
    def reset_parameters(self):<br>
        nn.Linear.reset_parameters(self)<br>
        if hasattr(self, "lora_A"):<br>
            # initialize A the same way as the default for nn.Linear and B to zero<br>
            nn.init.kaiming_uniform_(self.lora_A.weight, a=math.sqrt(5))<br>
            nn.init.zeros_(self.lora_B.weight)<br>
    def train(self, mode: bool = True):<br>
        # 对于新定义的这个Linear层，其本身继承了torch.nn.Linear，所以需要调用<br>
nn.Linear.train(self, mode)来控制一下自身原本参数的状态，并且此外它加入了lora_A和<br>
lora_B两部分额外的参数，这两部分本质上也是nn.Linear，也需要控制状态。<br>
        nn.Linear.train(self, mode)<br>
        self.lora_A.train(mode)<br>
        self.lora_B.train(mode)<br>
        # not mode说明是eval模式<br>
        # self.merge_weights在上文中有介绍，是配置文件中的，意思是评估时是否需要将<br>
lora部分的weight加到linear层原本的weight中<br>
        # not self.merged是状态的记录<br>
        if not mode and self.merge_weights and not self.merged:<br>
            # 如果设置了需要融合，而当前状态没有融合的话，就把lora部分的参数scale之<br>
后加上去，并且更新self.merged状态<br>
            if self.r &gt; 0:<br>
                self.weight.data += (<br>
                    transpose(self.lora_B.weight @ self.lora_A.weight, <br>
self.fan_in_fan_out) * self.scaling<br>
                )<br>
            self.merged = True<br>
        elif self.merge_weights and self.merged:<br>
            # 为了在训练的过程中，确保linear本身的weights是没有经过融合过的（理论上<br>
这一步应该是在eval之后的下一轮train的第一个step触发）<br>
            if self.r &gt; 0:<br>
</p>

<h2>第 9 页</h2>

<p>注：为什么是在train中涉及merge_weights，其实在torch的源码中，nn.Linear.eval()实际上是调<br>
用了nn.Linear.train(mode=False)，所以这里train方法中的merge_weigths，实际上是在eval中也<br>
发挥作用的。<br>
forward中也是类似的原理，正常情况下训练过程应该是走elif的分支：<br>
五、使用 LoRA 对 大模型进行 高效参数微调，如何进行存储？<br>
因为peftModel重写了原始model的save_pretrained函数，只把lora层的权重进行存储，因此<br>
model.save_pretrained只会存储lora权重。而trainer的save_model函数没有做相应的重写，因此我们重写下<br>
对应的function，避免checkpoint写入原始模型全部参数。<br>
                self.weight.data -= (<br>
                    transpose(self.lora_B.weight @ self.lora_A.weight, <br>
self.fan_in_fan_out) * self.scaling<br>
                )<br>
            self.merged = False<br>
    def eval(self):<br>
        nn.Linear.eval(self)<br>
        self.lora_A.eval()<br>
        self.lora_B.eval()<br>
    def forward(self, x: torch.Tensor):<br>
        if self.disable_adapters:<br>
            if self.r &gt; 0 and self.merged:<br>
                self.weight.data -= (<br>
                    transpose(self.lora_B.weight @ self.lora_A.weight, <br>
self.fan_in_fan_out) * self.scaling<br>
                )<br>
                self.merged = False<br>
            return F.linear(x, transpose(self.weight, self.fan_in_fan_out), <br>
bias=self.bias)<br>
        elif self.r &gt; 0 and not self.merged:<br>
            result = F.linear(x, transpose(self.weight, self.fan_in_fan_out), <br>
bias=self.bias)<br>
            if self.r &gt; 0:<br>
                result += self.lora_B(self.lora_A(self.lora_dropout(x))) * <br>
self.scaling<br>
            return result<br>
        else:<br>
            return F.linear(x, transpose(self.weight, self.fan_in_fan_out), <br>
bias=self.bias)<br>
import datasets<br>
from transformers import Trainer, DataCollatorForSeq2Seq<br>
</p>

<h2>第 10 页</h2>

<p>六、使用 LoRA 对 大模型进行 推理，如何进行加载？<br>
推理有两个方案<br>
if resume_from_checkpoint:<br>
    lora_weight = torch.load(ckpt_name)<br>
    set_peft_model_state_dict(model, lora_weight)<br>
train_data = datasets.load_from_disk(dataset_path)<br>
class ModifiedTrainer(Trainer):<br>
    def save_model(self, output_dir=None, _internal_call=False):<br>
        # 改写trainer的save_model，在checkpoint的时候只存lora权重<br>
        from transformers.trainer import TRAINING_ARGS_NAME<br>
        os.makedirs(output_dir, exist_ok=True)<br>
        torch.save(self.args, os.path.join(output_dir, TRAINING_ARGS_NAME))<br>
        saved_params = {<br>
            k: v.to("cpu") for k, v in self.model.named_parameters() if <br>
v.requires_grad<br>
        }<br>
        torch.save(saved_params, os.path.join(output_dir, "adapter_model.bin"))<br>
        <br>
trainer = ModifiedTrainer(<br>
    model=model,<br>
    train_dataset=train_data,<br>
        args=transformers.TrainingArguments(<br>
            per_device_train_batch_size=8,<br>
            gradient_accumulation_steps=16,<br>
            num_train_epochs=10,<br>
            learning_rate=3e-4,<br>
            fp16=True,<br>
            logging_steps=10,<br>
            save_steps=200,<br>
            output_dir=output_dir<br>
        ),<br>
    data_collator=DataCollatorForSeq2Seq(<br>
        tokenizer, pad_to_multiple_of=8, return_tensors="pt", padding=True<br>
    ),<br>
)<br>
trainer.train()<br>
model.save_pretrained(train_args.output_dir)<br>
• 方案一：和训练相同，直接加入Lora层<br>
• 缺点：不过会增加推理延时因为多了lora层的计算，适合线下测评用<br>
from peft import PeftModel<br>
</p>

<h2>第 11 页</h2>

<p>from transformers import AutoModel, AutoTokenizer<br>
model = AutoModel.from_pretrained(<br>
    "THUDM/chatglm3-6b", trust_remote_code=True, load_in_8bit=True, <br>
device_map='auto'<br>
)<br>
tokenizer = AutoTokenizer.from_pretrained("THUDM/chatglm3-6b", <br>
trust_remote_code=True)<br>
model = PeftModel.from_pretrained(model, "./lora_ckpt")<br>
model.half().to(device)<br>
model.eval()<br>
• 方案二：先把lora权重和原始模型权重进行合并，把合并后的参数存储成新的bin文件，然后和加载常规模型<br>
一样加载合并后的模型参数进行推理<br>
• 优点：没有推理延时<br>
• 缺点：<br>
tokenizer = AutoTokenizer.from_pretrained("THUDM/chatglm3-6b", <br>
trust_remote_code=True)<br>
# when merging disable int8<br>
model = AutoModel.from_pretrained(<br>
    "THUDM/chatglm3-6b", load_in_8bit=False, torch_dtype=torch.float16,<br>
    trust_remote_code=True, device_map={"": "cpu"},<br>
)<br>
## 用来检查权重是否合并成功，合并成功weight会改变<br>
first_weight = model.base_model.layers[0].attention.query_key_value.weight<br>
first_weight_old = first_weight.clone()<br>
# 返回的不是新的模型，而是在原始模型上加了adapter层<br>
lora_model = PeftModel.from_pretrained(<br>
    model,<br>
    "./lora_ckpt",<br>
    device_map={"": "cpu"},<br>
    torch_dtype=torch.float16,<br>
)<br>
# 报错：A*B shape mismatch，大概率是get_peft_model错误修改了peft_config里面的<br>
fan_in_fan_out参数，某个peft的revision有这个bug<br>
lora_model = lora_model.merge_and_unload()<br>
lora_model.train(False)<br>
# 报错：大概率peft训练有问题，检查adapter.bin大小<br>
assert not torch.allclose(first_weight_old, first_weight), 'Weight Should Change <br>
after Lora Merge'<br>
# lora模型权重把原模型权重加了prefix，这里移除恢复原始key<br>
deloreanized_sd = {<br>
    k.replace("base_model.model.", ""): v<br>
    for k, v in lora_model.state_dict().items()<br>
</p>

<h2>第 12 页</h2>

<p>七、huggingface大模型如何加载多个LoRA并随时切换？<br>
    if "lora" not in k<br>
}<br>
# 保存合并后的模型权重<br>
lora_model.save_pretrained(output_dir, state_dict=deloreanized_sd)<br>
• requirement<br>
    peft&gt;=0.3.0<br>
• 用法解释<br>
1. 在加载第一个适配器时，可以通过 PeftModel.from_pretrained 方法并指定 adapter_name 参数来给它命名。<br>
否则，将使用默认的适配器名称 default，例如：<br>
    model = PeftModel.from_pretrained(model, "tloen/alpaca-lora-7b", <br>
adapter_name="eng_alpaca")<br>
2. 要加载另一个适配器，请使用 PeftModel 的 load_adapter() 方法，例如：<br>
    model.load_adapter(peft_model_path, adapter_name)<br>
3. 要切换适配器，请使用 PeftModel 的 set_adapter() 方法，例如：<br>
    model.set_adapter(adapter_name)<br>
4. 要禁用适配器，请使用上下文管理器 disable_adapter()，例如：<br>
    with model.disable_adapter()<br>
5. 特别适用于LoRA方法：要合并和卸载当前活动的适配器，以便将LoRA权重添加到基础模型权重中，并将注<br>
入的LoRA模型删除以恢复具有添加了LoRA权重的Transformers基础模型的模型，请使用<br>
merge_and_unload()方法，例如：<br>
    model = model.merge_and_unload()<br>
• 实战案例<br>
from peft import PeftModel<br>
from transformers import LlamaTokenizer, LlamaForCausalLM, GenerationConfig<br>
model_name = "decapoda-research/llama-7b-hf"<br>
tokenizer = LlamaTokenizer.from_pretrained(model_name)<br>
model = LlamaForCausalLM.from_pretrained(<br>
    model_name,<br>
    load_in_8bit=True,<br>
    device_map="auto",<br>
    use_auth_token=True<br>
)<br>
model = PeftModel.from_pretrained(model, "tloen/alpaca-lora-7b", <br>
adapter_name="eng_alpaca")<br>
model.load_adapter("22h/cabrita-lora-v0-1", adapter_name="portuguese_alpaca")<br>
model.set_adapter("eng_alpaca")<br>
</p>

<h2>第 13 页</h2>

<p>instruction = "Tell me about alpacas."<br>
print(evaluate(instruction))<br>
"""output<br>
The alpaca (Vicugna pacos) is a domesticated species of South American camelid. It <br>
resembles a small llama in appearance, but unlike the llama, it is not used as a <br>
beast of burden. It is kept primarily for its fiber, which can be spun into yarn. <br>
Alpaca fiber is warmer, lighter, and softer than sheep's wool, and is highly valued <br>
in the textile industry. The fiber comes in a variety of natural colors, including <br>
white, beige, cream, and fawn. It can also be dyed in a wide range of colors.<br>
Alpaca herds can be found in the highlands of Peru, Bolivia, Chile, Ecuador, and <br>
Colombia. They are also raised in the United States, Canada, Australia, New <br>
Zealand, and Europe. The animals graze on grasses, herbs, and shrubs, and can <br>
survive in temperatures as low as -30°F (-34°C). They are social animals, living <br>
in herds of up to 20 individuals.<br>
The fiber of the alpaka is used to make clothing<br>
"""<br>
model.set_adapter("portuguese_alpaca")<br>
instruction = "Invente uma desculpa criativa pra dizer que não preciso ir à festa."<br>
print(evaluate(instruction))<br>
"""output<br>
"Eu preciso ficar em casa para cuidar de meu gato."<br>
"""<br>
with model.disable_adapter():<br>
    instruction = "Invente uma desculpa criativa pra dizer que não preciso ir à <br>
festa."<br>
    print(evaluate(instruction))<br>
"""output<br>
I'm sorry, but I can't go to the party. I'm sick. I have a cold. I don't feel well. <br>
I need to stay at home and rest.<br>
I have a lot of homework to do. My dog ate my homework. My homework is too hard. I <br>
didn't have time to do it. It's too late. I forgot about it.<br>
My parents won't let me go. My parents are out of town. They're on vacation. They <br>
have to work. They are sick. They need to take care of my brother.<br>
They're not home. They went to the grocery store. They took the car to the <br>
mechanic. They had to go to a meeting. They were in a hurry. They forgot about me.<br>
Their car broke down. Their car ran out of gas. They got a flat tire. They couldn't <br>
find a parking space. They didn' t have enough money. They lost their wallet.<br>
It's raining. The roads are icy. There's a blizzard. There are too many cars on the <br>
road. There was an accident.<br>
"""<br>
</p>

<h2>第 14 页</h2>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:55</p>
        </div>
    </div>
</body>
</html>