<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>90-命名实体识别常见面试篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>90-命名实体识别常见面试篇</h1>
        <h2>第 1 页</h2>

<p>命名实体识别常见面试篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月12日 06:39<br>
一、CRF 常见面试题<br>
1.1 什么是CRF？CRF的主要思想是什么？<br>
设 X 与 Y 是随机变量，P(Y|X) 是给定条件 X 的条件下 Y 的条件概率分布，若随机变量 Y 构成一<br>
个由无向图G=(V,E)表示的马尔科夫随机场。则称 条件概率分布P(X|Y)为条件随机场。<br>
CRF 的 主要思想统计全局概率，在做归一化时，考虑了数据在全局的分布。<br>
1.2 CRF的三个基本问题是什么？<br>
• 命名实体识别常见面试篇<br>
• 一、CRF 常见面试题<br>
• 1.1 什么是CRF？CRF的主要思想是什么？<br>
• 1.2 CRF的三个基本问题是什么？<br>
• 1.3 线性链条件随机场的参数化形式？<br>
• 1.4 CRF的优缺点是什么？<br>
• 1.5 HMM与CRF的区别？<br>
• 1.6 生成模型与判别模型的区别？<br>
• 二、HMM 常见面试题<br>
• 2.1 什么是马尔科夫过程？<br>
• 2.2 马尔科夫过程的核心思想是什么？<br>
• 2.3 隐马尔可夫算法中的两个假设是什么？<br>
• 2.4 隐马尔可夫模型三个基本问题是什么？<br>
• 2.5 隐马尔可夫模型三个基本问题的联系？<br>
• 2.6 隐马尔可夫算法存在哪些问题？<br>
• 致谢<br>
• 定义：给定 观测序列 x 和 状态序列 y， 计算概率 P(y|x)<br>
• 解决方法：前向计算、后向计算<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>1.3 线性链条件随机场的参数化形式？<br>
在随机变量 X 取值为 x 的条件下，随机变量 Y 取值为 y 的条件概率如下：<br>
1.4 CRF的优缺点是什么？<br>
1.5 HMM与CRF的区别？<br>
共性：都常用来做序列标注的建模，像词性标注。<br>
HMM是有向图，CRF是无向图。<br>
HMM 只使用了局部特征（齐次马尔科夫假设和观测独立性假设），只能找到局部最优解；CRF使<br>
用了全局特征（在所有特征进行全局归一化），可以得到全局的最优值。<br>
隐马尔可夫模型（HMM）是描述两个序列联合分布 P(I, O) 的概率模型；条件随机场模型（CRF）<br>
是给定观测状态O的条件下预测状态序列 I 的 P（I/O）的条件概率模型。<br>
HMM是生成模型，CRF是判别模型。<br>
CRF包含HMM，或者说HMM是CRF的一种特殊情况。<br>
• 学习计算问题<br>
• 定义：给定训练数据集估计条件随机场模型参数的问题，即条件随机场的学习问题。<br>
• 公式定义：利用极大似然的方法来定义目标函数<br>
• 解决方法：随机梯度法、牛顿法、拟牛顿法、迭代尺度法这些优化方法来求解得<br>
到参数。<br>
• 目标：解耦 模型定义，目标函数，优化方法<br>
• 预测问题<br>
• 定义：给定条件随机场 P(Y|X) 和输入序列（观测序列） x ，求条件概率最大的输出序列<br>
（标记序列） y* ，即对观测序列进行标注。<br>
• 方法：维特比算法<br>
• Z(x)：是规范化因子，求和是在所有可能得输出序列上进行的。<br>
• t_{k}：是定义在边上的特征函数，称为转移特征，依赖于当前和前一个位置。<br>
• s_{l}：是定义在结点上的特征函数，称为状态特征，依赖于当前位置。<br>
• 优点：<br>
• 为每个位置进行标注过程中可利用丰富的内部及上下文特征信息；<br>
• CRF模型在结合多种特征方面的存在优势；<br>
• 避免了标记偏置问题；<br>
• CRF的性能更好，对特征的融合能力更强；<br>
• 缺点：<br>
• 训练模型的时间比ME更长，且获得的模型非常大。在一般的PC机上可能无法执行；<br>
• 特征的选择和优化是影响结果的关键因素。特征选择问题的好与坏，直接决定了系统性<br>
能的高低<br>
</p>

<h2>第 3 页</h2>

<p>1.6 生成模型与判别模型的区别？<br>
生成模型：学习得到联合概率分布P(x,y)，即特征x，共同出现的概率<br>
常见的生成模型：朴素贝叶斯模型，混合高斯模型，HMM模型。<br>
判别模型：学习得到条件概率分布P(y|x)，即在特征x出现的情况下标记y出现的概率。<br>
常见的判别模型：感知机，决策树，逻辑回归，SVM，CRF等。<br>
判别式模型：要确定一个羊是山羊还是绵羊，用判别式模型的方法是从历史数据中学习到模型，然<br>
后通过提取这只羊的特征来预测出这只羊是山羊的概率，是绵羊的概率。<br>
生成式模型：是根据山羊的特征首先学习出一个山羊的模型，然后根据绵羊的特征学习出一个绵羊<br>
的模型，然后从这只羊中提取特征，放到山羊模型中看概率是多少，再放到绵羊模型中看概率是多<br>
少，哪个大就是哪个。<br>
二、HMM 常见面试题<br>
2.1 什么是马尔科夫过程？<br>
假设一个随机过程中，t_n 时刻的状态 x_n 的条件发布，只与其前一状态x_(n-1) 相关，即：<br>
则将其称为 马尔可夫过程。<br>
2.2 马尔科夫过程的核心思想是什么？<br>
对于马尔可夫过程的思想，用一句话去概括：当前时刻状态仅与上一时刻状态相关，与其他时刻不<br>
相关。<br>
可以从 马尔可夫过程图去理解，由于每个状态间是以有向直线连接，也就是当前时刻状态仅与上<br>
一时刻状态相关。<br>
2.3 隐马尔可夫算法中的两个假设是什么？<br>
齐次马尔可夫性假设：即假设隐藏的马尔科夫链在任意时刻 t 的状态只依赖于其前一时刻的状态，<br>
与其他时刻的状态及观测无关，也与时刻 t 无关；<br>
观测独立性假设：即假设任意时刻的观测只依赖于该时刻的马尔科夫链的状态，与其他观测及状态<br>
无关。<br>
2.4 隐马尔可夫模型三个基本问题是什么？<br>
</p>

<h2>第 4 页</h2>

<p>2.5 隐马尔可夫模型三个基本问题的联系？<br>
三个基本问题 存在 渐进关系。首先，要学会用前向算法和后向算法算观测序列出现的概率，然后<br>
用Baum-Welch算法求参数的时候，某些步骤是需要用到前向算法和后向算法的，计算得到参数<br>
后，我们就可以用来做预测了。因此可以看到，三个基本问题，它们是渐进的，解决NLP问题，应<br>
用HMM模型做解码任务应该是最终的目的。<br>
2.6 隐马尔可夫算法存在哪些问题？<br>
因为HMM模型其实它简化了很多问题，做了某些很强的假设，如齐次马尔可夫性假设和观测独立<br>
性假设，做了假设的好处是，简化求解的难度，坏处是对真实情况的建模能力变弱了。<br>
在序列标注问题中，隐状态（标注）不仅和单个观测状态相关，还和观察序列的长度、上下文等信<br>
息相关。例如词性标注问题中，一个词被标注为动词还是名词，不仅与它本身以及它前一个词的标<br>
注有关，还依赖于上下文中的其他词。可以使用最大熵马尔科夫模型进行优化。<br>
知识星球<br>
• （1）概率计算问题：给定模型(A,B,\pi)和观测序列，计算在模型下观测序列出现的概率。(直接<br>
计算法理论可行，但计算复杂度太大（O(N^2T)）；用前向与后向计算法)<br>
• （2）学习问题：已知观测序列，估计模型参数，使得在该模型下观测序列概率最大。（极大似<br>
然估计的方法来估计参数，Baum-Welch算法（EM算法））<br>
• （3）预测问题，也称为解码问题：已知模型和观测序列，求对给定观测序列条件概率最大的状<br>
态序列。（维特比算法，动态规划，核心：边计算边删掉不可能是答案的路径，在最后剩下的<br>
路径中挑选最优路径）<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:57</p>
        </div>
    </div>
</body>
</html>