<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>34-基于lora的llama2二次预训练</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>34-基于lora的llama2二次预训练</h1>
        <h2>第 1 页</h2>

<p>基于lora的llama2二次预训练<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 20:47<br>
一、为什么需要 对 llama2 做 基于lora的二次预训练?<br>
加入中文训练语料进行llama2的二次预训练，这样模型就可以增加支持中文输出的能力。<br>
二、基于lora的llama2二次预训练 的目标是什么？<br>
在保持预训练模型权重不变的情况下，通过添加额外的网络层并仅训练这些新增的网络层参数，实现大模型的高<br>
效微调（peft）。<br>
三、基于lora的llama2二次预训练 的思想是什么？<br>
思想：基于对模型本征维度（intrinsic dimension）的理解。<br>
“本征维度”是指模型中真正有用的、能够影响模型输出的参数数量。<br>
Aghajanyan研究发现，预训练模型的内在维度实际上非常小，即只有一小部分参数对模型输出有显著影响。就是<br>
存在一个极低维度的参数，微调它和在全参数空间中微调能起到相同的效果<br>
LORA假设模型在任务适配过程中权重的改变量是低秩（low rank）<br>
W=W0+ΔW，ΔW=BA<br>
参数更新范围：只训练新增的网络层参数<br>
四、基于lora的llama2二次预训练 语料构建思路？<br>
本项目 基于lora的llama2二次预训练 语料 来自中文书籍，一个 中文书籍收录整理 项目。<br>
• 基于lora的llama2二次预训练<br>
• 一、为什么需要 对 llama2 做 基于lora的二次预训练?<br>
• 二、基于lora的llama2二次预训练 的目标是什么？<br>
• 三、基于lora的llama2二次预训练 的思想是什么？<br>
• 四、基于lora的llama2二次预训练 语料构建思路？<br>
• 五、如何 基于lora的llama2二次预训练 ？<br>
• 5.1 基于lora的llama2二次预训练 参数介绍<br>
• 5.2 基于lora的llama2二次预训练<br>
• 六、如何 基于lora的llama2 微调 ？<br>
• 6.1 训练数据介绍<br>
• 6.2 基于lora的llama2 微调 参数介绍<br>
• 6.3 基于lora的llama2 微调<br>
• 七、如何 使用 基于lora的llama2 做推理 ？<br>
• 致谢<br>
1. 预训练 数据集 下载<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>介绍：数据集格式，.txt结尾<br>
    $ git clone https://github.com/shjwudp/shu.git<br>
1. 数据集格式介绍<br>
1. 数据集介绍<br>
《红楼梦》<br>
曹雪芹 　高鄂  著<br>
</p>

<h2>第 3 页</h2>

<p>第一回  甄士隐梦幻识通灵　贾雨村风尘怀闺秀<br>
列位看官：你道此书从何而来？说起根由，虽近荒唐，细按则深有趣味。待在下将此来历注<br>
明，方使阅者了然不惑。<br>
原来女娲氏炼石补天之时，于大荒山无稽崖炼成高经十二丈、方经二十四丈顽石三万六千五百<br>
零一块。娲皇氏只用了三万六千五百块，只单单剩了一块未用，便弃在此山青埂峰下。谁知此<br>
石自经煅炼之后，灵性已通，因见众石俱得补天，独自己无材不堪入选，遂自怨自叹，日夜悲<br>
号惭愧。<br>
一日，正当嗟悼之际，俄见一僧一道远远而来，生得骨格不凡，丰神迥别，说说笑笑，来至峰<br>
下，坐于石边，高谈快论：先是说些云山雾海、神仙玄幻之事，后便说到红尘中荣华富贵。此<br>
石听了，不觉打动凡心，也想要到人间去享一享这荣华富贵，但自恨粗蠢，不得已，便口吐人<br>
言，向那僧道说道：“大师，弟子蠢物，不能见礼了！适闻二位谈那人世间荣耀繁华，心切慕<br>
之。弟子质虽粗蠢，性却稍通，况见二师仙形道体，定非凡品，必有补天济世之材，利物济人<br>
之德。如蒙发一点慈心，携带弟子得入红尘，在那富贵场中，温柔乡里受享几年，自当永佩洪<br>
恩，万劫不忘也！”二仙师听毕，齐憨笑道：“善哉，善哉！那红尘中有却有些乐事，但不能<br>
永远依恃；况又有‘美中不足，好事多磨’八个字紧相连属，瞬息间则又乐极悲生，人非物<br>
换，究竟是到头一梦，万境归空，倒不如不去的好。”这石凡心已炽，那里听得进这话去，乃<br>
复苦求再四。二仙知不可强制，乃叹道：“此亦静极思动，无中生有之数也！既如此，我们便<br>
携你去受享受享，只是到不得意时，切莫后悔！”石道：“自然，自然。”那僧又道：“若说<br>
你性灵，却又如此质蠢，并更无奇贵之处。如此也只好踮脚而已。也罢！我如今大施佛法，助<br>
你助，待劫终之日，复还本质，以了此案。你道好否？”石头听了，感谢不尽。那僧便念咒书<br>
符，大展幻术，将一块大石登时变成一块鲜明莹洁的美玉，且又缩成扇坠大小的可佩可拿。那<br>
僧托于掌上，笑道：“形体倒也是个宝物了！还只没有实在的好处，须得再镌上数字，使人一<br>
见便知是奇物方妙。然后好携你到那昌明隆盛之邦、诗礼簪缨之族、花柳繁华地、温柔富贵乡<br>
去安身乐业。”石头听了，喜不能禁，乃问：“不知赐了弟子那哪几件奇处？又不知携了弟子<br>
到何地方？望乞明示，使弟子不惑。”那僧笑道：“你且莫问，日后自然明白的。”说着，便<br>
袖了这石，同那道人飘然而去，竟不知投奔何方何舍。<br>
后来，不知过了几世几劫，因有个空空道人访道求仙，从这大荒山无稽崖青埂峰下经过，忽见<br>
一大块石上字迹分明，编述历历。空空道人乃从头一看，原来就是无材补天，幻形入世，蒙茫<br>
茫大士、渺渺真人携入红尘，历尽离合悲欢、炎凉世态的一段故事。后面又有一首偈云：<br>
无材可去补苍天，枉入红尘若许年。此系身前身后事，倩谁记去作奇传？<br>
诗后便是此石坠落之乡，投胎之处，亲自经历的一段陈迹故事。其中家庭闺阁琐事，以及闲情<br>
诗词倒还全备，或可适趣解闷；然朝代年纪、地舆邦国却反失落无考。<br>
空空道人遂向石头说道：“石兄，你这一段故事，据你自己说有些趣味，故编写在此，意欲问<br>
世传奇。据我看来：第一件，无朝代年纪可考；第二件，并无大贤大忠理朝廷、治风俗的善<br>
政，其中只不过几个异样女子，或情或痴，或小才微善，亦无班姑、蔡女之德能。我纵抄去，<br>
恐世人不爱看呢！”石头笑答道：“我师何太痴耶！若云无朝代可考，今我师竟借汉、唐等年<br>
纪添缀，又有何难？但我想，历来野史，皆蹈一辙，莫如我这不借此套者，反倒新奇别致。不<br>
过只取其事体情理罢了，又何必拘拘于朝代年纪哉！再者，市井俗人喜看理治之书者甚少，爱<br>
适趣闲文者特多。历来野史，或讪谤君相，或贬人妻女，奸淫凶恶，不可胜数。更有一种风月<br>
</p>

<h2>第 4 页</h2>

<p>红楼梦.txt<br>
五、如何 基于lora的llama2二次预训练 ？<br>
5.1 基于lora的llama2二次预训练 参数介绍<br>
笔墨，其淫秽污臭，屠毒笔墨，坏人子弟，又不可胜数。至若佳人才子等书，则又千部共出一<br>
套，且其中终不能不涉于淫滥，以致满纸潘安、子建、西子、文君。不过作者要写出自己的那<br>
两首情诗艳赋来，故假拟出男女二人名姓，又必旁出一小人其间拨乱，亦如剧中之小丑然。且<br>
鬟婢开口即者也之乎，非文即理。故逐一看去，悉皆自相矛盾、大不近情理之话，竟不如我半<br>
世亲睹亲闻的这几个女子，虽不敢说强似前代书中所有之人，但事迹原委，亦可以消愁破闷；<br>
也有几首歪诗熟话，可以喷饭供酒。至若离合悲欢，兴衰际遇，则又追踪蹑迹，不敢稍加穿<br>
凿，徒为供人之目而反失其真传者。今之人，贫者日为衣食所累，富者又怀不足之心；纵然一<br>
时稍闲，又有贪淫恋色、好货寻愁之事，哪里有工夫去看那理治之书！所以，我这一段故事，<br>
也不愿世人称奇道妙，也不定要世人喜悦检读，只愿他们当那醉淫饱卧之时，或避世去愁之<br>
际，把此一玩，岂不省了些寿命筋力？就比那谋虚逐妄，却也省了口舌是非之害、腿脚奔忙之<br>
苦。再者，亦令世人换新眼目，不比那些胡牵乱扯，忽离忽遇，满纸才人淑女、子建、文君、<br>
红娘、小玉等通共熟套之旧稿。我师意为何如？”<br>
空空道人听如此说，思忖半晌，将一这《石头记》再检阅一遍，因见上面虽有些指奸责佞、贬<br>
恶诛邪之语，亦非伤时骂世之旨；及至君仁臣良、父慈子孝，凡伦常所关之处，皆是称功颂<br>
德，眷眷无穷，实非别书之可比。虽其中大旨谈情，亦不过实录其事，又非假拟妄称，一味淫<br>
邀艳约，私订偷盟之可比。因毫不干涉时世，方从头至尾抄录回来，问世传奇。因空见色，由<br>
色生情，传情入色，自色悟空，空空道人遂易名为情僧，改《石头记》为《情僧录》。至?玉峰<br>
题曰《红楼梦》。东鲁孔梅溪则题曰《风月宝鉴》。后因曹雪芹于悼红轩中，披阅十载，增删<br>
五次，纂成目录，分出章回，则题曰《金陵十二钗》，并题一绝云：<br>
满纸荒唐言，一把辛酸泪！都云作者痴，谁解其中味？<br>
至脂砚斋甲戌抄阅再评，仍用《石头记》。<br>
出则既明，且看石上是何故事。按那石上书云：<br>
...<br>
• 实现代码：run_clm_pt_with_peft.py<br>
1. 预训练模型参数<br>
@dataclass<br>
class ModelArguments:<br>
    """<br>
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune, <br>
or train from scratch.<br>
    """<br>
    model_name_or_path: Optional[str] = field(<br>
</p>

<h2>第 5 页</h2>

<p>        default=None,<br>
        metadata={<br>
            "help": (<br>
                "The model checkpoint for weights initialization.Don't set if you <br>
want to train a model from scratch."<br>
            )<br>
        },<br>
    )<br>
    tokenizer_name_or_path: Optional[str] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "The tokenizer for weights initialization.Don't set if you want to <br>
train a model from scratch."<br>
            )<br>
        },<br>
    )<br>
    model_type: Optional[str] = field(<br>
        default=None,<br>
        metadata={"help": "If training from scratch, pass a model type from the <br>
list: " + ", ".join(MODEL_TYPES)},<br>
    )<br>
    config_overrides: Optional[str] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "Override some existing default config settings when a model is <br>
trained from scratch. Example: "<br>
                <br>
"n_embd=10,resid_pdrop=0.2,scale_attn_weights=false,summary_type=cls_index"<br>
            )<br>
        },<br>
    )<br>
    config_name: Optional[str] = field(<br>
        default=None, metadata={"help": "Pretrained config name or path if not the <br>
same as model_name"}<br>
    )<br>
    tokenizer_name: Optional[str] = field(<br>
        default=None, metadata={"help": "Pretrained tokenizer name or path if not <br>
the same as model_name"}<br>
    )<br>
    cache_dir: Optional[str] = field(<br>
        default=None,<br>
        metadata={"help": "Where do you want to store the pretrained models <br>
downloaded from huggingface.co"},<br>
    )<br>
    use_fast_tokenizer: bool = field(<br>
</p>

<h2>第 6 页</h2>

<p>        default=True,<br>
        metadata={"help": "Whether to use one of the fast tokenizer (backed by the <br>
tokenizers library) or not."},<br>
    )<br>
    model_revision: str = field(<br>
        default="main",<br>
        metadata={"help": "The specific model version to use (can be a branch name, <br>
tag name or commit id)."},<br>
    )<br>
    use_auth_token: bool = field(<br>
        default=False,<br>
        metadata={<br>
            "help": (<br>
                "Will use the token generated when running `huggingface-cli login` <br>
(necessary to use this script "<br>
                "with private models)."<br>
            )<br>
        },<br>
    )<br>
    torch_dtype: Optional[str] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "Override the default `torch.dtype` and load the model under this <br>
dtype. If `auto` is passed, the "<br>
                "dtype will be automatically derived from the model's weights."<br>
            ),<br>
            "choices": ["auto", "bfloat16", "float16", "float32"],<br>
        },<br>
    )<br>
    def __post_init__(self):<br>
        if self.config_overrides is not None and (self.config_name is not None or <br>
self.model_name_or_path is not None):<br>
            raise ValueError(<br>
                "--config_overrides can't be used in combination with --config_name <br>
or --model_name_or_path"<br>
            )<br>
• 关键参数介绍：<br>
• model_name_or_path：预训练模型地址<br>
• tokenizer_name_or_path：：预训练模型 tokenizer 地址<br>
• model_type：大模型类型<br>
</p>

<h2>第 7 页</h2>

<p>1. 预训练 数据参数介绍<br>
@dataclass<br>
class DataTrainingArguments:<br>
    """<br>
    Arguments pertaining to what data we are going to input our model for training <br>
and eval.<br>
    """<br>
    dataset_dir: Optional[str] = field(<br>
        default=None, metadata={"help": "The name of the dataset to use (via the <br>
datasets library)."}<br>
    )<br>
    dataset_config_name: Optional[str] = field(<br>
        default=None, metadata={"help": "The configuration name of the dataset to <br>
use (via the datasets library)."}<br>
    )<br>
    train_file: Optional[str] = field(default=None, metadata={"help": "The input <br>
training data file (a text file)."})<br>
    validation_file: Optional[str] = field(<br>
        default=None,<br>
        metadata={"help": "An optional input evaluation data file to evaluate the <br>
perplexity on (a text file)."},<br>
    )<br>
    max_train_samples: Optional[int] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "For debugging purposes or quicker training, truncate the number of <br>
training examples to this "<br>
                "value if set."<br>
            )<br>
        },<br>
    )<br>
    max_eval_samples: Optional[int] = field(<br>
        default=None,<br>
</p>

<h2>第 8 页</h2>

<p>        metadata={<br>
            "help": (<br>
                "For debugging purposes or quicker training, truncate the number of <br>
evaluation examples to this "<br>
                "value if set."<br>
            )<br>
        },<br>
    )<br>
    streaming: bool = field(default=False, metadata={"help": "Enable streaming <br>
mode"})<br>
    block_size: Optional[int] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "Optional input sequence length after tokenization. "<br>
                "The training dataset will be truncated in block of this size for <br>
training. "<br>
                "Default to the model max input length for single sentence inputs <br>
(take into account special tokens)."<br>
            )<br>
        },<br>
    )<br>
    overwrite_cache: bool = field(<br>
        default=False, metadata={"help": "Overwrite the cached training and <br>
evaluation sets"}<br>
    )<br>
    validation_split_percentage: Optional[float] = field(<br>
        default=0.05,<br>
        metadata={<br>
            "help": "The percentage of the train set used as validation set in case <br>
there's no validation split"<br>
        },<br>
    )<br>
    preprocessing_num_workers: Optional[int] = field(<br>
        default=None,<br>
        metadata={"help": "The number of processes to use for the preprocessing."},<br>
    )<br>
    keep_linebreaks: bool = field(<br>
        default=True, metadata={"help": "Whether to keep line breaks when using TXT <br>
files or not."}<br>
    )<br>
    data_cache_dir: Optional[str] = field(default="./", metadata={"help": "The <br>
datasets processed stored"})<br>
    def __post_init__(self):<br>
        if self.streaming:<br>
</p>

<h2>第 9 页</h2>

<p>5.2 基于lora的llama2二次预训练<br>
            require_version("datasets&gt;=2.0.0", "The streaming feature requires <br>
`datasets&gt;=2.0.0`")<br>
2. 预训练 模型参数介绍<br>
@dataclass<br>
class MyTrainingArguments(TrainingArguments):<br>
    trainable : Optional[str] = field(default="q_proj,v_proj")<br>
    lora_rank : Optional[int] = field(default=8)<br>
    lora_dropout : Optional[float] = field(default=0.1)<br>
    lora_alpha : Optional[float] = field(default=32.)<br>
    modules_to_save : Optional[str] = field(default=None)<br>
    debug_mode : Optional[bool] = field(default=False)<br>
    peft_path : Optional[str] = field(default=None)<br>
    flash_attn : Optional[bool] = field(default=False)<br>
    double_quant: Optional[bool] = field(default=True)<br>
    quant_type: Optional[str] = field(default="nf4")<br>
    load_in_kbits: Optional[int] = field(default=16)<br>
########参数设置########<br>
lr=2e-4  # 学习率<br>
lora_rank=64 # LoRA低秩矩阵的维数<br>
lora_alpha=128 #  LoRA低秩矩阵的缩放系数，为一个常数超参，调整alpha与调整学习率类似<br>
lora_trainable="q_proj,v_proj,k_proj,o_proj,gate_proj,down_proj,up_proj" # 可训练的 <br>
LORA 模块，q_proj、k_proj和v_proj是多头注意力机制中的三个线性变换，用于将输入的<br>
token映射到一个高维向量空间中，以便于模型对输入进行处理；o_proj则是多头注意力机制的<br>
输出层，它将模型的输出映射到一个概率分布上，以便于模型预测下一个token；gate_proj、<br>
down_proj和up_proj则是在LoRA微调方法中使用的一些层<br>
modules_to_save="embed_tokens,lm_head" # 需要保存的模块，embed_tokens层将输入的<br>
token映射到一个高维向量空间中，以便于模型对输入进行处理。lm_head层则是预测下一个<br>
token的输出层，它将模型的输出映射到一个概率分布上，以便于模型预测下一个token<br>
lora_dropout=0.05 # LoRA 层的丢弃（dropout）率，取值范围为[0, 1)<br>
pretrained_model=/root/llama/all_transformer # 预训练模型路径<br>
chinese_tokenizer_path=/root/llama/all_transformer # 中文分词器路径<br>
dataset_dir=/root/llama/data # 数据集路径<br>
data_cache=./cache/ # 数据缓存路径<br>
per_device_train_batch_size=1 # 每个设备上的训练批次大小<br>
gradient_accumulation_steps=1 # 梯度累积步数<br>
output_dir=output_dir # 输出目录路径<br>
block_size=512 # 设置最大序列长度为512，超过这个长度的序列将被截断或填充<br>
# resume_from=output_dir/checkpoint-24000 # 从哪个检查点恢复训练<br>
training_steps=25000<br>
</p>

<h2>第 10 页</h2>

<p>deepspeed_config_file=scripts/training/ds_zero2_no_offload.json<br>
########启动命令########<br>
torchrun --nnodes 1 --nproc_per_node 1 scripts/training/run_clm_pt_with_peft.py \<br>
    --deepspeed ${deepspeed_config_file} \<br>
    --model_name_or_path ${pretrained_model} \<br>
    --tokenizer_name_or_path ${chinese_tokenizer_path} \<br>
    --dataset_dir ${dataset_dir} \<br>
    --data_cache_dir ${data_cache} \<br>
    --validation_split_percentage 0.001 \<br>
    --per_device_train_batch_size ${per_device_train_batch_size} \<br>
    --do_train \<br>
    --seed $RANDOM \<br>
    --fp16 \<br>
    --max_steps ${training_steps} \<br>
    --num_train_epochs 1 \<br>
    --lr_scheduler_type cosine \<br>
    --learning_rate ${lr} \<br>
    --warmup_ratio 0.05 \<br>
    --weight_decay 0.01 \<br>
    --logging_strategy steps \<br>
    --logging_steps 10 \<br>
    --save_strategy steps \<br>
    --save_total_limit 3 \<br>
    --save_steps 500 \<br>
    --gradient_accumulation_steps ${gradient_accumulation_steps} \<br>
    --preprocessing_num_workers 8 \<br>
    --block_size ${block_size} \<br>
    --output_dir ${output_dir} \<br>
    --overwrite_output_dir \<br>
    --ddp_timeout 30000 \<br>
    --logging_first_step True \<br>
    --lora_rank ${lora_rank} \<br>
    --lora_alpha ${lora_alpha} \<br>
    --trainable ${lora_trainable} \<br>
    --modules_to_save ${modules_to_save} \<br>
    --lora_dropout ${lora_dropout} \<br>
    --torch_dtype float16 \<br>
    --resume True \<br>
    --resume_from_checkpoint ${resume_from} \<br>
    --gradient_checkpointing \<br>
    --ddp_find_unused_parameters False<br>
</p>

<h2>第 11 页</h2>

<p>基于lora的llama2二次预训练 显存占用<br>
基于lora的llama2二次预训练 模型<br>
六、如何 基于lora的llama2 微调 ？<br>
6.1 训练数据介绍<br>
• 实现代码：run_clm_sft_with_peft.py<br>
</p>

<h2>第 12 页</h2>

<p>本次 基于lora的llama2 Lora 微调 这样使用 standford 开源的的stanford_alpaca项目，该项目 提供了廉价的对<br>
llama模型微调方法——利用openai提供的gpt模型api生成质量较高的instruct tuning数据（仅52k），并且基于这<br>
些数据微调模型。<br>
在prompt设计上，精调以及预测时采用的都是原版Stanford Alpaca不带input的模版。对于包含input字段的数<br>
据，采用f"{instruction}+\n+{input}"的形式进行拼接。<br>
但是由于 该数据集是 英文的，所以 使用Chinese-LLaMA-Alpaca开源的中文Alpaca数据，该数据集包含51k个从<br>
ChatGPT (gpt-3.5-turbo)爬取的指令数据。<br>
6.2 基于lora的llama2 微调 参数介绍<br>
[<br>
  {<br>
    "instruction": "我们如何在日常生活中减少用水？",<br>
    "input": "",<br>
    "output": "1. 使用节水装置，如节水淋浴喷头和水龙头。 \n2. 使用水箱或水桶收集家<br>
庭废水，例如洗碗和洗浴。 \n3. 在社区中提高节水意识。 \n4. 检查水管和灌溉系统的漏水<br>
情况，并及时修复它们。 \n5. 洗澡时间缩短，使用低流量淋浴头节约用水。 \n6. 收集雨<br>
水，用于园艺或其他非饮用目的。 \n7. 刷牙或擦手时关掉水龙头。 \n8. 减少浇水草坪的时<br>
间。 \n9. 尽可能多地重复使用灰水（来自洗衣机、浴室水槽和淋浴的水）。 \n10. 只购买能<br>
源效率高的洗碗机和洗衣机。"<br>
  },<br>
  {<br>
    "instruction": "编辑文章，使其更吸引读者。",<br>
    "input": "自主机器人是计算机控制的机器，被编程执行特定任务而不需要任何人类输<br>
入。自主机器人在各个行业中被越来越广泛地应用，从制造业到医疗保健再到安全。",<br>
    "output": "自主机器人是计算机控制的机器，被编程执行特定任务而不需要任何人类输<br>
入，从而实现了新的效率、精确度和可靠性水平。自主机器人在各个行业中被越来越广泛地应<br>
用，从制造业，它们可以使用精度和一致的质量组装复杂的组件，到医疗保健，可以协助进行<br>
医疗测试和处理，再到安全，可以监控大面积地区，保障人们和财产的安全。自主机器人还可<br>
以减少在危险或有害环境中的错误和增加安全，在工业流程的检查或维修期间等。由于其多样<br>
性，自主机器人将彻底改变我们工作方式的方式，使任务变得更加简单、快速，最终更加愉<br>
悦。"<br>
  },<br>
  ...<br>
]<br>
1. 基于lora的llama2 微调 大模型路径参数<br>
@dataclass<br>
class ModelArguments:<br>
    """<br>
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune, <br>
or train from scratch.<br>
    """<br>
    model_name_or_path: Optional[str] = field(<br>
        default=None,<br>
        metadata={<br>
</p>

<h2>第 13 页</h2>

<p>            "help": (<br>
                "The model checkpoint for weights initialization.Don't set if you <br>
want to train a model from scratch."<br>
            )<br>
        },<br>
    )<br>
    tokenizer_name_or_path: Optional[str] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "The tokenizer for weights initialization.Don't set if you want to <br>
train a model from scratch."<br>
            )<br>
        },<br>
    )<br>
    config_overrides: Optional[str] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "Override some existing default config settings when a model is <br>
trained from scratch. Example: "<br>
                <br>
"n_embd=10,resid_pdrop=0.2,scale_attn_weights=false,summary_type=cls_index"<br>
            )<br>
        },<br>
    )<br>
    config_name: Optional[str] = field(<br>
        default=None, metadata={"help": "Pretrained config name or path if not the <br>
same as model_name"}<br>
    )<br>
    tokenizer_name: Optional[str] = field(<br>
        default=None, metadata={"help": "Pretrained tokenizer name or path if not <br>
the same as model_name"}<br>
    )<br>
    cache_dir: Optional[str] = field(<br>
        default=None,<br>
        metadata={"help": "Where do you want to store the pretrained models <br>
downloaded from huggingface.co"},<br>
    )<br>
    use_fast_tokenizer: bool = field(<br>
        default=True,<br>
        metadata={"help": "Whether to use one of the fast tokenizer (backed by the <br>
tokenizers library) or not."},<br>
    )<br>
    model_revision: str = field(<br>
        default="main",<br>
</p>

<h2>第 14 页</h2>

<p>        metadata={"help": "The specific model version to use (can be a branch name, <br>
tag name or commit id)."},<br>
    )<br>
    use_auth_token: bool = field(<br>
        default=False,<br>
        metadata={<br>
            "help": (<br>
                "Will use the token generated when running `huggingface-cli login` <br>
(necessary to use this script "<br>
                "with private models)."<br>
            )<br>
        },<br>
    )<br>
    torch_dtype: Optional[str] = field(<br>
        default=None,<br>
        metadata={<br>
            "help": (<br>
                "Override the default `torch.dtype` and load the model under this <br>
dtype. If `auto` is passed, the "<br>
                "dtype will be automatically derived from the model's weights."<br>
            ),<br>
            "choices": ["auto", "bfloat16", "float16", "float32"],<br>
        },<br>
    )<br>
    def __post_init__(self):<br>
        if self.config_overrides is not None and (self.config_name is not None or <br>
self.model_name_or_path is not None):<br>
            raise ValueError(<br>
                "--config_overrides can't be used in combination with --config_name <br>
or --model_name_or_path"<br>
            )<br>
• 关键参数介绍：<br>
• model_name_or_path：预训练模型地址<br>
• tokenizer_name_or_path：：预训练模型 tokenizer 地址<br>
• ...<br>
2. 基于lora的llama2 微调 数据参数介绍<br>
@dataclass<br>
class DataTrainingArguments:<br>
    """<br>
    Arguments pertaining to what data we are going to input our model for training <br>
and eval.<br>
    """<br>
    dataset_dir: Optional[str] = field(<br>
</p>

<h2>第 15 页</h2>

<p>        default=None, metadata={"help": "The name of the dataset to use (via the <br>
datasets library)."}<br>
    )<br>
    train_file: Optional[str] = field(default=None, metadata={"help": "The input <br>
training data file (a text file)."})<br>
    validation_file: Optional[str] = field(<br>
        default=None,<br>
        metadata={"help": "An optional input evaluation data file to evaluate the <br>
perplexity on (a text file)."},<br>
    )<br>
    overwrite_cache: bool = field(<br>
        default=False, metadata={"help": "Overwrite the cached training and <br>
evaluation sets"}<br>
    )<br>
    validation_split_percentage: Optional[float] = field(<br>
        default=0.05,<br>
        metadata={<br>
            "help": "The percentage of the train set used as validation set in case <br>
there's no validation split"<br>
        },<br>
    )<br>
    preprocessing_num_workers: Optional[int] = field(<br>
        default=None,<br>
        metadata={"help": "The number of processes to use for the preprocessing."},<br>
    )<br>
    keep_linebreaks: bool = field(<br>
        default=True, metadata={"help": "Whether to keep line breaks when using TXT <br>
files or not."}<br>
    )<br>
    data_cache_dir: Optional[str] = field(default=None, metadata={"help": "The <br>
datasets processed stored"})<br>
    max_seq_length: Optional[int] = field(default=1024)<br>
3. 基于lora的llama2 微调 模型参数介绍<br>
@dataclass<br>
class MyTrainingArguments(TrainingArguments):<br>
    trainable : Optional[str] = field(default="q_proj,v_proj")<br>
    lora_rank : Optional[int] = field(default=8)<br>
    lora_dropout : Optional[float] = field(default=0.1)<br>
    lora_alpha : Optional[float] = field(default=32.)<br>
    modules_to_save : Optional[str] = field(default=None)<br>
    peft_path : Optional[str] = field(default=None)<br>
    flash_attn : Optional[bool] = field(default=False)<br>
    double_quant: Optional[bool] = field(default=True)<br>
</p>

<h2>第 16 页</h2>

<p>6.3 基于lora的llama2 微调<br>
    quant_type: Optional[str] = field(default="nf4")<br>
    load_in_kbits: Optional[int] = field(default=16)<br>
lr=1e-4<br>
lora_rank=64<br>
lora_alpha=128<br>
lora_trainable="q_proj,v_proj,k_proj,o_proj,gate_proj,down_proj,up_proj"<br>
modules_to_save="embed_tokens,lm_head"<br>
lora_dropout=0.05<br>
pretrained_model=/root/llama/correspond_output_dir<br>
chinese_tokenizer_path=/root/llama/correspond_output_dir<br>
dataset_dir=data_pt<br>
per_device_train_batch_size=1<br>
per_device_eval_batch_size=1<br>
gradient_accumulation_steps=8<br>
max_seq_length=512<br>
output_dir=sft_output_dir2<br>
validation_file=data_pt/alpaca_data_zh_51k.json<br>
training_steps=6000<br>
deepspeed_config_file=scripts/training/ds_zero2_no_offload.json<br>
torchrun --nnodes 1 --nproc_per_node 7 scripts/training/run_clm_sft_with_peft.py \<br>
    --deepspeed ${deepspeed_config_file} \<br>
    --model_name_or_path ${pretrained_model} \<br>
    --tokenizer_name_or_path ${chinese_tokenizer_path} \<br>
    --dataset_dir ${dataset_dir} \<br>
    --per_device_train_batch_size ${per_device_train_batch_size} \<br>
    --per_device_eval_batch_size ${per_device_eval_batch_size} \<br>
    --do_train \<br>
    --do_eval  \<br>
    --eval_steps 1000 \<br>
    --seed $RANDOM \<br>
    --fp16 \<br>
    --num_train_epochs 1 \<br>
    --lr_scheduler_type cosine \<br>
    --learning_rate ${lr} \<br>
    --warmup_ratio 0.03 \<br>
    --weight_decay 0 \<br>
    --logging_strategy steps \<br>
    --logging_steps 10 \<br>
    --save_strategy steps \<br>
</p>

<h2>第 17 页</h2>

<p>    --save_total_limit 3 \<br>
    --evaluation_strategy steps \<br>
    --eval_steps 6000 \<br>
    --save_steps 3000 \<br>
    --gradient_accumulation_steps ${gradient_accumulation_steps} \<br>
    --preprocessing_num_workers 8 \<br>
    --max_steps ${training_steps} \<br>
    --max_seq_length ${max_seq_length} \<br>
    --output_dir ${output_dir} \<br>
    --overwrite_output_dir \<br>
    --ddp_timeout 30000 \<br>
    --logging_first_step True \<br>
    --lora_rank ${lora_rank} \<br>
    --lora_alpha ${lora_alpha} \<br>
    --trainable ${lora_trainable} \<br>
    --lora_dropout ${lora_dropout} \<br>
    --modules_to_save ${modules_to_save} \<br>
    --torch_dtype float16 \<br>
    --validation_file ${validation_file}<br>
</p>

<h2>第 18 页</h2>

<p>七、如何 使用 基于lora的llama2 做推理 ？<br>
python scripts/inference/inference_hf.py \ <br>
    --base_model  correspond_output_dir \ # 基础模型<br>
    --lora_model  sft_output_dir2/sft_lora_model \ # 如果没有设置，将在基础模型上执<br>
行推理<br>
    --tokenizer_path correspond_output_dir \ # 分词器路径<br>
    --with_prompt  # 自动用提示符包装输入<br>
</p>

<h2>第 19 页</h2>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:56</p>
        </div>
    </div>
</body>
</html>