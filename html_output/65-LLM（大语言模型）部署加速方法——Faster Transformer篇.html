<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>65-LLM（大语言模型）部署加速方法——Faster Transformer篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>65-LLM（大语言模型）部署加速方法——Faster Transformer篇</h1>
        <h2>第 1 页</h2>

<p>LLM（大语言模型）部署加速方法——Faster Transformer篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 13:01<br>
一、为什么需要 FasterTransformer？<br>
二、FasterTransformer 介绍一下？<br>
NVIDIA FasterTransformer (FT) 是一个库，用于实现基于Transformer的神经网络推理的加速引擎，特别强调大<br>
型模型，以分布式方式跨越许多 GPU 和节点。<br>
FasterTransformer 包含Transformer块的高度优化版本的实现，其中包含编码器和解码器部分。<br>
使用此模块，您可以运行完整的编码器-解码器架构（如 T5）以及仅编码器模型（如 BERT）或仅解码器模型<br>
（如 GPT）的推理。 它是用 C++/CUDA 编写的，依赖于高度优化的 cuBLAS、cuBLASLt 和 cuSPARSELt 库。 <br>
这使您可以在 GPU 上构建最快的Transformer推理流程。<br>
Faster Transformer模型加速推理应用<br>
三、FasterTransformer 核心是什么？<br>
张量并行 (TP) 和流水线并行 (PP) 技术<br>
跟之前的tensorRT的加速方法对比，Faster Transformer可以利用多gpu加载Transformer不同的块，推理时更<br>
好的利用了gpu运算。<br>
当每个张量被分成多个块时，就会发生张量并行性，并且张量的每个块都可以放置在单独的 GPU 上。在计算过<br>
程中，每个块在不同的 GPU 上单独并行处理，并且可以通过组合来自多个 GPU 的结果来计算结果（最终张<br>
量）。<br>
当模型被深度拆分并将不同的完整层放置到不同的 GPU/节点上时，就会发生流水线并行。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>在底层，启用节点间/节点内通信依赖于 MPI 和 NVIDIA NCCL。使用此软件堆栈，您可以在多个 GPU 上以张量<br>
并行模式运行大型Transformer，以减少计算延迟。<br>
同时，TP 和 PP 可以结合在一起，在多 GPU 和多节点环境中运行具有数十亿和数万亿个参数（相当于 TB 级权<br>
重）的大型 Transformer 模型。<br>
除了 C 中的源代码，FasterTransformer 还提供 TensorFlow 集成（使用 TensorFlow 操作）、PyTorch 集成（使<br>
用 PyTorch 操作）和 Triton 集成作为后端。<br>
目前，TensorFlow op 仅支持单 GPU，而 PyTorch op 和 Triton 后端都支持多 GPU 和多节点。<br>
为了避免为模型并行性而拆分模型的额外工作，FasterTransformer 还提供了一个工具，用于将模型从不同格式<br>
拆分和转换为 FasterTransformer 二进制文件格式。然后 FasterTransformer 可以直接以二进制格式加载模型。<br>
四、FasterTransformer 优化？<br>
4.1 推理缓存优化<br>
4.2 内存优化<br>
4.3 使用MPI 和 NCCL通信优化<br>
4.4 MatMul 内核自动调整（GEMM 自动调整）<br>
• 动机：因为自回归在推理的时候，像上面vllm的时候讲的会产生非常多的key和value的值，每次都需要重复<br>
计算<br>
• 优化策略：对这些产生的缓存分块存储，避免重复计算。<br>
• 动机：大模型往往带来极大参数量,就算量化到int4也是个不小的内存占用，GPT-3 175b 使用半精度存储也需<br>
要 350 GB；<br>
• 优化策略：Faster Transformer会缓存激活值和输出，在进行新sentence推理时可以重新利用缓存激活值和输<br>
出，避免多层反复计算和保存激活值和输出信息，例如GPT-3 中的层数为 96，因此我们只需要 1/96 的内存<br>
量用于激活<br>
• 张量并行性: FasterTransformer 遵循了 Megatron 的思想。 对于自注意力块和前馈网络块，FT 按行拆分第<br>
一个矩阵的权重，并按列拆分第二个矩阵的权重。 通过优化，FT 可以将每个 Transformer 块的归约操作减<br>
少到两倍;<br>
• 流水线并行性：FasterTransformer 将整批请求拆分为多个微批，隐藏了通信的泡沫。 FasterTransformer 会<br>
针对不同情况自动调整微批量大小;<br>
</p>

<h2>第 3 页</h2>

<p>矩阵乘法是基于Transformer的神经网络中主要和最繁重的操作。 FT 使用来自 CuBLAS 和 CuTLASS 库的功能<br>
来执行这些类型的操作。 重要的是要知道 MatMul 操作可以在“硬件”级别使用不同的低级算法以数十种不同的方<br>
式执行。<br>
GemmBatchedEx 函数实现 MatMul 操作，并以“cublasGemmAlgo_t”作为输入参数。 使用此参数，您可以选择<br>
不同的底层算法进行操作。<br>
FasterTransformer 库使用此参数对所有底层算法进行实时基准测试，并为模型的参数和您的输入数据（注意层<br>
的大小、注意头的数量、隐藏层的大小）选择最佳的一个。 此外，FT 对网络的某些部分使用硬件加速的底层函<br>
数，例如 __expf、__shfl_xor_sync。<br>
4.5 量化推理<br>
FT 的内核支持使用 fp16 和 int8 中的低精度输入数据进行推理。 由于较少的数据传输量和所需的内存，这两种<br>
机制都允许加速。 同时，int8 和 fp16 计算可以在特殊硬件上执行，例如张Tensor Core（适用于从 Volta 开始的<br>
所有 GPU 架构），以及即将推出的 Hopper GPU 中的Transformer引擎。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:57</p>
        </div>
    </div>
</body>
</html>