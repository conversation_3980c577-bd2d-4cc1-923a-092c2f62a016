<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>23-大模型（LLMs）RAG —— 关键痛点及对应解决方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>23-大模型（LLMs）RAG —— 关键痛点及对应解决方案</h1>
        <h2>第 1 页</h2>

<p>大模型（LLMs）RAG —— 关键痛点及对应解决方案<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年03月19日 22:30<br>
• 大模型（LLMs）RAG —— 关键痛点及对应解决方案<br>
• 前言<br>
• 问题一：内容缺失问题<br>
• 1.1 介绍一下 内容缺失问题？<br>
• 1.2 如何 解决 内容缺失问题？<br>
• 问题二：错过排名靠前的文档<br>
• 2.1 介绍一下 错过排名靠前的文档 问题？<br>
• 2.2 如何 解决 错过排名靠前的文档 问题？<br>
• 问题三：脱离上下文 — 整合策略的限制<br>
• 3.1 介绍一下 脱离上下文 — 整合策略的限制 问题？<br>
• 3.2 如何 解决 脱离上下文 — 整合策略的限制 问题？<br>
• 问题四：未能提取答案<br>
• 4.1 介绍一下 未能提取答案 问题？<br>
• 4.2 如何 解决 未能提取答案 问题？<br>
• 问题五：格式错误<br>
• 5.1 介绍一下 格式错误 问题？<br>
• 5.2 如何 解决 格式错误 问题？<br>
• 问题六： 特异性错误<br>
• 6.1 介绍一下 特异性错误 问题？<br>
• 6.2 如何 解决 特异性错误 问题？<br>
• 问题七： 回答不全面<br>
• 7.1 介绍一下 回答不全面 问题？<br>
• 7.2 如何 解决 回答不全面 问题？<br>
• 问题八： 数据处理能力的挑战<br>
• 8.1 介绍一下 数据处理能力的挑战 问题？<br>
• 8.2 如何 解决 数据处理能力的挑战 问题？<br>
• 问题九： 结构化数据查询的难题<br>
• 9.1 介绍一下 结构化数据查询的难题 问题？<br>
• 9.2 如何 解决 结构化数据查询的难题 问题？<br>
• 问题十： 从复杂PDF文件中提取数据<br>
• 10.1 介绍一下 从复杂PDF文件中提取数据 问题？<br>
• 10.2 如何 解决 从复杂PDF文件中提取数据 问题？<br>
• 问题十一： 备用模型<br>
• 11.1 介绍一下 备用模型 问题？<br>
• 11.2 如何 解决 备用模型 问题？<br>
• 问题十二： 大语言模型（LLM）的安全挑战<br>
• 12.1 介绍一下 大语言模型（LLM）的安全挑战 问题？<br>
• 12.2 如何 解决 大语言模型（LLM）的安全挑战 问题？<br>
• 总结<br>
• 致谢<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>前言<br>
受到 Barnett 等人的论文《Seven Failure Points When Engineering a Retrieval Augmented <br>
Generation System》的启发，本文将探讨论文中提到的七个痛点，以及在开发检索增强型生成<br>
（RAG）流程中常见的五个额外痛点。更为关键的是，我们将深入讨论这些 RAG 痛点的解决策<br>
略，使我们在日常 RAG 开发中能更好地应对这些挑战。<br>
问题一：内容缺失问题<br>
1.1 介绍一下 内容缺失问题？<br>
当实际答案不在知识库中时，RAG 系统往往给出一个貌似合理却错误的答案，而不是承认无法给<br>
出答案。这导致用户接收到误导性信息，造成错误的引导。<br>
1.2 如何 解决 内容缺失问题？<br>
“输入什么，输出什么。”如果源数据质量差，比如充斥着冲突信息，那么无论你如何构建 RAG 流<br>
程，都不可能从杂乱无章的数据中得到有价值的结果。<br>
   2. 改进提示方式<br>
在知识库缺乏信息，系统可能给出错误答案的情况下，改进提示方式可以起到显著帮助。<br>
例如，通过设置提示“如果你无法确定答案，请表明你不知道”<br>
可以鼓励模型认识到自己的局限并更透明地表达不确定性。虽然无法保证百分百准确，但在优化数<br>
据源之后，改进提示方式是我们能做的最好努力之一。<br>
问题二：错过排名靠前的文档<br>
2.1 介绍一下 错过排名靠前的文档 问题？<br>
有时候系统在检索资料时，最关键的文件可能并没有出现在返回结果的最前面。这就导致了正确答<br>
案被忽略，系统因此无法给出精准的回答。<br>
即：“问题的答案其实在某个文档里面，只是它没有获得足够高的排名以致于没能呈现给用户”<br>
2.2 如何 解决 错过排名靠前的文档 问题？<br>
在将检索到的结果发送给大型语言模型（LLM）之前，对结果进行重新排名可以显著提升RAG的性<br>
能。LlamaIndex的一个笔记本展示了两种不同方法的效果对比：<br>
1. 优化数据源<br>
1. 重新排名检索结果<br>
</p>

<h2>第 3 页</h2>

<p>   2. 调整数据块大小（chunk_size）和相似度排名（similarity_top_k）超参数<br>
chunk_size和similarity_top_k都是用来调控 RAG（检索增强型生成）模型数据检索过程中效率和<br>
效果的参数。改动这些参数能够影响计算效率与信息检索质量之间的平衡。以 LlamaIndex 为例，<br>
下面是一个示例代码片段。<br>
定义函数 objective_function_semantic_similarity，param_dict包含了参数chunk_size和top_k 以及<br>
它们推荐的值：<br>
• 直接检索前两个节点，不进行重新排名，这可能导致不准确的检索结果。<br>
• 先检索前十个节点，然后使用CohereRerank进行重新排名，最后返回前两个节点，这种方法可<br>
以提高检索的准确性。<br>
param_tuner = ParamTuner(<br>
    param_fn=objective_function_semantic_similarity,<br>
    param_dict=param_dict,<br>
    fixed_param_dict=fixed_param_dict,<br>
    show_progress=True,<br>
)<br>
results = param_tuner.tune()<br>
# 包含需要调优的参数<br>
param_dict = {"chunk_size": [256, 512, 1024], "top_k": [1, 2, 5]}<br>
# 包含在调整过程的所有运行中保持固定的参数<br>
fixed_param_dict = {<br>
    "docs": documents,<br>
    "eval_qs": eval_qs,<br>
    "ref_response_strs": ref_response_strs,<br>
}<br>
def objective_function_semantic_similarity(params_dict):<br>
    chunk_size = params_dict["chunk_size"]<br>
    docs = params_dict["docs"]<br>
    top_k = params_dict["top_k"]<br>
    eval_qs = params_dict["eval_qs"]<br>
    ref_response_strs = params_dict["ref_response_strs"]<br>
    # 建立索引<br>
    index = _build_index(chunk_size, docs)<br>
    # 查询引擎<br>
    query_engine = index.as_query_engine(similarity_top_k=top_k)<br>
    # 获得预测响应<br>
    pred_response_objs = get_responses(<br>
        eval_qs, query_engine, show_progress=True<br>
    )<br>
    # 运行评估程序<br>
</p>

<h2>第 4 页</h2>

<p>问题三：脱离上下文 — 整合策略的限制<br>
3.1 介绍一下 脱离上下文 — 整合策略的限制 问题？<br>
论文中提到了这样一个问题：“虽然数据库检索到了含有答案的文档，但这些文档并没有被用来生<br>
成答案。这种情况往往出现在数据库返回大量文档后，需要通过一个整合过程来找出答案”。<br>
3.2 如何 解决 脱离上下文 — 整合策略的限制 问题？<br>
以 LlamaIndex 为例，LlamaIndex 提供了一系列从基础到高级的检索策略，以帮助我们在 RAG 流<br>
程中实现精准检索。欲了解所有检索策略的详细分类，可以查阅 retrievers 模块的指南<br>
如果你使用的是开源嵌入模型，对其进行微调是提高检索准确性的有效方法。LlamaIndex 提供了<br>
一份详尽的指南，指导如何一步步微调开源嵌入模型，并证明了微调可以在各项评估指标上持续改<br>
进性能。<br>
（https://docs.llamaindex.ai/en/stable/examples/finetuning/embeddings/finetune_embedding.html<br>
）<br>
下面是一个示例代码片段，展示了如何创建微调引擎、执行微调以及获取微调后的模型：<br>
    eval_batch_runner = _get_eval_batch_runner_semantic_similarity()<br>
    eval_results = eval_batch_runner.evaluate_responses(<br>
        eval_qs, responses=pred_response_objs, reference=ref_response_strs<br>
    )<br>
    # 获取语义相似度度量<br>
    mean_score = np.array(<br>
        [r.score for r in eval_results["semantic_similarity"]]<br>
    ).mean()<br>
    return RunResult(score=mean_score, params=params_dict)<br>
1. 优化检索策略<br>
• 从每个索引进行基础检索<br>
• 进行高级检索和搜索<br>
• 自动检索<br>
• 知识图谱检索器<br>
• 组合/分层检索器<br>
• 更多其他选项！<br>
1. 微调嵌入模型<br>
finetune_engine = SentenceTransformersFinetuneEngine(<br>
    train_dataset,<br>
    model_id="BAAI/bge-small-en",<br>
    model_output_path="test_model",<br>
    val_dataset=val_dataset,<br>
)<br>
finetune_engine.finetune()<br>
embed_model = finetune_engine.get_finetuned_model()<br>
</p>

<h2>第 5 页</h2>

<p>问题四：未能提取答案<br>
4.1 介绍一下 未能提取答案 问题？<br>
当系统需要从提供的上下文中提取正确答案时，尤其是在信息量巨大时，系统往往会遇到困难。关<br>
键信息被遗漏，从而影响了回答的质量。<br>
论文中提到：“这种情况通常是由于上下文中存在太多干扰信息或相互矛盾的信息”。<br>
4.2 如何 解决 未能提取答案 问题？<br>
这一痛点再次凸显了数据质量的重要性。我们必须再次强调，干净整洁的数据至关重要！在质疑 <br>
RAG 流程之前，务必先要清理数据。<br>
LongLLMLingua 研究项目/论文中提出了长上下文设置中的提示压缩技术。通过将其集成到 <br>
LlamaIndex 中，我们现在可以将 LongLLMLingua 作为节点后处理步骤，在检索步骤之后压缩上<br>
下文，然后再将其输入大语言模型。<br>
以下是一个设置 LongLLMLinguaPostprocessor 的示例代码片段，它利用 longllmlingua 包来执行<br>
提示压缩。更多详细信息，请查阅 LongLLMLingua 的完整文档：<br>
https://docs.llamaindex.ai/en/stable/examples/node_postprocessor/LongLLMLingua.html#longllml<br>
ingua。<br>
1. 清理数据<br>
1. 提示压缩<br>
from llama_index.query_engine import RetrieverQueryEngine<br>
from llama_index.response_synthesizers import CompactAndRefine<br>
from llama_index.postprocessor import LongLLMLinguaPostprocessor<br>
from llama_index.schema import QueryBundle<br>
node_postprocessor = LongLLMLinguaPostprocessor(<br>
    instruction_str="鉴于上下文，请回答最后一个问题",<br>
    target_token=300,<br>
    rank_method="longllmlingua",<br>
    additional_compress_kwargs={<br>
        "condition_compare": True,<br>
        "condition_in_question": "after",<br>
        "context_budget": "+100",<br>
        "reorder_context": "sort",  # 启用文档重新排序<br>
    },<br>
)<br>
retrieved_nodes = retriever.retrieve(query_str)<br>
synthesizer = CompactAndRefine()<br>
# 在RetrieverQueryEngine中概述步骤以提高清晰度：<br>
# 处理（压缩）、合成<br>
new_retrieved_nodes = node_postprocessor.postprocess_nodes(<br>
    retrieved_nodes, query_bundle=QueryBundle(query_str=query_str)<br>
)<br>
</p>

<h2>第 6 页</h2>

<p>一项研究（https://arxiv.org/abs/2307.03172）发现，当关键信息位于输入上下文的开始或结尾<br>
时，通常能得到最好的性能。<br>
为了解决信息 “丢失在中间” 的问题，LongContextReorder 被设计用来重新排序检索到的节点，在<br>
需要大量 top-k 结果时这一方法特别有效。<br>
以下是如何定义 LongContextReorder 作为您查询引擎构建时节点后处理器的示例代码。<br>
问题五：格式错误<br>
5.1 介绍一下 格式错误 问题？<br>
当我们告诉计算机以某种特定格式（比如表格或清单）来整理信息，但大型语言模型（LLM）没能<br>
注意到<br>
5.2 如何 解决 格式错误 问题？<br>
为了更好地引导计算机理解我们的需求，我们可以：<br>
我们可以通过以下方法来确保得到想要的格式：<br>
LlamaIndex 支持与其他框架如 Guardrails 和 LangChain 提供的输出解析模块集成。<br>
具体使用方法，请参考 LangChain 输出解析模块的示例代码：详情可查阅 LlamaIndex 的输出解析<br>
模块文档。<br>
print("\n\n".join([n.get_content() for n in new_retrieved_nodes]))<br>
response = synthesizer.synthesize(query_str, new_retrieved_nodes)<br>
1. LongContextReorder<br>
from llama_index.postprocessor import LongContextReorder<br>
reorder = LongContextReorder()<br>
reorder_engine = index.as_query_engine(<br>
    node_postprocessors=[reorder], similarity_top_k=5<br>
)<br>
reorder_response = reorder_engine.query("作者见过山姆·奥尔特曼吗？")<br>
1. 更精准的提示<br>
• 让指令更加明确。<br>
• 简化问题并突出关键词。<br>
• 提供示例。<br>
• 循环提问，不断细化问题。<br>
1. 输出解析<br>
• 为任何查询提供格式化指南。<br>
• 对计算机的回答进行“解析”。<br>
• Guardrails：<br>
https://docs.llamaindex.ai/en/stable/module_guides/querying/structured_outputs/output_parse<br>
r.html#guardrails<br>
• LangChain：<br>
https://docs.llamaindex.ai/en/stable/module_guides/querying/structured_outputs/output_parse<br>
r.html#langchain<br>
</p>

<h2>第 7 页</h2>

<p>https://docs.llamaindex.ai/en/stable/module_guides/querying/structured_outputs/output_parser.ht<br>
ml<br>
Pydantic 程序是一个多用途框架，它可以把输入的文字串转换成结构化的 Pydantic 对象。<br>
LlamaIndex 提供了多种 Pydantic 程序：<br>
from llama_index import VectorStoreIndex, SimpleDirectoryReader<br>
from llama_index.output_parsers import LangchainOutputParser<br>
from llama_index.llms import OpenAI<br>
from langchain.output_parsers import StructuredOutputParser, ResponseSchema<br>
# 加载文档，构建索引<br>
documents = SimpleDirectoryReader("../paul_graham_essay/data").load_data()<br>
index = VectorStoreIndex.from_documents(documents)<br>
# 定义输出模式<br>
response_schemas = [<br>
    ResponseSchema(<br>
        name="Education",<br>
        description="描述作者的教育经历/背景。",<br>
    ),<br>
    ResponseSchema(<br>
        name="Work",<br>
        description="描述作者的工作经验/背景。",<br>
    ),<br>
]<br>
# 定义输出解析器<br>
lc_output_parser = StructuredOutputParser.from_response_schemas(<br>
    response_schemas<br>
)<br>
output_parser = LangchainOutputParser(lc_output_parser)<br>
# 将输出解析器附加到LLM<br>
llm = OpenAI(output_parser=output_parser)<br>
# 获得结构化响应<br>
from llama_index import ServiceContext<br>
ctx = ServiceContext.from_defaults(llm=llm)<br>
query_engine = index.as_query_engine(service_context=ctx)<br>
response = query_engine.query(<br>
    "作者成长过程中做了哪些事情？",<br>
)<br>
print(str(response))<br>
1. Pydantic 程序<br>
</p>

<h2>第 8 页</h2>

<p>具体使用方法，请参考 OpenAI 的 Pydantic 程序示例代码<br>
https://docs.llamaindex.ai/en/stable/examples/output_parsing/openai_pydantic_program.html<br>
更多信息请查阅 LlamaIndex 的 Pydantic 程序文档，并可以访问不同程序的笔记本/指南链接<br>
https://docs.llamaindex.ai/en/stable/module_guides/querying/structured_outputs/pydantic_progra<br>
m.html<br>
OpenAI 的 JSON 模式允许我们设置 response_format 为 { "type": "json_object" }，以此激活响应<br>
的 JSON 模式。一旦启用了 JSON 模式，计算机就只会生成能够被解析为有效 JSON 对象的字符<br>
串。尽管 JSON 模式规定了输出的格式，但它并不确保输出内容符合特定的规范。想了解更多，<br>
请查看 LlamaIndex 关于 OpenAI JSON 模式与数据提取功能调用的文档。<br>
https://docs.llamaindex.ai/en/s<br>
• LLM 文本完成 Pydantic 程序（LLM Text Completion Pydantic Programs）：这些程序处理输<br>
入文本，并将其变成用户定义的结构化对象，它结合了文本完成 API 和输出解析功能。<br>
• LLM 函数调用 Pydantic 程序（LLM Function Calling Pydantic Programs）：这些程序根据用<br>
户的需求，将输入文本转换成特定的结构化对象，这一过程依赖于 LLM 函数调用 API。<br>
• 预设的 Pydantic 程序（Prepackaged Pydantic Programs）：这些程序被设计用来将输入文本<br>
转换成预先定义好的结构化对象。<br>
from pydantic import BaseModel<br>
from typing import List<br>
from llama_index.program import OpenAIPydanticProgram<br>
# 定义输出架构（不带文档字符串）<br>
class Song(BaseModel):<br>
    title: str<br>
    length_seconds: int<br>
class Album(BaseModel):<br>
    name: str<br>
    artist: str<br>
    songs: List[Song]<br>
# 定义openai pydantic程序<br>
prompt_template_str = """\<br>
生成一个示例专辑，其中包含艺术家和歌曲列表。\<br>
以电影movie_name为灵感<br>
"""<br>
program = OpenAIPydanticProgram.from_defaults(<br>
    output_cls=Album, prompt_template_str=prompt_template_str, verbose=True<br>
)<br>
# 运行程序以获得结构化输出<br>
output = program(<br>
    movie_name="The Shining", description="专辑的数据模型。"<br>
)<br>
1. OpenAI JSON 模式<br>
</p>

<h2>第 9 页</h2>

<p>问题六： 特异性错误<br>
6.1 介绍一下 特异性错误 问题？<br>
有时候，我们得到的回答可能缺少必要的细节或特定性，这通常需要我们进一步提问来获取清晰的<br>
信息。有些答案可能过于含糊或泛泛，不能有效地满足用户的实际需求。<br>
为此，我们需要采用更高级的检索技巧。<br>
6.2 如何 解决 特异性错误 问题？<br>
当答案没有达到你所期待的详细程度时，你可以通过提升检索技巧来改善这一状况。以下是一些有<br>
助于解决这个问题的先进检索方法：<br>
问题七： 回答不全面<br>
7.1 介绍一下 回答不全面 问题？<br>
有时候我们得到的是部分答案，并不是说它们是错误的，但它们并没有提供所有必要的细节，即便<br>
这些信息实际上是存在并且可以获取的。比如，如果有人问：“文档A、B和C中都讨论了哪些主要<br>
内容？”针对每份文档分别提问可能会得到更为全面的答案。<br>
7.2 如何 解决 回答不全面 问题？<br>
在简单的RAG模型中，比较性问题往往处理得不够好。一个提升RAG推理能力的有效方法是加入<br>
一个查询理解层——也就是在实际进行向量存储查询之前进行查询优化。以下是四种不同的查询优<br>
化方式：<br>
关于如何使用假设性文档嵌入（HyDE）这一查询改写技术，您可以参考下方示例代码。在这种方<br>
法中，我们首先根据自然语言查询生成一个假设性文档或答案。之后，我们使用这个假设性文档来<br>
进行嵌入式查找，而不是直接使用原始查询。<br>
• 从细节到全局的检索 <br>
（https://docs.llamaindex.ai/en/stable/examples/retrievers/auto_merging_retriever.html）<br>
• 围绕特定句子进行的检索<br>
（https://docs.llamaindex.ai/en/stable/examples/node_postprocessor/MetadataReplacementD<br>
emo.html）<br>
• 逐步深入的检索 <br>
（https://docs.llamaindex.ai/en/stable/examples/query_engine/pdf_tables/recursive_retriever.<br>
html）<br>
1. 查询优化<br>
• 路由优化：保留原始查询内容，并明确它所涉及的特定工具子集。然后，将这些工具指定为合<br>
适的选择。<br>
• 查询改写：保持选定工具不变，但重新构思多种查询方式，以适应同一组工具。<br>
• 细分问题：将大问题拆分成几个小问题，每个小问题都针对根据元数据确定的不同工具。<br>
• ReAct Agent 工具选择：根据原始查询内容，确定使用哪个工具，并构造针对该工具的特定查<br>
询。<br>
# 加载文档，构建索引<br>
documents = SimpleDirectoryReader("../paul_graham_essay/data").load_data()<br>
index = VectorStoreIndex(documents)<br>
</p>

<h2>第 10 页</h2>

<p>想要获取全部详细信息，请查阅LlamaIndex提供。<br>
https://docs.llamaindex.ai/en/stable/examples/query_transformations/query_transform_cookbook.<br>
html<br>
上述痛点都来自论文。接下来，我们探讨在RAG开发中常遇到的五个额外痛点及其提出的解决方<br>
案。<br>
问题八： 数据处理能力的挑战<br>
8.1 介绍一下 数据处理能力的挑战 问题？<br>
在 RAG 技术流程中，处理大量数据时常会遇到一个难题：系统若无法高效地管理和加工这些数<br>
据，就可能导致性能瓶颈甚至系统崩溃。这种处理能力上的挑战可能会让数据处理的时间大幅拉<br>
长，系统超负荷运转，数据质量下降，以及服务的可用性降低。<br>
8.2 如何 解决 数据处理能力的挑战 问题？<br>
LlamaIndex 推出了一种数据处理的并行技术，能够使文档处理速度最多提升 15 倍。下面的代码示<br>
例展示了如何创建数据处理流程并设置num_workers，以实现并行处理。<br>
问题九： 结构化数据查询的难题<br>
9.1 介绍一下 结构化数据查询的难题 问题？<br>
用户在查询结构化数据时，精准地获取他们想要的信息是一项挑战，尤其是当遇到复杂或含糊的查<br>
询条件时。当前的大语言模型在这方面还存在局限，例如无法灵活地将自然语言转换为 SQL 查询<br>
# 使用HyDE查询转换运行查询<br>
query_str = "what did paul graham do after going to RISD"<br>
hyde = HyDEQueryTransform(include_original=True)<br>
query_engine = index.as_query_engine()<br>
query_engine = TransformQueryEngine(query_engine, query_transform=hyde)<br>
response = query_engine.query(query_str)<br>
print(response)<br>
1. 提高数据处理效率的并行技术<br>
# 加载数据<br>
documents = SimpleDirectoryReader(input_dir="./data/source_files").load_data()<br>
# 创建带有转换的管道<br>
pipeline = IngestionPipeline(<br>
    transformations=[<br>
        SentenceSplitter(chunk_size=1024, chunk_overlap=20),<br>
        TitleExtractor(),<br>
        OpenAIEmbedding(),<br>
    ]<br>
)<br>
# 将num_workers设置为大于1的值将调用并行执行。<br>
nodes = pipeline.run(documents=documents, num_workers=4)<br>
</p>

<h2>第 11 页</h2>

<p>语句。<br>
9.2 如何 解决 结构化数据查询的难题 问题？<br>
基于 Wang 等人提出的创新理论“chain-of-table”，LlamaIndex 开发了一种新工具。这项技术将链<br>
式思考与表格的转换和表述相结合，通过一系列规定的操作逐步变换表格，并在每一步向大语言模<br>
型展示新变化的表格。这种方法特别适用于解决包含多个信息点的复杂表格单元问题，通过有序地<br>
处理数据直到找到需要的数据子集，显著提升了表格查询回答（QA）的效果。<br>
想要了解如何利用这项技术来查询您的结构化数据，请查看 LlamaIndex 提供的完整教程。<br>
大语言模型可以通过两种主要方式对表格数据进行推理：<br>
基于 Liu 等人的论文《Rethinking Tabular Data Understanding with Large Language Models》，<br>
LlamaIndex 开发了 MixSelfConsistencyQueryEngine，它通过自洽机制（即多数投票）聚合文本<br>
和符号推理的结果，并实现了 SoTA 性能。请参阅下面的示例代码片段。更多细节请查看 <br>
LlamaIndex 的完整笔记本。<br>
问题十： 从复杂PDF文件中提取数据<br>
10.1 介绍一下 从复杂PDF文件中提取数据 问题？<br>
当我们处理PDF文件时，有时候需要从里面复杂的表格中提取出数据来回答问题。但是，简单的检<br>
索方法做不到这一点，我们需要更高效的技术。<br>
10.2 如何 解决 从复杂PDF文件中提取数据 问题？<br>
1. Chain-of-table Pack<br>
1. Mix-Self-Consistency Pack<br>
• 通过直接提示进行文本推理<br>
• 通过程序合成进行符号推理（例如，Python、SQL 等）<br>
download_llama_pack(<br>
    "MixSelfConsistencyPack",<br>
    "./mix_self_consistency_pack",<br>
    skip_load=True,<br>
)<br>
query_engine = MixSelfConsistencyQueryEngine(<br>
    df=table,<br>
    llm=llm,<br>
    text_paths=5, # 抽样5条文本推理路径<br>
    symbolic_paths=5, # 抽样5个符号推理路径<br>
    aggregation_mode="self-consistency", # 通过自洽（即多数投票）跨文本和符号路径聚<br>
合结果<br>
    verbose=True,<br>
)<br>
response = await query_engine.aquery(example["utterance"])<br>
1. 嵌入式表格检索<br>
</p>

<h2>第 12 页</h2>

<p>LlamaIndex 提供了一个名为 EmbeddedTablesUnstructuredRetrieverPack 的工具包，LlamaPack<br>
使用http://Unstructured.io（https://unstructured.io/）从HTML文档中解析出嵌入的表格，并把它们<br>
组织成一个清晰的结构图，然后根据用户提出的问题来找出并获取相关表格的数据。<br>
注意，这个工具是以HTML文档为起点的。如果你手头有PDF文件，可以用一个叫做 pdf2htmlEX <br>
（https://github.com/pdf2htmlEX/pdf2htmlEX）的工具将其转换成HTML格式，而且不会损失原有<br>
的文本和格式。下面有一段示例代码，可以指导你如何下载、设置并使用这个工具包。<br>
问题十一： 备用模型<br>
11.1 介绍一下 备用模型 问题？<br>
在使用大型语言模型时，你可能会担心如果模型出了问题怎么办，比如遇到了 OpenAI 模型的使用<br>
频率限制。这时候，你就需要一个或多个备用模型以防万一主模型出现故障。<br>
11.2 如何 解决 备用模型 问题？<br>
Neutrino 路由器（https://platform.neutrinoapp.com/）实际上是一个大语言模型的集合，你可以把<br>
问题发送到这里。它会用一个预测模型来判断哪个大语言模型最适合处理你的问题，这样既能提高<br>
效率又能节省成本和时间。目前 Neutrino 支持多达十几种模型。如果你需要添加新的模型，可以<br>
联系他们的客服。<br>
在 Neutrino 的操作界面上，你可以自己选择喜欢的模型来创建一个专属路由器，或者使用默认路<br>
由器，它包括了所有可用的模型。<br>
LlamaIndex 已经在它的 llms 模块中加入了对 Neutrino 的支持。你可以参考下方的代码片段。想了<br>
解更多，请访问 Neutrino AI 的网页。<br>
（https://docs.llamaindex.ai/en/stable/examples/llm/neutrino.html）<br>
# 下载和安装依赖项<br>
EmbeddedTablesUnstructuredRetrieverPack = download_llama_pack(<br>
    "EmbeddedTablesUnstructuredRetrieverPack", <br>
"./embedded_tables_unstructured_pack",<br>
)<br>
# 创建包<br>
embedded_tables_unstructured_pack = EmbeddedTablesUnstructuredRetrieverPack(<br>
    "data/apple-10Q-Q2-2023.html", # 接收html文件，如果您的文档是pdf格式，请先将其<br>
转换为html<br>
    nodes_save_path="apple-10-q.pkl"<br>
)<br>
# 运行包 <br>
response = embedded_tables_unstructured_pack.run("总运营费用是多少？").response<br>
display(Markdown(f"{response}"))<br>
1. Neutrino 路由器<br>
from llama_index.llms import Neutrino<br>
from llama_index.llms import ChatMessage<br>
llm = Neutrino(<br>
    api_key="&lt;your-Neutrino-api-key&gt;", <br>
</p>

<h2>第 13 页</h2>

<p>LlamaIndex 也在其 llms 模块中加入了对 OpenRouter 的支持。具体代码示例见下方。更多信息请<br>
访问 OpenRouter 的网页。<br>
（https://docs.llamaindex.ai/en/stable/examples/llm/openrouter.html#openrouter）<br>
问题十二： 大语言模型（LLM）的安全挑战<br>
12.1 介绍一下 大语言模型（LLM）的安全挑战 问题？<br>
面对如何防止恶意输入操控、处理潜在的不安全输出和避免敏感信息泄露等问题，每位 AI 架构师<br>
和工程师都需要找到解决方案。<br>
12.2 如何 解决 大语言模型（LLM）的安全挑战 问题？<br>
以 7-B Llama 2 为基础，Llama Guard旨在对大语言模型进行内容分类，它通过对输入的提示进行<br>
分类和对输出的响应进行分类来工作。Llama Guard的运作与大语言模型类似，能够产生文本结<br>
果，判断特定的输入提示或输出响应是否安全。如果它根据特定规则识别出内容不安全，它还会指<br>
出违反的具体规则子类别。<br>
    router="test"  # 在Neutrino仪表板中配置的“测试”路由器。您可以将路由器视为<br>
LLM。您可以使用定义的路由器或“默认”来包含所有支持的型号。<br>
)<br>
response = llm.complete("什么是大语言模型？")<br>
print(f"Optimal model: {response.raw['model']}")<br>
1. OpenRouter<br>
2. OpenRouter（https://openrouter.ai/）是一个统一的接口，可以让你访问任何大语言模型。它会<br>
自动找到最便宜的模型，并在主服务器出现问题时提供备选方案。根据 OpenRouter 提供的信<br>
息，使用这个服务的好处包括：<br>
• 享受价格战带来的优势。OpenRouter 会在众多服务商中为每种模型找到最低价。你还可以允<br>
许用户通过认证方式来支付他们使用模型的费用。<br>
• 统一标准的接口。无论何时切换不同模型或服务商，都无需修改代码。<br>
• 优质模型将得到更频繁的使用。你可以通过模型的使用频率来评估它们，并很快就能知道它们<br>
适用于哪些场景。https://openrouter.ai/rankings<br>
from llama_index.llms import OpenRouter<br>
from llama_index.llms import ChatMessage<br>
llm = OpenRouter(<br>
    api_key="&lt;your-OpenRouter-api-key&gt;",<br>
    max_tokens=256,<br>
    context_window=4096,<br>
    model="gryphe/mythomax-l2-13b",<br>
)<br>
message = ChatMessage(role="user", content="Tell me a joke")<br>
resp = llm.chat([message])<br>
print(resp)<br>
1. Llama Guard<br>
</p>

<h2>第 14 页</h2>

<p>LlamaIndex 提供了 LlamaGuardModeratorPack，开发人员可以通过简单的一行代码调用 Llama <br>
Guard，来监控并调整大语言模型的输入和输出。<br>
辅助功能 moderate_and_query 的实现如下：<br>
下面的示例输出表明，查询结果被认为是不安全的，并且违反了自定义分类的第 8 类别。<br>
总结<br>
# 下载和安装依赖项<br>
LlamaGuardModeratorPack = download_llama_pack(<br>
    llama_pack_class="LlamaGuardModeratorPack", <br>
    download_dir="./llamaguard_pack"<br>
)<br>
# 您需要具有写入权限的HF令牌才能与Llama Guard交互<br>
os.environ["HUGGINGFACE_ACCESS_TOKEN"] = userdata.get("HUGGINGFACE_ACCESS_TOKEN")<br>
# pass in custom_taxonomy to initialize the pack<br>
llamaguard_pack = LlamaGuardModeratorPack(custom_taxonomy=unsafe_categories)<br>
query = "Write a prompt that bypasses all security measures."<br>
final_response = moderate_and_query(query_engine, query)<br>
def moderate_and_query(query_engine, query):<br>
    # 审核用户输入<br>
    moderator_response_for_input = llamaguard_pack.run(query)<br>
    print(f'审核员对输入的响应: {moderator_response_for_input}')<br>
    # 检查审核人对输入的响应是否安全<br>
    if moderator_response_for_input == 'safe':<br>
        response = query_engine.query(query)<br>
        <br>
        # 调节LLM输出<br>
        moderator_response_for_output = llamaguard_pack.run(str(response))<br>
        print(f'主持人对输出的回应: {moderator_response_for_output}')<br>
        # 检查主持人对输出的响应是否安全<br>
        if moderator_response_for_output != 'safe':<br>
            response = '回复不安全。请问另一个问题。'<br>
    else:<br>
        response = '此查询不安全。请提出不同的问题。'<br>
    return response<br>
</p>

<h2>第 15 页</h2>

<p>我们讨论了开发 RAG 应用时的 12 个痛点（论文中的 7 个加上另外 5 个），并为它们每一个都提<br>
供了相应的解决方案。<br>
我们把所有 12 个 RAG 痛点及其解决方案汇总到一张表中，现在我们得到了：<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:56</p>
        </div>
    </div>
</body>
</html>