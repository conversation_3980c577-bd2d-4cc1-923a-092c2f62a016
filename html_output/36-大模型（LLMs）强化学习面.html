<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>36-大模型（LLMs）强化学习面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>36-大模型（LLMs）强化学习面</h1>
        <h2>第 1 页</h2>

<p>大模型（LLMs）强化学习面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 20:47<br>
1 简单介绍强化学习？<br>
强化学习：（Reinforcement Learning）一种机器学习的方法，通过从外部获得激励来校正学习方向从而获得一<br>
种自适应的学习能力。<br>
2 简单介绍一下 RLHF？<br>
基于人工反馈的强化学习（Reinforcement Learning from Human Feedback，RLHF）：构建人类反馈数据集，<br>
训练一个激励模型，模仿人类偏好对结果打分，这是GPT-3后时代大语言模型越来越像人类对话核心技术。<br>
3. 奖励模型需要和基础模型一致吗？<br>
不同实现方式似乎限制不同。（待实践确认）colossal-ai的coati中需要模型有相同的tokenizer，所以选模型只能<br>
从同系列中找。在ppo算法实现方式上据说trlx是最符合论文的。<br>
4. RLHF 在实践过程中存在哪些不足？<br>
5. 如何解决 人工产生的偏好数据集成本较高，很难量产问题？<br>
该方法的核心在于通过AI 模型监督其他 AI 模型，即在SFT阶段，从初始模型中采样，然后生成自我批评和修<br>
正，然后根据修正后的反应微调原始模型。 在 RL 阶段，从微调模型中采样，使用一个模型来评估生成的样本，<br>
• 大模型（LLMs）强化学习面<br>
• 1 简单介绍强化学习？<br>
• 2 简单介绍一下 RLHF？<br>
• 3. 奖励模型需要和基础模型一致吗？<br>
• 4. RLHF 在实践过程中存在哪些不足？<br>
• 5. 如何解决 人工产生的偏好数据集成本较高，很难量产问题？<br>
• 6. 如何解决三个阶段的训练（SFT-&gt;RM-&gt;PPO）过程较长，更新迭代较慢问题？<br>
• 7. 如何解决 PPO 的训练过程同时存在4个模型（2训练，2推理），对计算资源的要求较高 问题？<br>
• 致谢<br>
1. 不足点1：人工产生的偏好数据集成本较高，很难量产；<br>
2. 不足点2：三个阶段的训练（SFT-&gt;RM-&gt;PPO）过程较长，更新迭代较慢；<br>
3. 不足点3：PPO 的训练过程同时存在4个模型（2训练，2推理），对计算资源的要求较高。<br>
• 解决方法：AI 专家替代派<br>
• 代表方法：<br>
1. RLAIF<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>并从这个 AI 偏好数据集训练一个偏好模型。 然后使用偏好模型作为奖励信号对 RL 进行训练，即 RL from AI <br>
Feedback（RLAIF）。<br>
RRHF(Rank Response from Human Feedback) 不需要强化学习，可以利用不同语言模型生成的回复，包括 <br>
ChatGPT、GPT-4 或当前的训练模型。RRHF通过对回复进行评分，并通过排名损失来使回复与人类偏好对齐。<br>
RRHF 通过通过排名损失使评分与人类的偏好（或者代理的奖励模型）对齐。RRHF 训练好的模型可以同时作为<br>
生成语言模型和奖励模型使用<br>
6. 如何解决三个阶段的训练（SFT-&gt;RM-&gt;PPO）过程较长，更新迭代较慢问题？<br>
LIMA(Less Is More for Alignment) 即浅层对齐假说，即一个模型的知识和能力几乎完全是在预训练中学习的，而<br>
对齐则是教会它与用户交互时如何选择子分布。如果假说正确，对齐主要有关于学习方式，那么该假说的一个推<br>
论是，人们可以用相当少的样本充分调整预训练的语言模型。因此，该工作假设，对齐可以是一个简单的过程，<br>
模型学习与用户互动的风格或格式，以揭示在预训练中已经获得的知识和能力。<br>
本文主要从数据角度来探讨如何降低 LLM 训练阶段的成本，提高数据效率。为了实现该目的，作者通过从现有<br>
数据中识别出最有价值的核心样本来帮助模型获取下游任务的知识，并仅用少量数据来实现可比甚至更好的性<br>
能。<br>
7. 如何解决 PPO 的训练过程同时存在4个模型（2训练，2推理），对计算资源的要求较高 问题？<br>
RAFT（Reward rAnked FineTuning），它基于关于通过奖励和监督微调对样本进行排序的组合的形式。<br>
DPO(Direct Preference Optimization) 提出了一种使用二进制交叉熵目标来精确优化LLM的方法，以替代基于 RL <br>
HF 的优化目标，从而大大简化偏好学习 pipeline。<br>
知识星球<br>
1. RRHF<br>
• 解决方法：微调数据优化派<br>
• 方法介绍：该类方法的核心在于仅仅通过优质数据集的获取和产生，以训练得到一个效果较好的 <br>
SFT 模型，而无需进行 RM 和 PPO 的训练。<br>
• 代表方法：<br>
1. LIMA<br>
1. MAYBE ONLY 0.5% DATA IS NEEDED<br>
• 解决方法：训练过程改造派<br>
• 方法介绍：该类方法通常通过改造模型的训练方式（如只保留SFT和RM），以提高训练效率并减少<br>
训练成本。<br>
• 代表方法：<br>
1. RAFT<br>
1. DPO<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:26:57</p>
        </div>
    </div>
</body>
</html>