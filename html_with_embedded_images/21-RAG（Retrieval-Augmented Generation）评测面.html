<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>21-RAG（Retrieval-Augmented Generation）评测面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>21-RAG（Retrieval-Augmented Generation）评测面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>RAG（Retrieval-Augmented Generation）评测面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月28日 10:12<br>
一、为什么需要 对 RAG 进行评测？<br>
在探索和优化 RAG（检索增强生成器）的过程中，如何有效评估其性能已经成为关键问题。<br>
二、如何合成 RAG 测试集？<br>
假设你已经成功构建了一个RAG 系统，并且现在想要评估它的性能。为了这个目的，你需要一个<br>
评估数据集，该数据集包含以下列：<br>
前两列代表真实数据，最后两列代表 RAG 预测数据。<br>
要创建这样的数据集，我们首先需要生成问题和答案的元组。<br>
接下来，在RAG上运行这些问题以获得预测结果。<br>
要生成（问题、答案）元组，我们首先需要准备 RAG 数据，我们将其拆分为块，并将其嵌入向量<br>
数据库中。 完成这些步骤后，我们会指示 LLM 从指定主题中生成 num_questions 个问题，从而得<br>
• RAG（Retrieval-Augmented Generation）评测面<br>
• 一、为什么需要 对 RAG 进行评测？<br>
• 二、如何合成 RAG 测试集？<br>
• 三、RAG 有哪些评估方法？<br>
• 3.1 独立评估<br>
• 3.1.1 介绍一下 独立评估？<br>
• 3.1.2 介绍一下 独立评估 模块？<br>
• 3.2 端到端评估<br>
• 3.2.1 介绍一下 端到端评估<br>
• 3.2.2 介绍一下 端到端评估 模块？<br>
• 四、RAG 有哪些关键指标和能力？<br>
• 五、RAG 有哪些评估框架？<br>
• 4.1 RAGAS<br>
• 4.2 ARES<br>
• 致谢<br>
• question（问题）：想要评估的RAG的问题<br>
• ground_truths（真实答案）：问题的真实答案<br>
• answer（答案）：RAG 预测的答案<br>
• contexts（上下文）：RAG 用于生成答案的相关信息列表<br>
• 生成问题和基准答案（实践中可能会出现偏差）<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>到问题和答案元组。<br>
为了从给定的上下文中生成问题和答案，我们需要按照以下步骤操作：<br>
1. 选择一个随机块并将其作为根上下文<br>
2. 从向量数据库中检索 K 个相似的上下文<br>
3. 将根上下文和其 K 个相邻上下文的文本连接起来以构建一个更大的上下文<br>
4. 使用这个大的上下文和 num_questions 在以下的提示模板中生成问题和答案<br>
"""\\<br>
Your task is to formulate exactly {num_questions} questions from given context and <br>
provide the answer to each one.<br>
End each question with a '?' character and then in a newline write the answer to <br>
that question using only <br>
the context provided.<br>
Separate each question/answer pair by "XXX"<br>
Each question must start with "question:".<br>
Each answer must start with "answer:".<br>
The question must satisfy the rules given below:<br>
1.The question should make sense to humans even when read without the given <br>
context.<br>
2.The question should be fully answered from the given context.<br>
3.The question should be framed from a part of context that contains important <br>
information. It can also be from tables,code,etc.<br>
4.The answer to the question should not contain any links.<br>
5.The question should be of moderate difficulty.<br>
6.The question must be reasonable and must be understood and responded by humans.<br>
7.Do no use phrases like 'provided context',etc in the question<br>
8.Avoid framing question using word "and" that can be decomposed into more than one <br>
question.<br>
9.The question should not contain more than 10 words, make of use of abbreviation <br>
wherever possible.<br>
    <br>
context: {context}<br>
"""<br>
"""\\<br>
您的任务是根据给定的上下文提出{num_questions}个问题，并给出每个问题的答案。<br>
在每个问题的末尾加上"? <br>
提供的上下文写出该问题的答案。<br>
每个问题/答案之间用 "XXX "隔开。<br>
每个问题必须以 "question: "开头。<br>
每个答案必须以 "answer: "开头。<br>
问题必须符合以下规则：<br>
1.即使在没有给定上下文的情况下，问题也应该对人类有意义。<br>
</p>

<h2>第 3 页</h2>

<p>基于上面的工作流程，下面是我生成问题和答案的结果示例。<br>
首先构建一个向量存储，其中包含 RAG 使用的数据。<br>
2.问题应能根据给定的上下文给出完整的答案。<br>
3.问题应从包含重要信息的上下文中提取。也可以是表格、代码等。<br>
4.问题答案不应包含任何链接。<br>
5.问题难度应适中。<br>
6.问题必须合理，必须为人类所理解和回答。<br>
7.不要在问题中使用 "提供上下文 "等短语。<br>
8.避免在问题中使用 "和 "字，因为它可以分解成多个问题。<br>
9.问题不应超过 10 个单词，尽可能使用缩写。<br>
    <br>
语境： {上下文｝<br>
"""<br>
5. 重复以上步骤 num_count 次,每次改变上下文并生成不同的问题。<br>
|    | question                                           | ground_truths              <br>
|<br>
|---:|:---------------------------------------------------|:-----------------------<br>
----------------------------|<br>
|  8 | What is the difference between lists and tuples in | ['Lists are mutable and <br>
cannot be used as          |<br>
|    | Python?                                            | dictionary keys, while <br>
tuples are immutable and    |<br>
|    |                                                    | can be used as <br>
dictionary keys if all elements are |<br>
|    |                                                    | immutable.']               <br>
|<br>
|  4 | What is the name of the Python variant optimized   | ['MicroPython and <br>
CircuitPython']                  |<br>
|    | for microcontrollers?                              |                            <br>
|<br>
| 13 | What is the name of the programming language that  | ['ABC programming <br>
language']                       |<br>
|    | Python was designed to replace?                    |                            <br>
|<br>
| 17 | How often do bugfix releases occur?                | ['Bugfix releases occur <br>
about every 3 months.']    |<br>
|  3 | What is the significance of Python's release       | ['Python 2.0 was <br>
released in 2000, while Python    |<br>
|    | history?                                           | 3.0, a major revision <br>
with limited backward        |<br>
|    |                                                    | compatibility, was <br>
released in 2008.']             |<br>
• 编码用例<br>
1. 我们从 Wikipedia 加载它<br>
from langchain.document_loaders import WikipediaLoader<br>
</p>

<h2>第 4 页</h2>

<p>topic = "python programming"<br>
wikipedia_loader = WikipediaLoader(<br>
    query=topic,<br>
    load_max_docs=1,<br>
    doc_content_chars_max=100000,<br>
)<br>
docs = wikipedia_loader.load()<br>
doc = docs[0]<br>
2. 加载数据后，我们将其分成块。<br>
from langchain.text_splitter import RecursiveCharacterTextSplitter<br>
CHUNK_SIZE = 512<br>
CHUNK_OVERLAP = 128<br>
splitter = RecursiveCharacterTextSplitter(<br>
    chunk_size=CHUNK_SIZE,<br>
    chunk_overlap=CHUNK_OVERLAP,<br>
    separators=[". "],<br>
)<br>
splits = splitter.split_documents([doc])<br>
3. 在 Pinecone 中创建一个索引。<br>
import pinecone<br>
pinecone.init(<br>
    api_key=os.environ.get("PINECONE_API_KEY"),<br>
    environment=os.environ.get("PINECONE_ENV"),<br>
)<br>
index_name = topic.replace(" ", "-")<br>
pinecone.init(<br>
    api_key=os.environ.get("PINECONE_API_KEY"),<br>
    environment=os.environ.get("PINECONE_ENV"),<br>
)<br>
if index_name in pinecone.list_indexes():<br>
    pinecone.delete_index(index_name)<br>
pinecone.create_index(index_name, dimension=768)<br>
4. 使用 LangChain 包装器来索引其中的分片嵌入。<br>
from langchain.vectorstores import Pinecone<br>
docsearch = Pinecone.from_documents(<br>
    splits,<br>
    embedding_model,<br>
    index_name=index_name,<br>
)<br>
5. 生成合成数据集<br>
</p>

<h2>第 5 页</h2>

<p>我们使用 LLM、文档拆分、嵌入模型和 Pinecone 索引名称从TestsetGenerator 类初始化一个对<br>
象。<br>
from langchain.embeddings import VertexAIEmbeddings<br>
from langchain.llms import VertexAI<br>
from testset_generator import TestsetGenerator<br>
generator_llm = VertexAI(<br>
    location="europe-west3",<br>
    max_output_tokens=256,<br>
    max_retries=20,<br>
)<br>
embedding_model = VertexAIEmbeddings()<br>
testset_generator = TestsetGenerator(<br>
    generator_llm=generator_llm,<br>
    documents=splits,<br>
    embedding_model=embedding_model,<br>
    index_name=index_name,<br>
    key="text",<br>
)<br>
1. 通过传递两个参数来调用generate方法<br>
synthetic_dataset = testset_generator.generate(<br>
    num_contexts=10,<br>
    num_questions_per_context=2,<br>
)<br>
2. 生成问题与答案如下<br>
|    | question                                           | ground_truths              <br>
|<br>
|---:|:---------------------------------------------------|:-----------------------<br>
----------------------------|<br>
|  8 | What is the difference between lists and tuples in | ['Lists are mutable and <br>
cannot be used as          |<br>
|    | Python?                                            | dictionary keys, while <br>
tuples are immutable and    |<br>
|    |                                                    | can be used as <br>
dictionary keys if all elements are |<br>
|    |                                                    | immutable.']               <br>
|<br>
|  4 | What is the name of the Python variant optimized   | ['MicroPython and <br>
CircuitPython']                  |<br>
|    | for microcontrollers?                              |                            <br>
|<br>
| 13 | What is the name of the programming language that  | ['ABC programming <br>
language']                       |<br>
|    | Python was designed to replace?                    |                            <br>
|<br>
</p>

<h2>第 6 页</h2>

<p>接下来使用 RAG 来预测每个问题的答案，并提供用于支撑响应的上下文列表。<br>
| 17 | How often do bugfix releases occur?                | ['Bugfix releases occur <br>
about every 3 months.']    |<br>
|  3 | What is the significance of Python's release       | ['Python 2.0 was <br>
released in 2000, while Python    |<br>
|    | history?                                           | 3.0, a major revision <br>
with limited backward        |<br>
|    |                                                    | compatibility, was <br>
released in 2008.']             |<br>
1. 初始化 RAG<br>
# 初始化RAG<br>
from rag import RAGimport RAG<br>
rag = RAG(<br>
    index_name,<br>
    "text-bison",<br>
    embedding_model,<br>
    "text",<br>
)<br>
2. 通过对每个问题调用 predict 方法来迭代合成数据集并收集预测<br>
rag_answers = []<br>
contexts = []<br>
for i, row in synthetic_dataset.iterrows():<br>
    question = row["question"]<br>
    prediction = rag.predict(question)<br>
    rag_answer = prediction["answer"]<br>
    rag_answers.append(rag_answer)<br>
    source_documents = prediction["source_documents"]<br>
    contexts.append([s.page_content for s in source_documents])<br>
synthetic_dataset_rag = synthetic_dataset.copy()<br>
synthetic_dataset_rag["answer"] = rag_answers<br>
3. 最终结果如下<br>
|    | question                                                                    <br>
| ground_truths               | answer                                                 <br>
| contexts                                           |_truths               | <br>
answer                                                                                 <br>
| contexts                                           |<br>
|---:|:----------------------------------------------------------------------------<br>
|:----------------------------|:---------------------------------------------------<br>
--------------------------------------------------------|:-------------------------<br>
--------------------------|<br>
|  7 | What are the two types of classes that Python supported before version 3.0? <br>
| ['old-style and new-style'] | Before version 3.0, Python had two kinds of classes <br>
</p>

<h2>第 7 页</h2>

<p>基于以上步骤，我们已经为评估 RAG 做好了准备，接下来我们讲解如何进行 RAG 评估。<br>
三、RAG 有哪些评估方法？<br>
主要有两种方法来评估 RAG 的有效性：独立评估和端到端评估。<br>
3.1 独立评估<br>
3.1.1 介绍一下 独立评估？<br>
3.1.2 介绍一下 独立评估 模块？<br>
此指标的目标是评估生成的答案与提供的问题提示之间的相关性。答案如果缺乏完整性或者包含冗<br>
余信息，那么其得分将相对较低。这一指标通过问题和答案的结合来进行计算，评分的范围通常在<br>
0到1之间，其中高分代表更好的相关性。<br>
示例<br>
(both using the same syntax): old-style and new-style. | ['. New instances of <br>
classes are constructed by    |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| calling the class (for example, SpamClass() or     |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| EggsClass()), and the classes are instances of the |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| metaclass type (itself an instance of itself),     |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| allowing metaprogramming and reflection.\\nBefore   |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| version 3.0, Python had two kinds of classes (both |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| using the same syntax): old-style and new-style,   |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| current Python versions only support the semantics |<br>
|    |                                                                             <br>
|                             |                                                        <br>
| new style.\\nPython supports optio     ..........   |<br>
• 介绍：独立评估涉及对检索模块和生成模块（即阅读和合成信息）的评估。<br>
• 介绍：生成模块指的是将检索到的文档与查询相结合，形成增强或合成的输入。这与最终答案<br>
或响应的生成不同，后者通常采用端到端的评估方式。<br>
• 评估指标：<br>
• 1、 答案相关性（Answer Relevancy）<br>
</p>

<h2>第 8 页</h2>

<p>这个评价标准旨在检查生成的答案在给定上下文中的事实准确性。评估的过程涉及到答案内容与其<br>
检索到的上下文之间的比对。这一指标也使用一个介于0到1之间的数值来表示，其中更高的数值意<br>
味着答案与上下文的一致性更高。<br>
示例<br>
在这个指标中，我们评估所有在给定上下文中与基准信息相关的条目是否被正确地排序。理想情况<br>
下，所有相关的内容应该出现在排序的前部。这一评价标准同样使用0到1之间的得分值来表示，其<br>
中较高的得分反映了更高的精确度。<br>
指标：命中率 (Hit Rate)、平均排名倒数 (MRR)、归一化折扣累积增益 (NDCG)、**精确度 <br>
(Precision)**等。<br>
该指标主要用于测量生成的答案与实际基准答案之间的匹配程度。这一评估考虑了基准答案和生成<br>
答案的对比，其得分也通常在0到1之间，较高的得分表明生成答案与实际答案的一致性更高。<br>
3.2 端到端评估<br>
3.2.1 介绍一下 端到端评估<br>
3.2.2 介绍一下 端到端评估 模块？<br>
问题：健康饮食的主要特点是什么？<br>
低相关性答案：健康饮食对整体健康非常重要。<br>
高相关性答案：健康饮食应包括各种水果、蔬菜、全麦食品、瘦肉和乳制品，为优化健康提供<br>
必要的营养素。<br>
• 2、 忠实度（Faithfulness）<br>
问题：居里夫人的主要成就是什么？<br>
背景：玛丽·居里（1867-1934年）是一位开创性的物理学家和化学家，她是第一位获得诺贝尔<br>
奖的女性，也是唯一一位在两个不同领域获得诺贝尔奖的女性。<br>
高忠实度答案：玛丽·居里在物理和化学两个领域都获得了诺贝尔奖，使她成为第一位实现这<br>
一成就的女性。<br>
低忠实度答案：玛丽·居里只在物理学领域获得了诺贝尔奖。<br>
• 3、上下文精确度（Context Precision）<br>
• 4、答案正确性（Answer Correctness）<br>
示例：<br>
基本事实：埃菲尔铁塔于 1889 年在法国巴黎竣工。<br>
答案正确率高：埃菲尔铁塔于 1889 年在法国巴黎竣工。<br>
答案正确率低：埃菲尔铁塔于 1889 年竣工，矗立在英国伦敦。<br>
• 介绍：对 RAG 模型对特定输入生成的最终响应进行评估，涉及模型生成的答案与输入查询的<br>
相关性和一致性。<br>
1. 无标签的内容评估：<br>
a. 评价指标：答案的准确性、相关性和无害性<br>
2. 有标签的内容评估：<br>
</p>

<h2>第 9 页</h2>

<p>四、RAG 有哪些关键指标和能力？<br>
评估 RAG 在不同下游任务和不同检索器中的应用可能会得到不同的结果。然而，一些学术和工程<br>
实践已经开始关注 RAG 的通用评估指标和有效运用所需的能力。<br>
五、RAG 有哪些评估框架？<br>
在 RAG 评估框架领域，RAGAS 和 ARES 是较新的方法。<br>
5.1 RAGAS<br>
RAGAS 是一个基于简单手写提示的评估框架，通过这些提示全自动地衡量答案的准确性、相关性<br>
和上下文相关性。<br>
5.2 ARES<br>
ARES 的目标是自动化评价 RAG 系统在上下文相关性、答案忠实度和答案相关性三个方面的性<br>
能。ARES 减少了评估成本，通过使用少量的手动标注数据和合成数据，并应用预测驱动推理 <br>
(PDR) 提供统计置信区间，提高了评估的准确性。<br>
a. 评价指标：准确率 (Accuracy) 和精确匹配 (EM)<br>
• 关键指标：集中于三个关键指标：答案的准确性、答案的相关性和上下文的相关性。<br>
• 关键能力：<br>
• RGB的研究分析了不同大语言模型在处理 RAG 所需的四项基本能力方面的表现，包括<br>
抗噪声能力、拒绝无效回答能力、信息综合能力和反事实稳健性，从而为检索增强型生<br>
成设立了标准。<br>
• 介绍：<br>
• 算法原理：<br>
1. 答案忠实度评估：利用大语言模型 (LLM) 分解答案为多个陈述，检验每个陈述与上下文的一致<br>
性。最终，根据支持的陈述数量与总陈述数量的比例，计算出一个“忠实度得分”。<br>
2. 答案相关性评估：使用大语言模型 (LLM) 创造可能的问题，并分析这些问题与原始问题的相似<br>
度。答案相关性得分是通过计算所有生成问题与原始问题相似度的平均值来得出的。<br>
3. 上下文相关性评估：运用大语言模型 (LLM) 筛选出直接与问题相关的句子，以这些句子占上下<br>
文总句子数量的比例来确定上下文相关性得分。<br>
• 介绍：<br>
• 算法原理：<br>
1. 生成合成数据集：ARES 首先使用语言模型从目标语料库中的文档生成合成问题和答案，创建<br>
正负两种样本。<br>
2. 训练大语言模型 (LLM) 裁判：然后，ARES 对轻量级语言模型进行微调，利用合成数据集训练<br>
它们以评估上下文相关性、答案忠实度和答案相关性。<br>
3. 基于置信区间对 RAG 系统排名：最后，ARES 使用这些裁判模型为 RAG 系统打分，并结合手<br>
动标注的验证集，采用 PPI 方法生成置信区间，从而可靠地评估 RAG 系统的性能。<br>
</p>

<h2>第 10 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:46</p>
        </div>
    </div>
</body>
</html>