<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>38-大模型（LLMs）强化学习—— PPO 面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>38-大模型（LLMs）强化学习—— PPO 面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）强化学习—— PPO 面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 20:47<br>
一、大语言模型RLHF中的PPO主要分哪些步骤？<br>
大语言模型RLHF中的PPO 分为：<br>
对应的实现逻辑如下：<br>
二、举例描述一下 大语言模型的RLHF？<br>
大语言模型的RLHF，实际上是模型先试错再学习的过程。<br>
大语言模型的RLHF 好比是：老师与学生的角色<br>
• 大模型（LLMs）强化学习—— PPO 面<br>
• 一、大语言模型RLHF中的PPO主要分哪些步骤？<br>
• 二、举例描述一下 大语言模型的RLHF？<br>
• 三、大语言模型RLHF 采样篇<br>
• 3.1 什么是 PPO 中 采样过程？<br>
• 3.2 介绍一下 PPO 中 采样策略？<br>
• 3.3 PPO 中 采样策略中，如何评估“收益”？<br>
• 参考<br>
1. 采样<br>
2. 反馈<br>
3. 学习<br>
policy_model = load_model()<br>
for k in range(20000):<br>
    # 采样（生成答案）<br>
    prompts = sample_prompt()<br>
    data = respond(policy_model, prompts)<br>
    <br>
    # 反馈（计算奖励）<br>
    rewards = reward_func(reward_model, data)<br>
    <br>
    # 学习（更新参数）<br>
    for epoch in range(4):<br>
        policy_model = train(policy_model, prompts, data, rewards)<br>
• 我们扮演着老师的角色，给出有趣的问题。模型则会像小学生一样，不断尝试给出答案。<br>
• 模型会根据我们给出的问题，写出它觉得正确的答案，但是这些答案不一定是真的答案，需要我们结合正确<br>
答案进行打分。如果它表现得好，就会给予它高声赞扬；如果它表现不佳，我们则会给予它耐心的指导和反<br>
馈，帮助它不断改进，直到达到令人满意的水平。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>三、大语言模型RLHF 采样篇<br>
3.1 什么是 PPO 中 采样过程？<br>
PPO 中 采样过程：学生回答问题的过程，是模型根据提示（prompt）输出回答（response）的过程，或者说是<br>
模型自行生产训练数据的过程。<br>
eg:<br>
3.2 介绍一下 PPO 中 采样策略？<br>
PPO 中 采样工作 通过一种策略（policy）：policy由两个模型组成，一个叫做演员模型（Actor），另一个叫<br>
做评论家模型（Critic）。它们就像是学生大脑中的两种意识，一个负责决策，一个负责总结得失。<br>
演员：我们想要训练出来的大模型。在用PPO训练它之前，它就是RLHF的第一步训练出来的<br>
SFT（Supervised Fine-Tuning）model。输入一段上下文，它将输出下一个token的概率分布。<br>
评论家：强化学习的辅助模型，输入一段上下文，它将输出下一个token的“收益”。<br>
3.3 PPO 中 采样策略中，如何评估“收益”？<br>
从下一个token开始，模型能够获得的总奖励（浮点数标量）。这里说的奖励包括Reward Model给出的奖励。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:46</p>
        </div>
    </div>
</body>
</html>