<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>58-怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>58-怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>怎么让英文大语言模型支持中文？（一） —— 构建中文tokenization<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:24<br>
一、为什么需要 构建中文tokenization？<br>
大语言模型 百家争鸣趋势，但是 从 根本来看，主要 以 基于llama家族的模型 和 基于glm家族的模型为主。不过<br>
目前大多数 LLMs 还是以 基于llama家族的模型 为主，然而 原始基于llama家族的模型 主要训练语料为 英文语<br>
料，中文语料占比较少，直接导致，基于llama家族的模型 对于 中文的支持不太友好。<br>
那么有什么办法 能够 解决 基于llama家族的模型 对于 中文的支持不太友好 问题呢？<br>
本文利用 《斗破苍穹》作为语料，介绍 从 扩充vocab里面的词以对中文进行token化。<br>
二、如何对 原始数据预处理？<br>
《斗破苍穹》 原始数据<br>
上文摘取 部分 《斗破苍穹》 原始数据，从 数据中可以看出，数据中包含 大量换行 和 无效内容，所以需要对 <br>
《斗破苍穹》 原始数据 进行预处理，将每一行转化为一句或多句话，同时过滤掉 换行 和 无效内容。<br>
代码讲解<br>
    《斗破苍穹》来自: <br>
===上架感言===<br>
又一次上架了，这次比上次还激动，甚至激动到了上传了章节却不知道发出来的地步。<br>
    尴尬，关于新书，上架前成绩好得有些出乎土豆的意料，对于这份厚硕的成绩，土豆心里<br>
还真有几分惶恐与忐忑，虽说曾经有人说土豆是刷出来的数据，对于这些留言，我也并未太过<br>
在意，别的我不知道，我唯一能知道的，就是人在做，天在看！<br>
    究竟刷没刷，自己心中有杆秤就能衡量，问心无愧，何惧留言？<br>
    呵呵，在这里很感谢赐予土豆这种厚硕成绩的诸位书友，真的，很感谢你们。<br>
    ...<br>
# step 1: 《斗破苍穹》 原始数据 加载<br>
with open("data/《斗破苍穹》.txt", "r", encoding="utf-8") as fp:<br>
    data = fp.read().strip().split("\n")<br>
# step 2: 将每一行转化为一句或多句话，同时过滤掉 换行 和 无效内容<br>
sentences = []<br>
for d in data:<br>
    d = d.strip()<br>
    if "===" in d or len(d) == 0 or d == "《斗破苍穹》来自:":<br>
        continue<br>
    sentences.append(d)<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>预处理之后的 corpus.txt<br>
三、如何构建中文的词库？<br>
得到语料库 corpus.txt 之后，我们需要利用该 corpus.txt 构建 中文的词库。这里采用 sentencepiece 训练中文词<br>
库。<br>
运行 上述代码之后会得到 tokenizer.model和tokenizer.vocab两个文件<br>
tokenizer.vocab 词表: 除了一些特殊符号外，还有我们自定义的foo和bar，其余的一些词是BPE训练得到<br>
# step 3: 数据写入<br>
with open("data/corpus.txt", "w", encoding="utf-8") as fp:<br>
    fp.write("\n".join(sentences))<br>
    又一次上架了，这次比上次还激动，甚至激动到了上传了章节却不知道发出来的地步。<br>
    尴尬，关于新书，上架前成绩好得有些出乎土豆的意料，对于这份厚硕的成绩，土豆心里<br>
还真有几分惶恐与忐忑，虽说曾经有人说土豆是刷出来的数据，对于这些留言，我也并未太过<br>
在意，别的我不知道，我唯一能知道的，就是人在做，天在看！<br>
    ...<br>
1. sentencepiece 安装<br>
    $ pip install sentencepiece<br>
2. 训练中文词库<br>
    import sentencepiece as spm<br>
    spm.SentencePieceTrainer.train(<br>
        input='data/corpus.txt',<br>
        model_prefix='tokenizer',<br>
        vocab_size=50000,<br>
        user_defined_symbols=['foo', 'bar'],<br>
        character_coverage=1.0,<br>
        model_type="bpe",<br>
    )<br>
• 参数介绍：<br>
• input：指定输入文本文件的路径或者是一个目录，可以指定多个输入文件或目录。其中每一行可以<br>
是一句话或者多句话；<br>
• tokenizer：保存的模型的名称前缀；<br>
• vocab_size：设置的词表大小；<br>
• user_defined_symbols：用于指定用户自定义的符号。这些符号将会被视为单独的 Token，不会被拆<br>
分成子词。这个参数的作用是将一些用户定义的特殊符号作为一个整体加入到生成的词表中，以便于<br>
后续的模型使用。这里我们简单进行了测试；<br>
• model_type: 指定模型的类型，有三种可选参数：unigram, bpe, char. word；<br>
• character_coverage：指定覆盖字符的数量，可以理解为限制字符集的大小。默认值为 1.0，即覆盖<br>
全部字符；<br>
• unk_id: 指定未登录词的 ID 号，即在词表中为未登录词分配一个整数 ID。默认值为 0；<br>
• bos_id: 指定句子开头符号的 ID 号，即在词表中为句子开头符号分配一个整数 ID。默认值为 1；<br>
• eos_id: 指定句子结束符号的 ID 号，即在词表中为句子结束符号分配一个整数 ID。默认值为 2；<br>
• pad_id: 指定填充符号的 ID 号，即在词表中为填充符号分配一个整数 ID。默认值为 -1，即不使用填<br>
充符号；<br>
&lt;unk&gt;<br>
0<br>
&lt;s&gt; 0<br>
</p>

<h2>第 3 页</h2>

<p>四、如何使用transformers库加载sentencepiece模型？<br>
chinese_bpe.py<br>
&lt;/s&gt;<br>
0<br>
foo 0<br>
bar 0<br>
萧炎<br>
-0<br>
..<br>
-1<br>
▁“<br>
-2<br>
也是<br>
-3<br>
便是<br>
-4<br>
了一<br>
-5<br>
...<br>
import os<br>
os.environ["PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"] = "python"<br>
from transformers import LlamaTokenizer<br>
from sentencepiece import sentencepiece_model_pb2 as sp_pb2_model<br>
import sentencepiece as spm<br>
from tokenization import ChineseTokenizer<br>
chinese_sp_model_file = "sentencepisece_tokenizer/tokenizer.model"<br>
# load<br>
chinese_sp_model = spm.SentencePieceProcessor()<br>
chinese_sp_model.Load(chinese_sp_model_file)<br>
chinese_spm = sp_pb2_model.ModelProto()<br>
chinese_spm.ParseFromString(chinese_sp_model.serialized_model_proto())<br>
## Save<br>
output_dir = './transformers_tokenizer/chinese/'<br>
os.makedirs(output_dir, exist_ok=True)<br>
with open(output_dir + 'chinese.model', 'wb') as f:<br>
    f.write(chinese_spm.SerializeToString())<br>
tokenizer = ChineseTokenizer(vocab_file=output_dir + 'chinese.model')<br>
tokenizer.save_pretrained(output_dir)<br>
print(f"Chinese tokenizer has been saved to {output_dir}")<br>
# Test<br>
chinese_tokenizer = ChineseTokenizer.from_pretrained(output_dir)<br>
print(tokenizer.all_special_tokens)<br>
print(tokenizer.all_special_ids)<br>
print(tokenizer.special_tokens_map)<br>
text = '''白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including'''<br>
</p>

<h2>第 4 页</h2>

<p>运行结果<br>
注：其中ChineseTokenizer这里参考了llama模型里面使用的方法，并稍微做些修改：<br>
print("Test text:\n", text)<br>
print(f"Tokenized by Chinese tokenizer:{chinese_tokenizer.tokenize(text)}")<br>
Chinese tokenizer has been saved to ./transformers_tokenizer/chinese/<br>
['&lt;s&gt;', '&lt;/s&gt;', '&lt;unk&gt;']<br>
[1, 2, 0]<br>
{'bos_token': '&lt;s&gt;', 'eos_token': '&lt;/s&gt;', 'unk_token': '&lt;unk&gt;'}<br>
Test text:<br>
 白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including<br>
Tokenized by Chinese-LLaMA tokenizer:['▁', '白日', '依', '山', '尽', ',', '黄', <br>
'河', '入', '海', '流', '。', '欲', '穷', '千里', '目', ',', '更', '上一层', '楼', <br>
'。', '▁', 'T', 'h', 'e', '▁', 'p', 'r', 'i', 'm', 'a', 'r', 'y', '▁', 'u', 's', <br>
'e', '▁', 'o', 'f', '▁', 'LL', 'a', 'MA', '▁i', 's', '▁', 'r', 'e', 's', 'e', <br>
'a', 'r', 'ch', '▁', 'o', 'n', '▁', 'l', 'a', 'r', 'g', 'e', '▁', 'l', 'an', <br>
'g', 'u', 'a', 'g', 'e', '▁', 'm', 'o', 'd', 'e', 'l', 's', ',', '▁i', 'n', 'c', <br>
'lu', 'd', 'i', 'ng']<br>
# coding=utf-8<br>
# Copyright 2022 EleutherAI and the HuggingFace Inc. team. All rights reserved.<br>
#<br>
# This code is based on EleutherAI's GPT-NeoX library and the GPT-NeoX<br>
# and OPT implementations in this library. It has been modified from its<br>
# original forms to accommodate minor architectural differences compared<br>
# to GPT-NeoX and OPT used by the Meta AI team that trained the model.<br>
#<br>
# Licensed under the Apache License, Version 2.0 (the "License");<br>
# you may not use this file except in compliance with the License.<br>
# You may obtain a copy of the License at<br>
#<br>
#     http://www.apache.org/licenses/LICENSE-2.0<br>
#<br>
# Unless required by applicable law or agreed to in writing, software<br>
# distributed under the License is distributed on an "AS IS" BASIS,<br>
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.<br>
# See the License for the specific language governing permissions and<br>
# limitations under the License.<br>
"""Tokenization classes for LLaMA."""<br>
import os<br>
from shutil import copyfile<br>
from typing import Any, Dict, List, Optional, Tuple<br>
import sentencepiece as spm<br>
</p>

<h2>第 5 页</h2>

<p>from transformers.tokenization_utils import AddedToken, PreTrainedTokenizer<br>
from transformers.utils import logging<br>
logger = logging.get_logger(__name__)<br>
VOCAB_FILES_NAMES = {"vocab_file": "tokenizer.model"}<br>
# PRETRAINED_VOCAB_FILES_MAP = {<br>
#     "vocab_file": {<br>
#         "hf-internal-testing/llama-tokenizer": "https://huggingface.co/hf-<br>
internal-testing/llama-tokenizer/resolve/main/tokenizer.model",<br>
#     },<br>
#     "tokenizer_file": {<br>
#         "hf-internal-testing/llama-tokenizer": "https://huggingface.co/hf-<br>
internal-testing/llama-tokenizer/resolve/main/tokenizer_config.json",<br>
#     },<br>
# }<br>
# PRETRAINED_POSITIONAL_EMBEDDINGS_SIZES = {<br>
#     "hf-internal-testing/llama-tokenizer": 2048,<br>
# }<br>
class ChineseTokenizer(PreTrainedTokenizer):<br>
    """<br>
    Construct a Llama tokenizer. Based on byte-level Byte-Pair-Encoding.<br>
    Args:<br>
        vocab_file (`str`):<br>
            Path to the vocabulary file.<br>
    """<br>
    vocab_files_names = VOCAB_FILES_NAMES<br>
    # pretrained_vocab_files_map = PRETRAINED_VOCAB_FILES_MAP<br>
    # max_model_input_sizes = PRETRAINED_POSITIONAL_EMBEDDINGS_SIZES<br>
    model_input_names = ["input_ids", "attention_mask"]<br>
    def __init__(<br>
        self,<br>
        vocab_file,<br>
        unk_token="&lt;unk&gt;",<br>
        bos_token="&lt;s&gt;",<br>
        eos_token="&lt;/s&gt;",<br>
        pad_token=None,<br>
        sp_model_kwargs: Optional[Dict[str, Any]] = None,<br>
        add_bos_token=True,<br>
        add_eos_token=False,<br>
</p>

<h2>第 6 页</h2>

<p>        clean_up_tokenization_spaces=False,<br>
        **kwargs,<br>
    ):<br>
        self.sp_model_kwargs = {} if sp_model_kwargs is None else sp_model_kwargs<br>
        bos_token = AddedToken(bos_token, lstrip=False, rstrip=False) if <br>
isinstance(bos_token, str) else bos_token<br>
        eos_token = AddedToken(eos_token, lstrip=False, rstrip=False) if <br>
isinstance(eos_token, str) else eos_token<br>
        unk_token = AddedToken(unk_token, lstrip=False, rstrip=False) if <br>
isinstance(unk_token, str) else unk_token<br>
        pad_token = AddedToken(pad_token, lstrip=False, rstrip=False) if <br>
isinstance(pad_token, str) else pad_token<br>
        super().__init__(<br>
            bos_token=bos_token,<br>
            eos_token=eos_token,<br>
            unk_token=unk_token,<br>
            pad_token=pad_token,<br>
            add_bos_token=add_bos_token,<br>
            add_eos_token=add_eos_token,<br>
            sp_model_kwargs=self.sp_model_kwargs,<br>
            clean_up_tokenization_spaces=clean_up_tokenization_spaces,<br>
            **kwargs,<br>
        )<br>
        self.vocab_file = vocab_file<br>
        self.add_bos_token = add_bos_token<br>
        self.add_eos_token = add_eos_token<br>
        self.sp_model = spm.SentencePieceProcessor(**self.sp_model_kwargs)<br>
        self.sp_model.Load(vocab_file)<br>
    def __getstate__(self):<br>
        state = self.__dict__.copy()<br>
        state["sp_model"] = None<br>
        return state<br>
    def __setstate__(self, d):<br>
        self.__dict__ = d<br>
        self.sp_model = spm.SentencePieceProcessor(**self.sp_model_kwargs)<br>
        self.sp_model.Load(self.vocab_file)<br>
    @property<br>
    def vocab_size(self):<br>
        """Returns vocab size"""<br>
        return self.sp_model.get_piece_size()<br>
    def get_vocab(self):<br>
        """Returns vocab as a dict"""<br>
        vocab = {self.convert_ids_to_tokens(i): i for i in range(self.vocab_size)}<br>
</p>

<h2>第 7 页</h2>

<p>        vocab.update(self.added_tokens_encoder)<br>
        return vocab<br>
    def _tokenize(self, text):<br>
        """Returns a tokenized string."""<br>
        return self.sp_model.encode(text, out_type=str)<br>
    def _convert_token_to_id(self, token):<br>
        """Converts a token (str) in an id using the vocab."""<br>
        return self.sp_model.piece_to_id(token)<br>
    def _convert_id_to_token(self, index):<br>
        """Converts an index (integer) in a token (str) using the vocab."""<br>
        token = self.sp_model.IdToPiece(index)<br>
        return token<br>
    def convert_tokens_to_string(self, tokens):<br>
        """Converts a sequence of tokens (string) in a single string."""<br>
        current_sub_tokens = []<br>
        out_string = ""<br>
        prev_is_special = False<br>
        for i, token in enumerate(tokens):<br>
            # make sure that special tokens are not decoded using sentencepiece <br>
model<br>
            if token in self.all_special_tokens:<br>
                if not prev_is_special and i != 0:<br>
                    out_string += " "<br>
                out_string += self.sp_model.decode(current_sub_tokens) + token<br>
                prev_is_special = True<br>
                current_sub_tokens = []<br>
            else:<br>
                current_sub_tokens.append(token)<br>
                prev_is_special = False<br>
        out_string += self.sp_model.decode(current_sub_tokens)<br>
        return out_string<br>
    def save_vocabulary(self, save_directory, filename_prefix: Optional[str] = <br>
None) -&gt; Tuple[str]:<br>
        """<br>
        Save the vocabulary and special tokens file to a directory.<br>
        Args:<br>
            save_directory (`str`):<br>
                The directory in which to save the vocabulary.<br>
        Returns:<br>
            `Tuple(str)`: Paths to the files saved.<br>
</p>

<h2>第 8 页</h2>

<p>        """<br>
        if not os.path.isdir(save_directory):<br>
            logger.error(f"Vocabulary path ({save_directory}) should be a <br>
directory")<br>
            return<br>
        out_vocab_file = os.path.join(<br>
            save_directory, (filename_prefix + "-" if filename_prefix else "") + <br>
VOCAB_FILES_NAMES["vocab_file"]<br>
        )<br>
        if os.path.abspath(self.vocab_file) != os.path.abspath(out_vocab_file) and <br>
os.path.isfile(self.vocab_file):<br>
            copyfile(self.vocab_file, out_vocab_file)<br>
        elif not os.path.isfile(self.vocab_file):<br>
            with open(out_vocab_file, "wb") as fi:<br>
                content_spiece_model = self.sp_model.serialized_model_proto()<br>
                fi.write(content_spiece_model)<br>
        return (out_vocab_file,)<br>
    def build_inputs_with_special_tokens(self, token_ids_0, token_ids_1=None):<br>
        bos_token_id = [self.bos_token_id] if self.add_bos_token else []<br>
        eos_token_id = [self.eos_token_id] if self.add_eos_token else []<br>
        output = bos_token_id + token_ids_0 + eos_token_id<br>
        if token_ids_1 is not None:<br>
            output = output + bos_token_id + token_ids_1 + eos_token_id<br>
        return output<br>
    def get_special_tokens_mask(<br>
        self, token_ids_0: List[int], token_ids_1: Optional[List[int]] = None, <br>
already_has_special_tokens: bool = False<br>
    ) -&gt; List[int]:<br>
        """<br>
        Retrieve sequence ids from a token list that has no special tokens added. <br>
This method is called when adding<br>
        special tokens using the tokenizer `prepare_for_model` method.<br>
        Args:<br>
            token_ids_0 (`List[int]`):<br>
                List of IDs.<br>
            token_ids_1 (`List[int]`, *optional*):<br>
                Optional second list of IDs for sequence pairs.<br>
            already_has_special_tokens (`bool`, *optional*, defaults to `False`):<br>
</p>

<h2>第 9 页</h2>

<p>                Whether or not the token list is already formatted with special <br>
tokens for the model.<br>
        Returns:<br>
            `List[int]`: A list of integers in the range [0, 1]: 1 for a special <br>
token, 0 for a sequence token.<br>
        """<br>
        if already_has_special_tokens:<br>
            return super().get_special_tokens_mask(<br>
                token_ids_0=token_ids_0, token_ids_1=token_ids_1, <br>
already_has_special_tokens=True<br>
            )<br>
        bos_token_id = [1] if self.add_bos_token else []<br>
        eos_token_id = [1] if self.add_eos_token else []<br>
        if token_ids_1 is None:<br>
            return bos_token_id + ([0] * len(token_ids_0)) + eos_token_id<br>
        return (<br>
            bos_token_id<br>
            + ([0] * len(token_ids_0))<br>
            + eos_token_id<br>
            + bos_token_id<br>
            + ([0] * len(token_ids_1))<br>
            + eos_token_id<br>
        )<br>
    def create_token_type_ids_from_sequences(<br>
        self, token_ids_0: List[int], token_ids_1: Optional[List[int]] = None<br>
    ) -&gt; List[int]:<br>
        """<br>
        Creates a mask from the two sequences passed to be used in a sequence-pair <br>
classification task. An ALBERT<br>
        sequence pair mask has the following format:<br>
        ```<br>
        0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1<br>
        | first sequence    | second sequence |<br>
        ```<br>
        if token_ids_1 is None, only returns the first portion of the mask (0s).<br>
        Args:<br>
            token_ids_0 (`List[int]`):<br>
                List of ids.<br>
            token_ids_1 (`List[int]`, *optional*):<br>
                Optional second list of IDs for sequence pairs.<br>
</p>

<h2>第 10 页</h2>

<p>不难发现其实里面使用了一些sentencepiece里面的函数。<br>
五、如何合并英文词表和中文词表？<br>
chinese_llama_bpe.py<br>
        Returns:<br>
            `List[int]`: List of [token type IDs](../glossary#token-type-ids) <br>
according to the given sequence(s).<br>
        """<br>
        bos_token_id = [self.bos_token_id] if self.add_bos_token else []<br>
        eos_token_id = [self.eos_token_id] if self.add_eos_token else []<br>
        output = [0] * len(bos_token_id + token_ids_0 + eos_token_id)<br>
        if token_ids_1 is not None:<br>
            output += [1] * len(bos_token_id + token_ids_1 + eos_token_id)<br>
        return output<br>
import os<br>
os.environ["PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"] = "python"<br>
from transformers import LlamaTokenizer<br>
from sentencepiece import sentencepiece_model_pb2 as sp_pb2_model<br>
import sentencepiece as spm<br>
llama_tokenizer_dir = "transformers_tokenizer/llama/tokenizer.model"<br>
chinese_sp_model_file = "sentencepisece_tokenizer/tokenizer.model"<br>
# load<br>
llama_tokenizer = LlamaTokenizer.from_pretrained(llama_tokenizer_dir)<br>
llama_spm = sp_pb2_model.ModelProto()<br>
llama_spm.ParseFromString(llama_tokenizer.sp_model.serialized_model_proto())<br>
chinese_spm = sp_pb2_model.ModelProto()<br>
chinese_sp_model = spm.SentencePieceProcessor()<br>
chinese_sp_model.Load(chinese_sp_model_file)<br>
chinese_spm.ParseFromString(chinese_sp_model.serialized_model_proto())<br>
# print number of tokens<br>
print(len(llama_tokenizer), len(chinese_sp_model))<br>
print(llama_tokenizer.all_special_tokens)<br>
print(llama_tokenizer.all_special_ids)<br>
print(llama_tokenizer.special_tokens_map)<br>
## Add Chinese tokens to LLaMA tokenizer<br>
llama_spm_tokens_set = set(p.piece for p in llama_spm.pieces)<br>
print(len(llama_spm_tokens_set))<br>
</p>

<h2>第 11 页</h2>

<p>代码中，最核心的是以下这一块，该代码的作用是 将 原始词表 中没有的 新词 加入 词表中<br>
运行结果<br>
print(f"Before:{len(llama_spm_tokens_set)}")<br>
for p in chinese_spm.pieces:<br>
    piece = p.piece<br>
    if piece not in llama_spm_tokens_set:<br>
        new_p = sp_pb2_model.ModelProto().SentencePiece()<br>
        new_p.piece = piece<br>
        new_p.score = 0<br>
        llama_spm.pieces.append(new_p)<br>
print(f"New model pieces: {len(llama_spm.pieces)}")<br>
## Save<br>
output_sp_dir = 'transformers_tokenizer/llama_chinese'<br>
output_hf_dir = 'transformers_tokenizer/llama_chinese'  # the path to save Chinese-<br>
LLaMA tokenizer<br>
os.makedirs(output_sp_dir, exist_ok=True)<br>
with open(output_sp_dir + '/chinese_llama.model', 'wb') as f:<br>
    f.write(llama_spm.SerializeToString())<br>
tokenizer = LlamaTokenizer(vocab_file=output_sp_dir + '/chinese_llama.model')<br>
tokenizer.save_pretrained(output_hf_dir)<br>
print(f"Chinese-LLaMA tokenizer has been saved to {output_hf_dir}")<br>
# Test<br>
llama_tokenizer = LlamaTokenizer.from_pretrained(llama_tokenizer_dir)<br>
chinese_llama_tokenizer = LlamaTokenizer.from_pretrained(output_hf_dir)<br>
print(tokenizer.all_special_tokens)<br>
print(tokenizer.all_special_ids)<br>
print(tokenizer.special_tokens_map)<br>
text = '''白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including'''<br>
print("Test text:\n", text)<br>
print(f"Tokenized by LLaMA tokenizer:{llama_tokenizer.tokenize(text)}")<br>
print(f"Tokenized by Chinese-LLaMA tokenizer:<br>
{chinese_llama_tokenizer.tokenize(text)}")<br>
for p in chinese_spm.pieces:<br>
    piece = p.piece<br>
    if piece not in llama_spm_tokens_set:<br>
        new_p = sp_pb2_model.ModelProto().SentencePiece()<br>
        new_p.piece = piece<br>
        new_p.score = 0<br>
        llama_spm.pieces.append(new_p)<br>
32000 50000<br>
['&lt;s&gt;', '&lt;/s&gt;', '&lt;unk&gt;']<br>
</p>

<h2>第 12 页</h2>

<p>注：会发现再加入了我们定义的词表后确实能够对中文进行分词了。<br>
六、怎么使用修改后的词表？<br>
如果我们重新从头开始训练，那么其实使用起来很简单：<br>
但是如果我们想要保留原始模型embedding的参数，那么我们可以这么做：<br>
[1, 2, 0]<br>
{'bos_token': '&lt;s&gt;', 'eos_token': '&lt;/s&gt;', 'unk_token': '&lt;unk&gt;'}<br>
32000<br>
Before:32000<br>
New model pieces: 81163<br>
Chinese-LLaMA tokenizer has been saved to transformers_tokenizer/llama_chinese<br>
['&lt;s&gt;', '&lt;/s&gt;', '&lt;unk&gt;']<br>
[1, 2, 0]<br>
{'bos_token': '&lt;s&gt;', 'eos_token': '&lt;/s&gt;', 'unk_token': '&lt;unk&gt;'}<br>
Test text:<br>
 白日依山尽，黄河入海流。欲穷千里目，更上一层楼。<br>
The primary use of LLaMA is research on large language models, including<br>
Tokenized by LLaMA tokenizer:['▁', '白', '日', '&lt;0xE4&gt;', '&lt;0xBE&gt;', '&lt;0x9D&gt;', '山', <br>
'&lt;0xE5&gt;', '&lt;0xB0&gt;', '&lt;0xBD&gt;', '，', '黄', '河', '入', '海', '流', '。', '&lt;0xE6&gt;', <br>
'&lt;0xAC&gt;', '&lt;0xB2&gt;', '&lt;0xE7&gt;', '&lt;0xA9&gt;', '&lt;0xB7&gt;', '千', '里', '目', '，', '更', <br>
'上', '一', '&lt;0xE5&gt;', '&lt;0xB1&gt;', '&lt;0x82&gt;', '&lt;0xE6&gt;', '&lt;0xA5&gt;', '&lt;0xBC&gt;', '。', <br>
'&lt;0x0A&gt;', 'The', '▁primary', '▁use', '▁of', '▁L', 'La', 'MA', '▁is', <br>
'▁research', '▁on', '▁large', '▁language', '▁models', ',', '▁including']<br>
Tokenized by Chinese-LLaMA tokenizer:['▁白', '日', '依', '山', '尽', '，', '黄', <br>
'河', '入', '海', '流', '。', '欲', '穷', '千里', '目', '，', '更', '上一层', '楼', <br>
'。', '&lt;0x0A&gt;', 'The', '▁primary', '▁use', '▁of', '▁L', 'La', 'MA', '▁is', <br>
'▁research', '▁on', '▁large', '▁language', '▁models', ',', '▁including']<br>
config = AutoConfig.from_pretrained(...)<br>
tokenizer = LlamaTokenizer.from_pretrained(...)<br>
model = LlamaForCausalLM.from_pretrained(..., config=config)<br>
model_vocab_size = model.get_output_embeddings().weight.size(0)<br>
model.resize_token_embeddings(len(tokenizer))<br>
1. 找到新词表和旧词表id之间的映射关系。<br>
2. 将模型里面新词表里面包含的旧词表用原始模型的embedding替换。<br>
3. 如果新词在旧词表里面没有出现就进行相应的初始化再进行赋值。比如transformers库中的llama是这么进行<br>
初始化的：<br>
 def _init_weights(self, module):<br>
        std = self.config.initializer_range<br>
        if isinstance(module, nn.Linear):<br>
            module.weight.data.normal_(mean=0.0, std=std)<br>
            if module.bias is not None:<br>
                module.bias.data.zero_()<br>
        elif isinstance(module, nn.Embedding):<br>
            module.weight.data.normal_(mean=0.0, std=std)<br>
            if module.padding_idx is not None:<br>
</p>

<h2>第 13 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>注：具体怎么做可以参考一下这个：https://github.com/yangjianxin1/LLMPruner<br>
总结一下 构建中文tokenization？<br>
到这里为止，我们已经学会了：<br>
知识星球<br>
                module.weight.data[module.padding_idx].zero_()<br>
1. 使用sentencepiece训练一个中文的词表。<br>
2. 使用transformers加载sentencepiece模型。<br>
3. 怎么合并中英文的词表，并使用transformers使用合并后的词表。<br>
4. 在模型中怎么使用新词表。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:45</p>
        </div>
    </div>
</body>
</html>