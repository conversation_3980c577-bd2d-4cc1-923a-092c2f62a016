<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>51-图解分布式训练（七）—— accelerate 分布式训练 详细解析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>51-图解分布式训练（七）—— accelerate 分布式训练 详细解析</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图解分布式训练（七）—— accelerate 分布式训练 详细解析<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 11:58<br>
一、为什么需要 accelerate 分布式训练？<br>
PyTorch Accelerate 是一个 PyTorch 的加速工具包，旨在简化 PyTorch 训练和推断的开发过程，并提高性能。它<br>
是由 Hugging Face、NVIDIA、AWS 和 Microsoft 等公司联合开发的，是一个开源项目。<br>
二、什么是 accelerate 分布式训练?<br>
2.1 accelerate 分布式训练 介绍<br>
PyTorch Accelerate 提供了一组简单易用的 API，帮助开发者实现模型的分布式训练、混合精度训练、自动调<br>
参、数据加载优化和模型优化等功能。它还集成了 PyTorch Lightning 和 TorchElastic，使用户能够轻松地实现高<br>
性能和高可扩展性的模型训练和推断。<br>
2.2 accelerate 分布式训练 主要优势<br>
PyTorch Accelerate 的主要优势包括：<br>
三、accelerate 分布式训练 原理讲解？<br>
3.1 分布式训练<br>
分布式训练是指将一个大型深度学习模型拆分成多个小模型，在不同的计算机上并行训练，最后将结果合并，得<br>
到最终的模型。分布式训练可以显著减少模型训练的时间，因为它充分利用了多个计算机的计算资源。同时，由<br>
于每个小模型只需要处理部分数据，因此可以使用更大的批次大小，进一步提高训练速度。<br>
3.2 加速策略<br>
Accelerate提供了多种加速策略，如pipeline并行、数据并行等。<br>
3.2.1 Pipeline并行<br>
Pipeline并行是指将模型拆分成多个部分，在不同的计算机上并行训练。在每个计算机上，只需要处理模型的一<br>
部分，然后将结果传递给下一个计算机。这样可以充分利用多个计算机的计算资源，并且可以使用更大的批次大<br>
小，提高训练速度。Pipeline并行的缺点是，由于每个计算机只处理部分数据，因此每个计算机的结果都会有一<br>
些误差，最终的结果可能会有一些偏差。<br>
3.2.2 数据并行<br>
数据并行是指将数据拆分成多个部分，在不同的计算机上并行训练。在每个计算机上，都会处理全部的模型，但<br>
是每个计算机只处理部分数据。这样可以充分利用多个计算机的计算资源，并且可以使用更大的批次大小，提高<br>
训练速度。数据并行的优点是，每个计算机都会处理全部的模型，因此结果更加准确。缺点是，由于每个计算机<br>
都需要完整的模型，因此需要更多的计算资源。<br>
3.2.3 加速器<br>
加速器是指用于加速深度学习模型训练的硬件设备，如GPU、TPU等。加速器可以大幅提高模型的训练速度，因<br>
为它们可以在更短的时间内完成更多的计算。Accelerate可以自动检测并利用可用的加速器，以进一步提高训练<br>
速度。<br>
四、accelerate 分布式训练 如何实践？<br>
4.1 accelerate 分布式训练 依赖安装<br>
• 分布式训练：可以在多个 GPU 或多台机器上并行训练模型，从而缩短训练时间和提高模型性能；<br>
• 混合精度训练：可以使用半精度浮点数加速模型训练，从而减少 GPU 内存使用和提高训练速度；<br>
• 自动调参：可以使用 PyTorch Lightning Trainer 来自动调整超参数，从而提高模型性能；<br>
• 数据加载优化：可以使用 DataLoader 和 DataLoaderTransforms 来优化数据加载速度，从而减少训练时<br>
间；<br>
• 模型优化：可以使用 Apex 或 TorchScript 等工具来优化模型性能。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>4.2 accelerate 分布式训练 代码实现逻辑<br>
    $ pip install accelerate==0.17.1<br>
1. 导包<br>
    ...<br>
    from accelerate import Accelerator<br>
    ...<br>
2. Trainer 训练类 编写<br>
class Trainer:<br>
    def __init__(self,<br>
                 args,<br>
                 config,<br>
                 model_engine,<br>
                 criterion,<br>
                 optimizer,<br>
                 accelerator):<br>
        ...<br>
        self.accelerator = accelerator<br>
        ...<br>
    def train(self, train_loader, dev_loader=None):<br>
        ...<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model_engine.train()<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                self.accelerator.backward(loss)<br>
                self.optimizer.step()<br>
                self.optimizer.zero_grad()<br>
        ...<br>
3. main() 函数 编写<br>
def main():<br>
    ...<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    ...<br>
    accelerator = Accelerator()<br>
    args.local_rank = int(dist.get_rank())<br>
    print(args.local_rank)<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
</p>

<h2>第 3 页</h2>

<p>4.3 accelerate 分布式训练 示例代码<br>
    # =======================================<br>
    # 定义训练器<br>
    trainer = Trainer(args,<br>
                      config,<br>
                      model_engine,<br>
                      criterion,<br>
                      optimizer_engine,<br>
                      accelerator)<br>
    # 训练和验证<br>
    trainer.train(train_loader_engine, dev_loader_engine)<br>
    # 测试<br>
    ...<br>
    # 需要重新初始化引擎<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
    model_engine.load_state_dict(torch.load(args.ckpt_path))<br>
    report = trainer.test(model_engine, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
import json<br>
import time<br>
import random<br>
import torch<br>
import deepspeed<br>
import torch.nn as nn<br>
import numpy as np<br>
import torch.distributed as dist<br>
from sklearn.metrics import classification_report<br>
from accelerate import Accelerator<br>
from torch.utils.data import DataLoader<br>
from collections import Counter<br>
from transformers import BertForMaskedLM, BertTokenizer, <br>
BertForSequenceClassification, BertConfig, AdamW<br>
def set_seed(seed=123):<br>
    """<br>
    设置随机数种子，保证实验可重现<br>
    :param seed:<br>
    :return:<br>
</p>

<h2>第 4 页</h2>

<p>    """<br>
    random.seed(seed)<br>
    torch.manual_seed(seed)<br>
    np.random.seed(seed)<br>
    torch.cuda.manual_seed_all(seed)<br>
def get_data():<br>
    with open("data/train.json", "r", encoding="utf-8") as fp:<br>
        data = fp.read()<br>
    data = json.loads(data)<br>
    return data<br>
def load_data():<br>
    data = get_data()<br>
    return_data = []<br>
    # [(文本， 标签id)]<br>
    for d in data:<br>
        text = d[0]<br>
        label = d[1]<br>
        return_data.append(("".join(text.split(" ")).strip(), label))<br>
    return return_data<br>
class Collate:<br>
    def __init__(self,<br>
                 tokenizer,<br>
                 max_seq_len,<br>
                 ):<br>
        self.tokenizer = tokenizer<br>
        self.max_seq_len = max_seq_len<br>
    def collate_fn(self, batch):<br>
        input_ids_all = []<br>
        token_type_ids_all = []<br>
        attention_mask_all = []<br>
        label_all = []<br>
        for data in batch:<br>
            text = data[0]<br>
            label = data[1]<br>
            inputs = self.tokenizer.encode_plus(text=text,<br>
                                                max_length=self.max_seq_len,<br>
                                                padding="max_length",<br>
                                                truncation="longest_first",<br>
                                                return_attention_mask=True,<br>
                                                return_token_type_ids=True)<br>
            input_ids = inputs["input_ids"]<br>
            token_type_ids = inputs["token_type_ids"]<br>
            attention_mask = inputs["attention_mask"]<br>
</p>

<h2>第 5 页</h2>

<p>            input_ids_all.append(input_ids)<br>
            token_type_ids_all.append(token_type_ids)<br>
            attention_mask_all.append(attention_mask)<br>
            label_all.append(label)<br>
        input_ids_all = torch.tensor(input_ids_all, dtype=torch.long)<br>
        token_type_ids_all = torch.tensor(token_type_ids_all, dtype=torch.long)<br>
        attention_mask_all = torch.tensor(attention_mask_all, dtype=torch.long)<br>
        label_all = torch.tensor(label_all, dtype=torch.long)<br>
        return_data = {<br>
            "input_ids": input_ids_all,<br>
            "attention_mask": attention_mask_all,<br>
            "token_type_ids": token_type_ids_all,<br>
            "label": label_all<br>
        }<br>
        return return_data<br>
class Trainer:<br>
    def __init__(self,<br>
                 args,<br>
                 config,<br>
                 model_engine,<br>
                 criterion,<br>
                 optimizer,<br>
                 accelerator):<br>
        self.args = args<br>
        self.config = config<br>
        self.model_engine = model_engine<br>
        self.criterion = criterion<br>
        self.optimizer = optimizer<br>
        self.accelerator = accelerator<br>
    def on_step(self, batch_data):<br>
        label = batch_data["label"].cuda()<br>
        input_ids = batch_data["input_ids"].cuda()<br>
        token_type_ids = batch_data["token_type_ids"].cuda()<br>
        attention_mask = batch_data["attention_mask"].cuda()<br>
        output = self.model_engine.forward(input_ids=input_ids,<br>
                                           token_type_ids=token_type_ids,<br>
                                           attention_mask=attention_mask,<br>
                                           labels=label)<br>
        logits = output[1]<br>
        return logits, label<br>
    def loss_reduce(self, loss):<br>
        rt = loss.clone()<br>
        dist.all_reduce(rt, op=dist.ReduceOp.SUM)<br>
</p>

<h2>第 6 页</h2>

<p>        rt /= torch.cuda.device_count()<br>
        return rt<br>
    def output_reduce(self, outputs, targets):<br>
        output_gather_list = [torch.zeros_like(outputs) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(output_gather_list, outputs)<br>
        outputs = torch.cat(output_gather_list, dim=0)<br>
        target_gather_list = [torch.zeros_like(targets) for _ in <br>
range(torch.cuda.device_count())]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(target_gather_list, targets)<br>
        targets = torch.cat(target_gather_list, dim=0)<br>
        return outputs, targets<br>
    def train(self, train_loader, dev_loader=None):<br>
        gloabl_step = 1<br>
        best_acc = 0.<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model_engine.train()<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                self.accelerator.backward(loss)<br>
                self.optimizer.step()<br>
                self.optimizer.zero_grad()<br>
                loss = self.loss_reduce(loss)<br>
                if self.args.local_rank == 0:<br>
                    print("【train】 epoch：{}/{} step：{}/{} loss：{:.6f}".format(<br>
                        epoch, self.args.epochs, gloabl_step, self.args.total_step, <br>
loss<br>
                    ))<br>
                gloabl_step += 1<br>
                if self.args.dev:<br>
                    if gloabl_step % self.args.eval_step == 0:<br>
                        loss, accuracy = self.dev(dev_loader)<br>
                        if self.args.local_rank == 0:<br>
                            print("【dev】 loss：{:.6f} accuracy：<br>
{:.4f}".format(loss, accuracy))<br>
                            if accuracy &gt; best_acc:<br>
                                best_acc = accuracy<br>
                                print("【best accuracy】 {:.4f}".format(best_acc))<br>
</p>

<h2>第 7 页</h2>

<p>                                torch.save(self.model_engine.state_dict(), <br>
self.args.ckpt_path)<br>
        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev and self.args.local_rank == 0:<br>
            torch.save(self.model_engine.state_dict(), self.args.ckpt_path)<br>
    def dev(self, dev_loader):<br>
        self.model_engine.eval()<br>
        correct_total = 0<br>
        num_total = 0<br>
        loss_total = 0.<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(dev_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                loss = self.loss_reduce(loss)<br>
                logits, label = self.output_reduce(logits, label)<br>
                loss_total += loss<br>
                logits = logits.detach().cpu().numpy()<br>
                label = label.view(-1).detach().cpu().numpy()<br>
                num_total += len(label)<br>
                preds = np.argmax(logits, axis=1).flatten()<br>
                correct_num = (preds == label).sum()<br>
                correct_total += correct_num<br>
        return loss_total, correct_total / num_total<br>
    def test(self, model_engine, test_loader, labels):<br>
        self.model_engine = model_engine<br>
        self.model_engine.eval()<br>
        preds = []<br>
        trues = []<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(test_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                logits, label = self.output_reduce(logits, label)<br>
                label = label.view(-1).detach().cpu().numpy().tolist()<br>
                logits = logits.detach().cpu().numpy()<br>
                pred = np.argmax(logits, axis=1).flatten().tolist()<br>
                trues.extend(label)<br>
                preds.extend(pred)<br>
        # print(trues, preds, labels)<br>
        print(np.array(trues).shape, np.array(preds).shape)<br>
        report = classification_report(trues, preds, target_names=labels)<br>
</p>

<h2>第 8 页</h2>

<p>        return report<br>
def build_optimizer(model, args):<br>
    no_decay = ['bias', 'LayerNorm.weight']<br>
    optimizer_grouped_parameters = [<br>
        {'params': [p for n, p in model.named_parameters() if not any(nd in n for <br>
nd in no_decay)],<br>
         'weight_decay': args.weight_decay},<br>
        {'params': [p for n, p in model.named_parameters() if any(nd in n for nd in <br>
no_decay)],<br>
         'weight_decay': 0.0}<br>
    ]<br>
    # optimizer = AdamW(model.parameters(), lr=learning_rate)<br>
    optimizer = AdamW(optimizer_grouped_parameters, lr=args.learning_rate)<br>
    return optimizer<br>
class Args:<br>
    model_path = "model_hub/chinese-bert-wwm-ext"<br>
    ckpt_path = "output/accelerate/multi-gpu-accelerate-cls.pt"<br>
    max_seq_len = 128<br>
    ratio = 0.92<br>
    epochs = 1<br>
    eval_step = 50<br>
    dev = False<br>
    local_rank = None<br>
    train_batch_size = 32<br>
    dev_batch_size = 32<br>
    weight_decay = 0.01<br>
    learning_rate=3e-5<br>
def main():<br>
    # =======================================<br>
    # 定义相关参数<br>
    set_seed()<br>
    label2id = {<br>
        "其他": 0,<br>
        "喜好": 1,<br>
        "悲伤": 2,<br>
        "厌恶": 3,<br>
        "愤怒": 4,<br>
        "高兴": 5,<br>
    }<br>
    args = Args()<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
    # =======================================<br>
</p>

<h2>第 9 页</h2>

<p>    # =======================================<br>
    # 加载数据集<br>
    data = load_data()<br>
    # 取1万条数据出来<br>
    data = data[:10000]<br>
    random.shuffle(data)<br>
    train_num = int(len(data) * args.ratio)<br>
    train_data = data[:train_num]<br>
    dev_data = data[train_num:]<br>
    collate = Collate(tokenizer, args.max_seq_len)<br>
    train_loader = DataLoader(train_data,<br>
                              batch_size=args.train_batch_size,<br>
                              shuffle=True,<br>
                              num_workers=2,<br>
                              collate_fn=collate.collate_fn)<br>
    total_step = len(train_loader) * args.epochs //  torch.cuda.device_count()<br>
    args.total_step = total_step<br>
    dev_loader = DataLoader(dev_data,<br>
                            batch_size=args.dev_batch_size,<br>
                            shuffle=False,<br>
                            num_workers=2,<br>
                            collate_fn=collate.collate_fn)<br>
    test_loader = dev_loader<br>
    # =======================================<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
    criterion = torch.nn.CrossEntropyLoss()<br>
    optimizer = build_optimizer(model, args)<br>
    accelerator = Accelerator()<br>
    args.local_rank = int(dist.get_rank())<br>
    print(args.local_rank)<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
    # =======================================<br>
    # 定义训练器<br>
    trainer = Trainer(args,<br>
                      config,<br>
</p>

<h2>第 10 页</h2>

<p>4.3 accelerate 分布式训练 运行<br>
运行效果<br>
GPU 使用情况<br>
                      model_engine,<br>
                      criterion,<br>
                      optimizer_engine,<br>
                      accelerator)<br>
    # 训练和验证<br>
    trainer.train(train_loader_engine, dev_loader_engine)<br>
    # 测试<br>
    labels = list(label2id.keys())<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda()<br>
    # 需要重新初始化引擎<br>
    model_engine, optimizer_engine, train_loader_engine, dev_loader_engine = <br>
accelerator.prepare(<br>
        model, optimizer, train_loader, dev_loader<br>
    )<br>
    model_engine.load_state_dict(torch.load(args.ckpt_path))<br>
    report = trainer.test(model_engine, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
if __name__ == '__main__':<br>
    main()<br>
• 方式一：<br>
    $ accelerate launch multi-gpu-accelerate-cls.py<br>
• 方式二：<br>
    $ python -m torch.distributed.launch --nproc_per_node 2 --use_env multi-gpu-<br>
accelerate-cls.py<br>
【train】 epoch：1/1 step：1/144 loss：1.795169<br>
【train】 epoch：1/1 step：2/144 loss：1.744665<br>
【train】 epoch：1/1 step：3/144 loss：1.631625<br>
【train】 epoch：1/1 step：4/144 loss：1.543691<br>
【train】 epoch：1/1 step：5/144 loss：1.788955<br>
</p>

<h2>第 11 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:47</p>
        </div>
    </div>
</body>
</html>