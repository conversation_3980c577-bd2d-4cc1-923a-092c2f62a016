<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>49-图解分布式训练（五） —— AMP混合精度训练 详细解析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>49-图解分布式训练（五） —— AMP混合精度训练 详细解析</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图解分布式训练（五） —— AMP混合精度训练 详细解析<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 11:36<br>
为什么需要 AMP混合精度训练？<br>
PyTorch 1.6版本今天发布了，带来的最大更新就是自动混合精度。release说明的标题是：<br>
Stable release of automatic mixed precision (AMP).New Beta features include a TensorPipe backend for RPC, <br>
memory profiler, and several improvements to distributed training for both RPC and DDP.<br>
可见自动混合精度正是PyTorch 1.6的最大更新。这就带来了几个问题：<br>
一、什么是自动混合精度训练(AMP)<br>
我们知道神经网络框架的计算核心是Tensor，也就是那个从scaler -&gt; array -&gt; matrix -&gt; tensor 维度一路丰富过来<br>
的tensor。在PyTorch中，我们可以这样创建一个Tensor：<br>
可以看到默认创建的tensor都是FloatTensor类型。而在PyTorch中，一共有10种类型的tensor：<br>
由此可见，默认的Tensor是32-bit floating point，这就是32位浮点型精度的Tensor。<br>
自动混合精度的关键词有两个：自动、混合精度。这是由PyTorch 1.6的torch.cuda.amp模块带来的：<br>
混合精度预示着有不止一种精度的Tensor，那在PyTorch的AMP模块里是几种呢？2种：torch.FloatTensor和<br>
torch.HalfTensor；<br>
• 什么是自动混合精度训练？<br>
• 为什么需要自动混合精度？<br>
• 如何在PyTorch中使用自动混合精度？<br>
&gt;&gt;&gt; import torch<br>
&gt;&gt;&gt; gemfield = torch.zeros(70,30)<br>
&gt;&gt;&gt; gemfield.type()<br>
'torch.FloatTensor'<br>
&gt;&gt;&gt; syszux = torch.Tensor([1,2])<br>
&gt;&gt;&gt; syszux.type()<br>
'torch.FloatTensor'<br>
• torch.FloatTensor (32-bit floating point)<br>
• torch.DoubleTensor (64-bit floating point)<br>
• torch.HalfTensor (16-bit floating point 1)<br>
• torch.BFloat16Tensor (16-bit floating point 2)<br>
• torch.ByteTensor (8-bit integer (unsigned))<br>
• torch.CharTensor (8-bit integer (signed))<br>
• torch.ShortTensor (16-bit integer (signed))<br>
• torch.IntTensor (32-bit integer (signed))<br>
• torch.LongTensor (64-bit integer (signed))<br>
• torch.BoolTensor (Boolean)<br>
  from torch.cuda.amp import autocast as autocast<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>自动预示着Tensor的dtype类型会自动变化，也就是框架按需自动调整tensor的dtype（其实不是完全自动，有些<br>
地方还是需要手工干预）；<br>
torch.cuda.amp 的名字意味着这个功能只能在cuda上使用，事实上，这个功能正是NVIDIA的开发人员贡献到<br>
PyTorch项目中的。而只有支持Tensor core的CUDA硬件才能享受到AMP的好处（比如2080ti显卡）。Tensor <br>
Core是一种矩阵乘累加的计算单元，每个Tensor Core每个时钟执行64个浮点混合精度操作（FP16矩阵相乘和<br>
FP32累加），英伟达宣称使用Tensor Core进行矩阵运算可以轻易的提速，同时降低一半的显存访问和存储。<br>
因此，在PyTorch中，当我们提到自动混合精度训练，我们说的就是在NVIDIA的支持Tensor core的CUDA设备上<br>
使用torch.cuda.amp.autocast （以及torch.cuda.amp.GradScaler）来进行训练。咦？为什么还要有<br>
torch.cuda.amp.GradScaler？<br>
二、为什么需要自动混合精度？<br>
这个问题其实暗含着这样的意思：为什么需要自动混合精度，也就是torch.FloatTensor和torch.HalfTensor的混<br>
合，而不全是torch.FloatTensor？或者全是torch.HalfTensor？<br>
如果非要以这种方式问，那么答案只能是，在某些上下文中torch.FloatTensor有优势，在某些上下文中<br>
torch.HalfTensor有优势呗。答案进一步可以转化为，相比于之前的默认的torch.FloatTensor，torch.HalfTensor<br>
有时具有优势，有时劣势不可忽视。<br>
torch.HalfTensor的优势就是存储小、计算快、更好的利用CUDA设备的Tensor Core。因此训练的时候可以减少<br>
显存的占用（可以增加batchsize了），同时训练速度更快；<br>
torch.HalfTensor的劣势就是：数值范围小（更容易Overflow / Underflow）、舍入误差（Rounding Error，导致<br>
一些微小的梯度信息达不到16bit精度的最低分辨率，从而丢失）。<br>
可见，当有优势的时候就用torch.HalfTensor，而为了消除torch.HalfTensor的劣势，我们带来了两种解决方案：<br>
1. 梯度scale，这正是上一小节中提到的torch.cuda.amp.GradScaler，通过放大loss的值来防止梯度的<br>
underflow（这只是BP的时候传递梯度信息使用，真正更新权重的时候还是要把放大的梯度再unscale回<br>
去）；<br>
2. 回落到torch.FloatTensor，这就是混合一词的由来。那怎么知道什么时候用torch.FloatTensor，什么时候用半<br>
精度浮点型呢？这是PyTorch框架决定的，在PyTorch 1.6的AMP上下文中，如下操作中tensor会被自动转化<br>
为半精度浮点型的torch.HalfTensor：<br>
  __matmul__<br>
  addbmm<br>
  addmm<br>
  addmv<br>
  addr<br>
  baddbmm<br>
  bmm<br>
  chain_matmul<br>
  conv1d<br>
  conv2d<br>
  conv3d<br>
  conv_transpose1d<br>
  conv_transpose2d<br>
  conv_transpose3d<br>
  linear<br>
  matmul<br>
  mm<br>
  mv<br>
  prelu<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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**************************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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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***************************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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>三、混合精度训练的优点是什么？<br>
四、混合精度训练的缺点是什么？<br>
五、混合精度训练的关键技术是什么？<br>
六、介绍一下 混合精度训练 动态损失缩放？<br>
七、如何在PyTorch中使用自动混合精度？<br>
答案就是autocast + GradScaler。<br>
正如前文所说，需要使用torch.cuda.amp模块中的autocast 类。使用也是非常简单的：<br>
• 减少显存<br>
• 占用加快训练速度:通信量<br>
• 减半;计算性能翻倍。<br>
• 数据溢出<br>
• 舍入误差<br>
• float32主权重备份<br>
• 动态损失缩放<br>
• 介绍：即损失标度。这会将梯度也放大1024倍，大大降只需要将损失乘以某个大数字 (如1024)，低了梯度发<br>
生下溢的几率。计算出梯度后，只需将其除以1024就可以得到准确值。<br>
• 动态选择损失标度 介绍：发生溢出，跳过优化器更新，损失标度减半;连续N个steps没有发生溢出，损失标<br>
度翻倍<br>
1. autocast<br>
from torch.cuda.amp import autocast as autocast<br>
# 创建model，默认是torch.FloatTensor<br>
model = Net().cuda()<br>
optimizer = optim.SGD(model.parameters(), ...)<br>
</p>

<h2>第 4 页</h2>

<p>可以使用autocast的context managers语义（如上所示），也可以使用decorators语义。 当进入autocast的上下<br>
文后，上面列出来的那些CUDA ops 会把tensor的dtype转换为半精度浮点型，从而在不损失训练精度的情况下加<br>
快运算。刚进入autocast的上下文时，tensor可以是任何类型，你不要在model或者input上手工调用.half() ，框架<br>
会自动做，这也是自动混合精度中“自动”一词的由来。<br>
另外一点就是，autocast上下文应该只包含网络的前向过程（包括loss的计算），而不要包含反向传播，因为BP<br>
的op会使用和前向op相同的类型。<br>
还有的时候呀，你的代码在autocast上下文中会报如下的错误：<br>
对于RuntimeError: expected scalar type float but found c10::Half，这估计是个bug。你可以在tensor上手工调<br>
用.float()来让type匹配。<br>
但是别忘了前面提到的梯度scaler模块呀，需要在训练最开始之前实例化一个GradScaler对象。因此PyTorch中<br>
经典的AMP使用方式如下：<br>
for input, target in data:<br>
    optimizer.zero_grad()<br>
    # 前向过程(model + loss)开启 autocast<br>
    with autocast():<br>
        output = model(input)<br>
        loss = loss_fn(output, target)<br>
    # 反向传播在autocast上下文之外<br>
    loss.backward()<br>
    optimizer.step()<br>
Traceback (most recent call last):<br>
......<br>
  File "/opt/conda/lib/python3.7/site-packages/torch/nn/modules/module.py", line <br>
722, in _call_impl<br>
    result = self.forward(*input, **kwargs)<br>
......<br>
RuntimeError: expected scalar type float but found c10::Half<br>
1. GradScaler<br>
from torch.cuda.amp import autocast as autocast<br>
# 创建model，默认是torch.FloatTensor<br>
model = Net().cuda()<br>
optimizer = optim.SGD(model.parameters(), ...)<br>
# 在训练最开始之前实例化一个GradScaler对象<br>
scaler = GradScaler()<br>
for epoch in epochs:<br>
    for input, target in data:<br>
        optimizer.zero_grad()<br>
        # 前向过程(model + loss)开启 autocast<br>
        with autocast():<br>
</p>

<h2>第 5 页</h2>

<p>scaler的大小在每次迭代中动态的估计，为了尽可能的减少梯度underflow，scaler应该更大；但是如果太大的<br>
话，半精度浮点型的tensor又容易overflow（变成inf或者NaN）。所以动态估计的原理就是在不出现inf或者NaN<br>
梯度值的情况下尽可能的增大scaler的值——在每次scaler.step(optimizer)中，都会检查是否又inf或NaN的梯度出<br>
现：<br>
八、如何使用 AMP混合精度训练 ？<br>
8.1 AMP混合精度训练 代码<br>
            output = model(input)<br>
            loss = loss_fn(output, target)<br>
        # Scales loss. 为了梯度放大.<br>
        scaler.scale(loss).backward()<br>
        # scaler.step() 首先把梯度的值unscale回来.<br>
        # 如果梯度的值不是 infs 或者 NaNs, 那么调用optimizer.step()来更新权重,<br>
        # 否则，忽略step调用，从而保证权重不更新（不被破坏）<br>
        scaler.step(optimizer)<br>
        # 准备着，看是否要增大scaler<br>
        scaler.update()<br>
• 如果出现了inf或者NaN，scaler.step(optimizer)会忽略此次的权重更新（optimizer.step() )，并且将scaler的<br>
大小缩小（乘上backoff_factor）；<br>
• 如果没有出现inf或者NaN，那么权重正常更新，并且当连续多次（growth_interval指定）没有出现inf或者<br>
NaN，则scaler.update()会将scaler的大小增加（乘上growth_factor）。<br>
1. Trainer 训练类<br>
  class Trainer:<br>
    ...<br>
     def train(self, train_loader, dev_loader=None, train_sampler=None):<br>
        ...<br>
        # 设置 AMP混合精度训练<br>
        if self.args.use_amp:<br>
            scaler = torch.cuda.amp.GradScaler()<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            train_sampler.set_epoch(epoch)<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model.train()<br>
                # 使用 AMP混合精度训练 <br>
                if self.args.use_amp:<br>
                    with torch.cuda.amp.autocast():<br>
                        logits, label = self.on_step(batch_data)<br>
                        loss = self.criterion(logits, label)<br>
                        torch.distributed.barrier()<br>
                        scaler.scale(loss).backward()<br>
                        scaler.step(self.optimizer)<br>
                        scaler.update()<br>
</p>

<h2>第 6 页</h2>

<p>                else:<br>
                    logits, label = self.on_step(batch_data)<br>
                    loss = self.criterion(logits, label)<br>
                    torch.distributed.barrier()<br>
                    loss.backward()<br>
                    self.optimizer.step()<br>
                ...<br>
        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev and self.args.local_rank == 0:<br>
            torch.save(self.model.state_dict(), self.args.ckpt_path)<br>
2. Args 参数类<br>
class Args:<br>
    ...<br>
    local_rank = None<br>
    local_world_size = None<br>
    device_ids = None<br>
    rank = None<br>
    dev = False<br>
    use_amp = True<br>
3. main_worker 主函数<br>
def main_worker(local_rank, local_world_size):<br>
    # =======================================<br>
    # 设置参数<br>
    ...<br>
    dist.init_process_group(backend="nccl", init_method="tcp://localhost:12345", <br>
world_size=local_world_size, rank=local_rank)<br>
    n = torch.cuda.device_count() // local_world_size<br>
    device_ids = [local_rank]<br>
    print(<br>
        f"[{os.getpid()}] rank = {local_rank}, "<br>
        + f"world_size = {local_world_size}, n = {n}, device_ids = {device_ids} <br>
\n", end=''<br>
    )<br>
    torch.cuda.set_device(local_rank)<br>
    args = Args()<br>
    args.local_world_size = local_world_size<br>
    args.local_rank = local_rank<br>
    args.device_ids = device_ids<br>
    args.rank = local_rank<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
</p>

<h2>第 7 页</h2>

<p>8.2 AMP混合精度训练 完整代码<br>
    ...<br>
    # 第三步：封装模型<br>
    model.cuda()<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    ...<br>
    model.cuda(args.local_rank)<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    ...<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
    # =======================================<br>
    dist.destroy_process_group()<br>
    # =======================================<br>
import os<br>
import time<br>
import json<br>
import random<br>
import torch<br>
import torch.nn as nn<br>
import numpy as np<br>
import torch.distributed as dist<br>
import torch.multiprocessing as mp<br>
from collections import Counter<br>
from tqdm import tqdm<br>
from sklearn.metrics import classification_report<br>
from torch.utils.data import DataLoader, Dataset<br>
from transformers import BertForMaskedLM, BertTokenizer, <br>
BertForSequenceClassification, BertConfig, AdamW<br>
def set_seed(seed=123):<br>
    """<br>
    设置随机数种子，保证实验可重现<br>
    :param seed:<br>
    :return:<br>
    """<br>
    random.seed(seed)<br>
    torch.manual_seed(seed)<br>
    np.random.seed(seed)<br>
</p>

<h2>第 8 页</h2>

<p>    torch.cuda.manual_seed_all(seed)<br>
def get_data():<br>
    with open("data/train.json", "r", encoding="utf-8") as fp:<br>
        data = fp.read()<br>
    data = json.loads(data)<br>
    return data<br>
def load_data():<br>
    data = get_data()<br>
    return_data = []<br>
    # [(文本， 标签id)]<br>
    for d in data:<br>
        text = d[0]<br>
        label = d[1]<br>
        return_data.append(("".join(text.split(" ")).strip(), label))<br>
    return return_data<br>
class ClsDataset(Dataset):<br>
    def __init__(self, data):<br>
        self.data = data<br>
    def __len__(self):<br>
        return len(self.data)<br>
    def __getitem__(self, index):<br>
        return self.data[index]<br>
class Collate:<br>
    def __init__(self,<br>
                 tokenizer,<br>
                 max_seq_len,<br>
                 ):<br>
        self.tokenizer = tokenizer<br>
        self.max_seq_len = max_seq_len<br>
    def collate_fn(self, batch):<br>
        input_ids_all = []<br>
        token_type_ids_all = []<br>
        attention_mask_all = []<br>
        label_all = []<br>
        for data in batch:<br>
            text = data[0]<br>
            label = data[1]<br>
            inputs = self.tokenizer.encode_plus(text=text,<br>
                                                max_length=self.max_seq_len,<br>
                                                padding="max_length",<br>
</p>

<h2>第 9 页</h2>

<p>                                                truncation="longest_first",<br>
                                                return_attention_mask=True,<br>
                                                return_token_type_ids=True)<br>
            input_ids = inputs["input_ids"]<br>
            token_type_ids = inputs["token_type_ids"]<br>
            attention_mask = inputs["attention_mask"]<br>
            input_ids_all.append(input_ids)<br>
            token_type_ids_all.append(token_type_ids)<br>
            attention_mask_all.append(attention_mask)<br>
            label_all.append(label)<br>
        input_ids_all = torch.tensor(input_ids_all, dtype=torch.long)<br>
        token_type_ids_all = torch.tensor(token_type_ids_all, dtype=torch.long)<br>
        attention_mask_all = torch.tensor(attention_mask_all, dtype=torch.long)<br>
        label_all = torch.tensor(label_all, dtype=torch.long)<br>
        return_data = {<br>
            "input_ids": input_ids_all,<br>
            "attention_mask": attention_mask_all,<br>
            "token_type_ids": token_type_ids_all,<br>
            "label": label_all<br>
        }<br>
        return return_data<br>
def build_optimizer(model, args):<br>
    no_decay = ['bias', 'LayerNorm.weight']<br>
    optimizer_grouped_parameters = [<br>
        {'params': [p for n, p in model.named_parameters() if not any(nd in n for <br>
nd in no_decay)],<br>
         'weight_decay': args.weight_decay},<br>
        {'params': [p for n, p in model.named_parameters() if any(nd in n for nd in <br>
no_decay)],<br>
         'weight_decay': 0.0}<br>
    ]<br>
    # optimizer = AdamW(model.parameters(), lr=learning_rate)<br>
    optimizer = AdamW(optimizer_grouped_parameters, lr=args.learning_rate)<br>
    return optimizer<br>
class Trainer:<br>
    def __init__(self,<br>
                 args,<br>
                 config,<br>
                 model,<br>
                 criterion,<br>
                 optimizer):<br>
        self.args = args<br>
        self.config = config,<br>
</p>

<h2>第 10 页</h2>

<p>        self.model = model<br>
        self.criterion = criterion<br>
        self.optimizer = optimizer<br>
    def on_step(self, batch_data):<br>
        label = batch_data["label"].cuda()<br>
        input_ids = batch_data["input_ids"].cuda()<br>
        token_type_ids = batch_data["token_type_ids"].cuda()<br>
        attention_mask = batch_data["attention_mask"].cuda()<br>
        output = self.model(input_ids=input_ids,<br>
                            token_type_ids=token_type_ids,<br>
                            attention_mask=attention_mask,<br>
                            labels=label)<br>
        logits = output[1]<br>
        return logits, label<br>
    def loss_reduce(self, loss):<br>
        rt = loss.clone()<br>
        dist.all_reduce(rt, op=dist.ReduceOp.SUM)<br>
        rt /= self.args.local_world_size<br>
        return rt<br>
    def output_reduce(self, outputs, targets):<br>
        output_gather_list = [torch.zeros_like(outputs) for _ in <br>
range(self.args.local_world_size)]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(output_gather_list, outputs)<br>
        outputs = torch.cat(output_gather_list, dim=0)<br>
        target_gather_list = [torch.zeros_like(targets) for _ in <br>
range(self.args.local_world_size)]<br>
        # 把每一个GPU的输出聚合起来<br>
        dist.all_gather(target_gather_list, targets)<br>
        targets = torch.cat(target_gather_list, dim=0)<br>
        return outputs, targets<br>
    def train(self, train_loader, dev_loader=None, train_sampler=None):<br>
        gloabl_step = 1<br>
        best_acc = 0.<br>
        if self.args.use_amp:<br>
            scaler = torch.cuda.amp.GradScaler()<br>
        if self.args.local_rank == 0:<br>
            start = time.time()<br>
        for epoch in range(1, self.args.epochs + 1):<br>
            train_sampler.set_epoch(epoch)<br>
            for step, batch_data in enumerate(train_loader):<br>
                self.model.train()<br>
</p>

<h2>第 11 页</h2>

<p>                if self.args.use_amp:<br>
                    with torch.cuda.amp.autocast():<br>
                        logits, label = self.on_step(batch_data)<br>
                        loss = self.criterion(logits, label)<br>
                        torch.distributed.barrier()<br>
                        scaler.scale(loss).backward()<br>
                        scaler.step(self.optimizer)<br>
                        scaler.update()<br>
                else:<br>
                    logits, label = self.on_step(batch_data)<br>
                    loss = self.criterion(logits, label)<br>
                    torch.distributed.barrier()<br>
                    loss.backward()<br>
                    self.optimizer.step()<br>
                if self.args.local_rank == 0:<br>
                    print("【train】 epoch：{}/{} step：{}/{} loss：{:.6f}".format(<br>
                        epoch, self.args.epochs, gloabl_step, self.args.total_step, <br>
loss<br>
                    ))<br>
                gloabl_step += 1<br>
                if self.args.dev:<br>
                    if gloabl_step % self.args.eval_step == 0:<br>
                        loss, accuracy = self.dev(dev_loader)<br>
                        if self.args.local_rank == 0:<br>
                            print("【dev】 loss：{:.6f} accuracy：<br>
{:.4f}".format(loss, accuracy))<br>
                            if accuracy &gt; best_acc:<br>
                                best_acc = accuracy<br>
                                print("【best accuracy】 {:.4f}".format(best_acc))<br>
                                torch.save(self.model.state_dict(), <br>
self.args.ckpt_path)<br>
        if self.args.local_rank == 0:<br>
            end = time.time()<br>
            print("耗时：{}分钟".format((end - start) / 60))<br>
        if not self.args.dev and self.args.local_rank == 0:<br>
            torch.save(self.model.state_dict(), self.args.ckpt_path)<br>
    def dev(self, dev_loader):<br>
        self.model.eval()<br>
        correct_total = 0<br>
        num_total = 0<br>
        loss_total = 0.<br>
        with torch.no_grad():<br>
            for step, batch_data in tqdm(enumerate(dev_loader)):<br>
                logits, label = self.on_step(batch_data)<br>
                loss = self.criterion(logits, label)<br>
                torch.distributed.barrier()<br>
</p>

<h2>第 12 页</h2>

<p>                loss = self.loss_reduce(loss)<br>
                loss_total += loss<br>
                logits, label = self.output_reduce(logits, label)<br>
                logits = logits.detach().cpu().numpy()<br>
                label = label.view(-1).detach().cpu().numpy()<br>
                num_total += len(label)<br>
                preds = np.argmax(logits, axis=1).flatten()<br>
                correct_num = (preds == label).sum()<br>
                correct_total += correct_num<br>
        return loss_total, correct_total / num_total<br>
    def test(self, model, test_loader, labels):<br>
        self.model = model<br>
        self.model.eval()<br>
        preds = []<br>
        trues = []<br>
        with torch.no_grad():<br>
            for step, batch_data in enumerate(test_loader):<br>
                logits, label = self.on_step(batch_data)<br>
                torch.distributed.barrier()<br>
                logits, label = self.output_reduce(logits, label)<br>
                label = label.view(-1).detach().cpu().numpy().tolist()<br>
                logits = logits.detach().cpu().numpy()<br>
                pred = np.argmax(logits, axis=1).flatten().tolist()<br>
                trues.extend(label)<br>
                preds.extend(pred)<br>
        report = classification_report(trues, preds, target_names=labels)<br>
        return report<br>
class Args:<br>
    model_path = "/mnt/kaimo/data/pretrain/bert-base-chinese"<br>
    ckpt_path = "output/multi-gpu-distributed-mp-amp-cls.pt"<br>
    max_seq_len = 128<br>
    ratio = 0.92<br>
    device = torch.device("cuda" if torch.cuda.is_available else "cpu")<br>
    train_batch_size = 32<br>
    dev_batch_size = 32<br>
    weight_decay = 0.01<br>
    epochs = 1<br>
    learning_rate = 3e-5<br>
    eval_step = 50<br>
    local_rank = None<br>
    local_world_size = None<br>
    device_ids = None<br>
    rank = None<br>
    dev = False<br>
</p>

<h2>第 13 页</h2>

<p>    use_amp = True<br>
def main_worker(local_rank, local_world_size):<br>
    # =======================================<br>
    # 设置参数<br>
    set_seed()<br>
    label2id = {<br>
        "其他": 0,<br>
        "喜好": 1,<br>
        "悲伤": 2,<br>
        "厌恶": 3,<br>
        "愤怒": 4,<br>
        "高兴": 5,<br>
    }<br>
    dist.init_process_group(backend="nccl", init_method="tcp://localhost:12345", <br>
world_size=local_world_size,<br>
                            rank=local_rank)<br>
    n = torch.cuda.device_count() // local_world_size<br>
    device_ids = [local_rank]<br>
    print(<br>
        f"[{os.getpid()}] rank = {local_rank}, "<br>
        + f"world_size = {local_world_size}, n = {n}, device_ids = {device_ids} <br>
\n", end=''<br>
    )<br>
    torch.cuda.set_device(local_rank)<br>
    args = Args()<br>
    args.local_world_size = local_world_size<br>
    args.local_rank = local_rank<br>
    args.device_ids = device_ids<br>
    args.rank = local_rank<br>
    tokenizer = BertTokenizer.from_pretrained(args.model_path)<br>
    # =======================================<br>
    # =======================================<br>
    # 加载数据集<br>
    data = load_data()<br>
    # 取1万条数据出来<br>
    data = data[:10000]<br>
    random.shuffle(data)<br>
    train_num = int(len(data) * args.ratio)<br>
    train_data = data[:train_num]<br>
    dev_data = data[train_num:]<br>
    collate = Collate(tokenizer, args.max_seq_len)<br>
</p>

<h2>第 14 页</h2>

<p>    train_dataset = ClsDataset(train_data)<br>
    train_sampler = torch.utils.data.distributed.DistributedSampler(train_dataset)<br>
    train_loader = DataLoader(train_dataset,<br>
                              batch_size=args.train_batch_size,<br>
                              num_workers=2,<br>
                              collate_fn=collate.collate_fn,<br>
                              sampler=train_sampler)<br>
    total_step = len(train_loader) * args.epochs<br>
    args.total_step = total_step<br>
    dev_dataset = ClsDataset(dev_data)<br>
    dev_sampler = torch.utils.data.distributed.DistributedSampler(dev_dataset)<br>
    dev_loader = DataLoader(dev_dataset,<br>
                            batch_size=args.dev_batch_size,<br>
                            shuffle=False,<br>
                            num_workers=2,<br>
                            collate_fn=collate.collate_fn,<br>
                            sampler=dev_sampler)<br>
    test_loader = dev_loader<br>
    # =======================================<br>
    # =======================================<br>
    # 定义模型、优化器、损失函数<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path,<br>
                                                          config=config)<br>
    # 第三步：封装模型<br>
    model.cuda()<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    criterion = torch.nn.CrossEntropyLoss()<br>
    optimizer = build_optimizer(model, args)<br>
    # =======================================<br>
    # =======================================<br>
    # 定义训练器，进行训练、验证和测试<br>
    trainer = Trainer(args,<br>
                      config,<br>
                      model,<br>
                      criterion,<br>
                      optimizer)<br>
    trainer.train(train_loader, dev_loader, train_sampler)<br>
    labels = list(label2id.keys())<br>
    config = BertConfig.from_pretrained(args.model_path, num_labels=6)<br>
</p>

<h2>第 15 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
    model = BertForSequenceClassification.from_pretrained(args.model_path, <br>
config=config)<br>
    model.cuda(args.local_rank)<br>
    model = torch.nn.parallel.DistributedDataParallel(model, <br>
device_ids=args.device_ids)<br>
    model.load_state_dict(torch.load(args.ckpt_path))<br>
    report = trainer.test(model, test_loader, labels)<br>
    if args.local_rank == 0:<br>
        print(report)<br>
    # =======================================<br>
    # =======================================<br>
    dist.destroy_process_group()<br>
    # =======================================<br>
if __name__ == '__main__':<br>
    import argparse<br>
    parser = argparse.ArgumentParser()<br>
    parser.add_argument("--local_world_size", type=int, default=1)<br>
    p_args = parser.parse_args()<br>
    # 第零步：启动进程<br>
    mp.spawn(main_worker, nprocs=p_args.local_world_size, args=<br>
(p_args.local_world_size,))<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:44</p>
        </div>
    </div>
</body>
</html>