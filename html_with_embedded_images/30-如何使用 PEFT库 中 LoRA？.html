<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>30-如何使用 PEFT库 中 LoRA？</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>30-如何使用 PEFT库 中 LoRA？</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>如何使用 PEFT库 中 LoRA？<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月28日 10:12<br>
一、前言<br>
本文章 主要介绍 使用 LoRA 对 大模型进行 高效参数微调，涉及内容：<br>
涉及框架<br>
• 如何使用 PEFT库 中 LoRA？<br>
• 一、前言<br>
• 二、如何 配置 LoraConfig？<br>
• 三、模型 加入PEFT策略<br>
• 3.1 模型加载 策略有哪些？<br>
• 3.2 模型显存占用的部分有哪些？<br>
• 3.3 模型显存占用 优化策略？<br>
• 3.3.1 8bit量化 优化策略？<br>
• 3.3.2 梯度检查 优化策略？<br>
• 3.4 如何 向 模型 加入PEFT策略？<br>
• 四、PEFT库 中 LoRA 模块 代码介绍<br>
• 4.1 PEFT库 中 LoRA 模块 整体实现思路<br>
• 4.2 PEFT库 中 LoRA 模块 _find_and_replace() 实现思路<br>
• 4.3 PEFT库 中 Lora层的 实现思路<br>
• 4.3.1 基类 LoraLayer 实现<br>
• 4.3.2 Linear 实现<br>
• 五、使用 LoRA 对 大模型进行 高效参数微调，如何进行存储？<br>
• 六、使用 LoRA 对 大模型进行 推理，如何进行加载？<br>
• 七、huggingface大模型如何加载多个LoRA并随时切换？<br>
• 参考<br>
1. PEFT库 中 LoRA 模块使用；<br>
2. PEFT库 中 LoRA 模块 代码介绍；<br>
3. 在推理时如何先进行weight的合并在加载模型进行推理；<br>
# 以下配置可能会随时间变化，出了问题就去issue里面刨吧<br>
# 要相信你不是唯一一个大冤种！<br>
accelerate<br>
appdirs<br>
loralib<br>
bitsandbytes<br>
black<br>
black[jupyter]<br>
datasets<br>
fire<br>
transformers&gt;=4.28.0<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>二、如何 配置 LoraConfig？<br>
注意：target_modules中的作用目标名在不同模型中的名字是不一样的。query_key_value是在<br>
ChatGLM中的名字<br>
三、模型 加入PEFT策略<br>
3.1 模型加载 策略有哪些？<br>
模型加载虽然很简单，这里涉及到2个时间换空间的大模型显存压缩技巧，主要说下load_in_8bit和<br>
prepare_model_for_int8_training。<br>
git+https://github.com/huggingface/peft.git<br>
sentencepiece<br>
gradio<br>
wandb<br>
cpm-kernel<br>
  # 设置超参数及配置<br>
  LORA_R = 8<br>
  LORA_ALPHA = 16<br>
  LORA_DROPOUT = 0.05<br>
  TARGET_MODULES = [<br>
      "q_proj",<br>
      "v_proj",<br>
  ]<br>
  config = LoraConfig(<br>
      r=LORA_R,<br>
      lora_alpha=LORA_ALPHA,<br>
      target_modules=TARGET_MODULES,<br>
      lora_dropout=LORA_DROPOUT,<br>
      bias="none",<br>
      task_type="CAUSAL_LM",<br>
  )<br>
• 参数介绍：<br>
• r：lora的秩，矩阵A和矩阵B相连接的宽度，r&lt;&lt;d；<br>
• lora_alpha：归一化超参数，lora参数 ΔWx 被以 α/r 归一化，以便减少改变r rr时需要重新训练的计算<br>
量；<br>
• target_modules：lora的目标位置；<br>
• merge_weights:eval模式中，是否将lora矩阵的值加到原有 W0 的值上;<br>
• lora_dropout：lora层的dropout比率；<br>
• fan_in_fan_out：只有应用在Conv1D层时置为True，其他情况False；<br>
• bias: 是否可训练bias，none：均不可；all：均可；lora_only：只有lora部分的bias可训练；<br>
• task_type：这是LoraConfig的父类PeftConfig中的参数，设定任务的类型；<br>
• modules_to_save：除了lora部分之外，还有哪些层可以被训练，并且需要保存；<br>
</p>

<h2>第 3 页</h2>

<p>3.2 模型显存占用的部分有哪些？<br>
这里需要介绍一下 两个模型显存占用的部分：<br>
3.3 模型显存占用 优化策略？<br>
模型显存占用 有以下两种方式：<br>
3.3.1 8bit量化 优化策略？<br>
参考：https://huggingface.co/blog/hf-bitsandbytes-integration<br>
from_pretrained中的load_in_8bit参数是bitsandbytes库赋予的能力，会把加载模型转化成混合8bit的量化模型，<br>
注意这里的8bit模型量化只用于模型推理，通过量化optimizer state降低训练时显存的时8bit优化器是另一个功能<br>
不要搞混哟~<br>
模型量化本质是对浮点参数进行压缩的同时，降低压缩带来的误差。 8-bit quantization是把原始FP32（4字节）<br>
压缩到Int8（1字节）也就是1/4的显存占用。如上加载后会发现除lora层外的多数层被转化成int类型如下<br>
    from peft import get_peft_model, LoraConfig, prepare_model_for_int8_training, <br>
set_peft_model_state_dict<br>
    from transformers import AutoTokenizer, AutoModel<br>
    model = AutoModel.from_pretrained(<br>
        "THUDM/chatglm3-6b", load_in_8bit=True, torch_dtype=torch.float16, <br>
trust_remote_code=True, device_map="auto"<br>
    )<br>
    tokenizer = AutoTokenizer.from_pretrained(<br>
        "THUDM/chatglm3-6b", trust_remote_code=True<br>
    )<br>
    model = prepare_model_for_int8_training(model)<br>
1. 静态显存基本由模型参数量级决定；<br>
2. 动态显存在向前传播的过程中每个样本的每个神经元都会计算激活值并存储，用于向后传播时的梯度计算，<br>
这部分和batchsize以及参数量级相关；<br>
1. 8bit量化优化。该方式只要用于优化 静态显存；<br>
2. 梯度检查优化。该方式只要用于优化 动态显存；<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>当然压缩方式肯定不是直接四舍五入，那样会带来巨大的精度压缩损失。常见的量化方案有absolute-maximum<br>
和zero-point，它们的差异只是rescale的方式不同，这里简单说下absmax，如下<br>
先寻找tensor矩阵的绝对值的最大值，并计算最大值到127的缩放因子，然后使用该缩放因子对整个tensor进行缩<br>
放后，再round到整数。这样就把浮点数映射到了INT8,逆向回到float的原理相同。<br>
当然以上的缩放方案依旧存在精度损失，以及当矩阵中存在outlier时，这个精度损失会被放大，例如当tensor中<br>
绝大部分取值在1以下，有几个值在100+，则缩放后，所有1以下的tensor信息都会被round抹去。因此LLM.int8()<br>
的实现对outlier做了进一步的优化，把outlier和非outlier的矩阵分开计算，再把结果进行合并来降低outlier对精度<br>
的影响<br>
</p>

<h2>第 5 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAlgAAAEsCAIAAACQX1rBAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAB7dJREFUeJzt3UFqY0kaRlHtfylekRcjcE7TmQrsEBEi9O6BM/i43WS7ugavCPNTt/v9437/ekTXdV3Xr99vg/82ACT4EAKQdvv8fPwf6Lqu63qh376/mdq2bdt2a3saBSDNhxCAtG8fwtPebXVd13V9d//nd4R/03Vd1/Xrd0+jAKT5EAKQ5o5Q13VdT3d3hLZt23Z6exoFIM2HEIA0d4S6rut6ursj1HVd19Pd0ygAaT6EAKS5I9R1XdfT3R2hbdu2nd6eRgFI8yEEIM0doa7rup7u7gh1Xdf1dPc0CkCaDyEAae4IdV3X9XR3R2jbtm2nt6dRANJ8CAFIc0eo67qup7s7Ql3XdT3dPY0CkOZDCECaO0Jd13U93d0R2rZt2+ntaRSANB9CANLcEeq6ruvp7o5Q13VdT3dPowCk+RACkOaOUNd1XU93d4S2bdt2ensaBSDNhxCANHeEuq7rerq7I9R1XdfT3dMoAGk+hACkuSPUdV3X090doW3btp3enkYBSPMhBCDNHaGu67qe7u4IdV3X9XT3NApAmg8hAGnuCHVd1/V0d0do27Ztp7enUQDSfAgBSHNHqOu6rqe7O0Jd13U93T2NApDmQwhAmjtCXdd1Pd3dEdq2bdvp7WkUgDQfQgDS3BHquq7r6e6OUNd1XU93T6MApPkQApDmjnCiz24/v67r+vndHaFt27ad3p5GAUjzIQQgzR2hruu6nu7uCHVd1/V09zQKQJoPIQBp7ghDfbRP+zl1Xddf2d0R2rZt2+ntaRSANB9CANLcEeq6ruvp7o4w1Uf7tJ9T13X9dd3TKABpPoQApLkj1HVd19PdHaFt27ad3p5GAUjzIQQgzR3hD33Vvuqfr+u6/u7dHeHDPtqn/ZyzP//sPu2vS9d1fX33NApAmg8hAGnuCHVd1/V0d0do27Ztp/fip9HR9xYAzuR3hACkbb8jHO3d/7v+unRd1/Xf9E13hKO96s/X/X3RdV1f0zc+jfp9IQDn8ztCANK23BH6/dmZ3d8XXdf1/7kjtG3bttPbHSEAaX5HCEDasjtCv3/SdV3X37G/+N9HONq7/3d1Xdd1/XH3NApAmg8hAGn+fYQP+qp91T9f13X9St0doW3btp3enkYBSPMhBCBt+7+PUNd1XddP7i++I9R1Xdf1s7qnUQDSfAgBSHNHGOqz+7SfX9d1fUd3R2jbtm2nt6dRANJ8CAFIc0f4ZPd7NV3X9Wt0d4RTfbRP+zlnf/53/+vSdV1/vnsaBSDNhxCANHeEuq7rerq7I7Rt27bT29MoAGk+hACkuSP8oa/aV/3zdV3X3727I9R1XdfT3dMoAGk+hACkuSPUdV3X090doW3btp3enkYBSPMhBCDNHaGu67qe7u4IdV3X9XT3NApAmg8hAGnuCHVd1/V0d0do27Ztp7enUQDSfAgBSHNHqOu6rqe7O0Jd13U93T2NApDmQwhAmjtCXdd1Pd3dEdq2bdvp7WkUgDQfQgDS3BHquq7r6e6OUNd1XU93T6Pbjf55BIAT+BACkOaOcKLP7lV/zqp92v+fuq7rJ3R3hLZt23Z6exr9ld3/PLLq5wFglg8hAGnuCJ/sfg+n67p+je6OUNd1XU93T6MApPkQApDmjlDXdV1Pd3eEtm3bdnp7GgUgzYcQgDR3hLqu63q6uyPUdV3X093TKABpPoQApLkj1HVd19PdHaFt27ad3p5GAUjzIQQgzR2hruu6nu7uCHVd1/V09zQKQJoPIQBp7gh1Xdf1dHdHaNu2bae3p1EA0nwIAUhzR6jruq6nuztCXdd1Pd09jQKQ5kMIQJo7Ql3XdT3d3RHatm3b6e1pFIA0H0IA0twR6rqu6+nujlDXdV1Pd0+jAKT5EAKQ5o5Q13VdT3d3hLZt23Z6exoFIM2HEIA0d4S6rut6ursj1HVd19Pd0ygAaT6EAKS5I9R1XdfT3R2hbdu2nd6eRgFI8yEEIM0doa7rup7u7gh1Xdf1dPc0CkCaDyEAae4IdV3X9XR3R2jbtm2nt6dRANJ8CAFIc0eo67qup7s7Ql3XdT3dPY0CkOZDCECaO0Jd13U93d0R2rZt2+ntaRSANB9CANLcEeq6ruvp7o5Q13VdT3dPowCk+RACkOaOUNd1XU93d4S2bdt2ensaBSDNhxCANHeEuq7rerq7I9R1XdfT3dMoAGk+hACkuSPUdV3X090doW3btp3enkYBSPMhBCDNHaGu67qe7u4IdV3X9XT3NApAmg8hAGnuCHVd1/V0d0do27Ztp7enUQDSfAgBSHNHqOu6rqe7O0Jd13U93T2NApDmQwhAmjtCXdd1Pd3dEdq2bdvp7WkUgDQfQgDS3BHquq7r6e6OUNd1XU93T6MApPkQApDmjlDXdV1Pd3eEtm3bdnp7GgUgzYcQgDR3hLqu63q6uyPUdV3X093TKABpPoQApLkj1HVd19PdHaFt27ad3p5GAUjzIQQgzR2hruu6nu7uCHVd1/V09zQKQJoPIQBp7gh1Xdf1dHdHaNu2bae3p1EA0nwIAUhzR6jruq6nuztCXdd1Pd09jQKQ5kMIQJo7Ql3XdT3d3RHatm3b6e1pFIA0H0IA0twR6rqu6+nujlDXdV1Pd0+jAKT5EAKQ5o5Q13VdT/c/s3f6N8Hcs3UAAAAASUVORK5CYII=" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>prepare_model_for_int8_training是对在Lora微调中使用LLM.int8()进行了适配用来提高训练的稳定性，主要包括<br>
3.3.2 梯度检查 优化策略？<br>
参考：https://medium.com/tensorflow/fitting-larger-networks-into-memory-583e3c758ff9<br>
prepare_model_for_int8_training函数还做了一件事就是设置gradient_checkpointing=True，这是另一个时间换<br>
空间的技巧。<br>
gradient checkpoint的实现是在向前传播的过程中使用torch.no_grad()不去存储中间激活值，降低动态显存的占<br>
用。而只是保存输入和激活函数，当进行反向传播的时候，会重新获取输入和激活函数计算激活值用于梯度计<br>
算。因此向前传播会计算两遍，所以需要更多的训练时间。<br>
3.4 如何 向 模型 加入PEFT策略？<br>
其实lora微调的代码本身并不复杂，相反是如何加速大模型训练，降低显存占用的一些技巧大家可能不太熟悉。<br>
模型初始化代码如下，get_peft_model会初始化PeftModel把原模型作为base模型，并在各个self-attention层加<br>
入lora层，同时改写模型forward的计算方式。<br>
注：use_cache设置为False，是因为和gradient checkpoint存在冲突。因为use_cache是对解码<br>
速度的优化，在解码器解码时，存储每一步输出的hidden-state用于下一步的输入，而因为开启<br>
了gradient checkpoint，中间激活值不会存储，因此use_cahe=False。其实#21737已经加入了<br>
参数检查，这里设置只是为了不输出warning。<br>
四、PEFT库 中 LoRA 模块 代码介绍<br>
4.1 PEFT库 中 LoRA 模块 整体实现思路<br>
具体 PEFT 包装 包装，结合PEFT模块的源码，来看一下LORA是如何实现的。<br>
在PEFT模块中，peft_model.py中的PeftModel类是一个总控类，用于模型的读取保存等功能，继承了<br>
transformers中的Mixin类，我们主要来看LORA的实现：<br>
• layer norm层保留FP32精度<br>
• 输出层保留FP32精度保证解码时随机sample的差异性<br>
  # 加入PEFT策略<br>
  model = get_peft_model(model, config)<br>
  model = model.to(device)<br>
  model.config.use_cache = False<br>
</p>

<h2>第 6 页</h2>

<p>代码位置：https://github.com/huggingface/peft/blob/main/src/peft/tuners/lora.py<br>
从构造方法可以看出，这个类在创建的时候主要做了两步：<br>
4.2 PEFT库 中 LoRA 模块 _find_and_replace() 实现思路<br>
_find_and_replace() 实现思路：<br>
注：其中这个replace的方法并不复杂，就是把原来的weight和bias赋给新创建的module，然后再分配到指定的设<br>
备上：<br>
class LoraModel(torch.nn.Module):<br>
    def __init__(self, config, model):<br>
        super().__init__()<br>
        self.peft_config = config<br>
        self.model = model<br>
        self._find_and_replace()<br>
        mark_only_lora_as_trainable(self.model, self.peft_config.bias)<br>
        self.forward = self.model.forward<br>
• 第一步：self._find_and_replace()。找到所有需要加入lora策略的层，例如q_proj，把它们替换成lora模式；<br>
• 第二步：mark_only_lora_as_trainable(self.model, self.peft_config.bias)。保留lora部分的参数可训练，其余<br>
参数全都固定下来不动；<br>
1. 找到需要的做lora的层：<br>
  # 其中的target_modules在上面的例子中就是"q_proj"，"v_proj"<br>
  # 这一步就是找到模型的各个组件中，名字里带"q_proj"，"v_proj"的<br>
  target_module_found = re.fullmatch(self.peft_config.target_modules, key)<br>
2. 对于每一个找到的目标层，创建一个新的lora层：<br>
  # 注意这里的Linear是在该py中新建的类，不是torch的Linear<br>
  new_module = Linear(target.in_features, target.out_features, bias=bias, **kwargs)<br>
3. 调用_replace_module方法替换掉原来的linear：<br>
  self._replace_module(parent, target_name, new_module, target)<br>
    def _replace_module(self, parent_module, child_name, new_module, old_module):<br>
        setattr(parent_module, child_name, new_module)<br>
        new_module.weight = old_module.weight<br>
        if old_module.bias is not None:<br>
            new_module.bias = old_module.bias<br>
        if getattr(old_module, "state", None) is not None:<br>
            new_module.state = old_module.state<br>
            new_module.to(old_module.weight.device)<br>
        # dispatch to correct device<br>
        for name, module in new_module.named_modules():<br>
            if "lora_" in name:<br>
                module.to(old_module.weight.device)<br>
</p>

<h2>第 7 页</h2>

<p>4.3 PEFT库 中 Lora层的 实现思路<br>
4.3.1 基类 LoraLayer 实现<br>
Lora的基类，可以看出这个类就是用来构造Lora的各种超参数用：<br>
4.3.2 Linear 实现<br>
上文中所提到的Linear类，也就是Lora的具体实现，它同时继承了nn.Linear和LoraLayer：<br>
class LoraLayer:<br>
    def __init__(<br>
        self,<br>
        r: int,<br>
        lora_alpha: int,<br>
        lora_dropout: float,<br>
        merge_weights: bool,<br>
    ):<br>
        self.r = r<br>
        self.lora_alpha = lora_alpha<br>
        # Optional dropout<br>
        if lora_dropout &gt; 0.0:<br>
            self.lora_dropout = nn.Dropout(p=lora_dropout)<br>
        else:<br>
            self.lora_dropout = lambda x: x<br>
        # Mark the weight as unmerged<br>
        self.merged = False<br>
        self.merge_weights = merge_weights<br>
        self.disable_adapters = False<br>
class Linear(nn.Linear, LoraLayer):<br>
    # Lora implemented in a dense layer<br>
    def __init__(<br>
        self,<br>
        in_features: int,<br>
        out_features: int,<br>
        r: int = 0,<br>
        lora_alpha: int = 1,<br>
        lora_dropout: float = 0.0,<br>
        fan_in_fan_out: bool = False,  # Set this to True if the layer to replace <br>
stores weight like (fan_in, fan_out)<br>
        merge_weights: bool = True,<br>
        **kwargs,<br>
    ):<br>
        nn.Linear.__init__(self, in_features, out_features, **kwargs)<br>
        LoraLayer.__init__(self, r=r, lora_alpha=lora_alpha, <br>
lora_dropout=lora_dropout, merge_weights=merge_weights)<br>
        self.fan_in_fan_out = fan_in_fan_out<br>
</p>

<h2>第 8 页</h2>

<p>在构造方法中，除了对各个超参数进行配置之外，还对所有参数进行了初始化，定义如下：<br>
其中lora的A矩阵采用了kaiming初始化，是Xavier初始化针对非线性激活函数的一种优化；B矩阵采用了零初始<br>
化，以确保在初始状态 ΔW=BA 为零。（值得注意的是在LORA的论文中，A采用的是Gaussian初始化）。<br>
对于train和eval方法，放在一起介绍，它主要是需要对merge状态进行记录：<br>
        # Actual trainable parameters<br>
        if r &gt; 0:<br>
            self.lora_A = nn.Linear(in_features, r, bias=False)<br>
            self.lora_B = nn.Linear(r, out_features, bias=False)<br>
            self.scaling = self.lora_alpha / self.r<br>
            # Freezing the pre-trained weight matrix<br>
            self.weight.requires_grad = False<br>
        self.reset_parameters()<br>
        if fan_in_fan_out:<br>
            self.weight.data = self.weight.data.T<br>
    def reset_parameters(self):<br>
        nn.Linear.reset_parameters(self)<br>
        if hasattr(self, "lora_A"):<br>
            # initialize A the same way as the default for nn.Linear and B to zero<br>
            nn.init.kaiming_uniform_(self.lora_A.weight, a=math.sqrt(5))<br>
            nn.init.zeros_(self.lora_B.weight)<br>
    def train(self, mode: bool = True):<br>
        # 对于新定义的这个Linear层，其本身继承了torch.nn.Linear，所以需要调用<br>
nn.Linear.train(self, mode)来控制一下自身原本参数的状态，并且此外它加入了lora_A和<br>
lora_B两部分额外的参数，这两部分本质上也是nn.Linear，也需要控制状态。<br>
        nn.Linear.train(self, mode)<br>
        self.lora_A.train(mode)<br>
        self.lora_B.train(mode)<br>
        # not mode说明是eval模式<br>
        # self.merge_weights在上文中有介绍，是配置文件中的，意思是评估时是否需要将<br>
lora部分的weight加到linear层原本的weight中<br>
        # not self.merged是状态的记录<br>
        if not mode and self.merge_weights and not self.merged:<br>
            # 如果设置了需要融合，而当前状态没有融合的话，就把lora部分的参数scale之<br>
后加上去，并且更新self.merged状态<br>
            if self.r &gt; 0:<br>
                self.weight.data += (<br>
                    transpose(self.lora_B.weight @ self.lora_A.weight, <br>
self.fan_in_fan_out) * self.scaling<br>
                )<br>
            self.merged = True<br>
        elif self.merge_weights and self.merged:<br>
            # 为了在训练的过程中，确保linear本身的weights是没有经过融合过的（理论上<br>
这一步应该是在eval之后的下一轮train的第一个step触发）<br>
            if self.r &gt; 0:<br>
</p>

<h2>第 9 页</h2>

<p>注：为什么是在train中涉及merge_weights，其实在torch的源码中，nn.Linear.eval()实际上是调<br>
用了nn.Linear.train(mode=False)，所以这里train方法中的merge_weigths，实际上是在eval中也<br>
发挥作用的。<br>
forward中也是类似的原理，正常情况下训练过程应该是走elif的分支：<br>
五、使用 LoRA 对 大模型进行 高效参数微调，如何进行存储？<br>
因为peftModel重写了原始model的save_pretrained函数，只把lora层的权重进行存储，因此<br>
model.save_pretrained只会存储lora权重。而trainer的save_model函数没有做相应的重写，因此我们重写下<br>
对应的function，避免checkpoint写入原始模型全部参数。<br>
                self.weight.data -= (<br>
                    transpose(self.lora_B.weight @ self.lora_A.weight, <br>
self.fan_in_fan_out) * self.scaling<br>
                )<br>
            self.merged = False<br>
    def eval(self):<br>
        nn.Linear.eval(self)<br>
        self.lora_A.eval()<br>
        self.lora_B.eval()<br>
    def forward(self, x: torch.Tensor):<br>
        if self.disable_adapters:<br>
            if self.r &gt; 0 and self.merged:<br>
                self.weight.data -= (<br>
                    transpose(self.lora_B.weight @ self.lora_A.weight, <br>
self.fan_in_fan_out) * self.scaling<br>
                )<br>
                self.merged = False<br>
            return F.linear(x, transpose(self.weight, self.fan_in_fan_out), <br>
bias=self.bias)<br>
        elif self.r &gt; 0 and not self.merged:<br>
            result = F.linear(x, transpose(self.weight, self.fan_in_fan_out), <br>
bias=self.bias)<br>
            if self.r &gt; 0:<br>
                result += self.lora_B(self.lora_A(self.lora_dropout(x))) * <br>
self.scaling<br>
            return result<br>
        else:<br>
            return F.linear(x, transpose(self.weight, self.fan_in_fan_out), <br>
bias=self.bias)<br>
import datasets<br>
from transformers import Trainer, DataCollatorForSeq2Seq<br>
</p>

<h2>第 10 页</h2>

<p>六、使用 LoRA 对 大模型进行 推理，如何进行加载？<br>
推理有两个方案<br>
if resume_from_checkpoint:<br>
    lora_weight = torch.load(ckpt_name)<br>
    set_peft_model_state_dict(model, lora_weight)<br>
train_data = datasets.load_from_disk(dataset_path)<br>
class ModifiedTrainer(Trainer):<br>
    def save_model(self, output_dir=None, _internal_call=False):<br>
        # 改写trainer的save_model，在checkpoint的时候只存lora权重<br>
        from transformers.trainer import TRAINING_ARGS_NAME<br>
        os.makedirs(output_dir, exist_ok=True)<br>
        torch.save(self.args, os.path.join(output_dir, TRAINING_ARGS_NAME))<br>
        saved_params = {<br>
            k: v.to("cpu") for k, v in self.model.named_parameters() if <br>
v.requires_grad<br>
        }<br>
        torch.save(saved_params, os.path.join(output_dir, "adapter_model.bin"))<br>
        <br>
trainer = ModifiedTrainer(<br>
    model=model,<br>
    train_dataset=train_data,<br>
        args=transformers.TrainingArguments(<br>
            per_device_train_batch_size=8,<br>
            gradient_accumulation_steps=16,<br>
            num_train_epochs=10,<br>
            learning_rate=3e-4,<br>
            fp16=True,<br>
            logging_steps=10,<br>
            save_steps=200,<br>
            output_dir=output_dir<br>
        ),<br>
    data_collator=DataCollatorForSeq2Seq(<br>
        tokenizer, pad_to_multiple_of=8, return_tensors="pt", padding=True<br>
    ),<br>
)<br>
trainer.train()<br>
model.save_pretrained(train_args.output_dir)<br>
• 方案一：和训练相同，直接加入Lora层<br>
• 缺点：不过会增加推理延时因为多了lora层的计算，适合线下测评用<br>
from peft import PeftModel<br>
</p>

<h2>第 11 页</h2>

<p>from transformers import AutoModel, AutoTokenizer<br>
model = AutoModel.from_pretrained(<br>
    "THUDM/chatglm3-6b", trust_remote_code=True, load_in_8bit=True, <br>
device_map='auto'<br>
)<br>
tokenizer = AutoTokenizer.from_pretrained("THUDM/chatglm3-6b", <br>
trust_remote_code=True)<br>
model = PeftModel.from_pretrained(model, "./lora_ckpt")<br>
model.half().to(device)<br>
model.eval()<br>
• 方案二：先把lora权重和原始模型权重进行合并，把合并后的参数存储成新的bin文件，然后和加载常规模型<br>
一样加载合并后的模型参数进行推理<br>
• 优点：没有推理延时<br>
• 缺点：<br>
tokenizer = AutoTokenizer.from_pretrained("THUDM/chatglm3-6b", <br>
trust_remote_code=True)<br>
# when merging disable int8<br>
model = AutoModel.from_pretrained(<br>
    "THUDM/chatglm3-6b", load_in_8bit=False, torch_dtype=torch.float16,<br>
    trust_remote_code=True, device_map={"": "cpu"},<br>
)<br>
## 用来检查权重是否合并成功，合并成功weight会改变<br>
first_weight = model.base_model.layers[0].attention.query_key_value.weight<br>
first_weight_old = first_weight.clone()<br>
# 返回的不是新的模型，而是在原始模型上加了adapter层<br>
lora_model = PeftModel.from_pretrained(<br>
    model,<br>
    "./lora_ckpt",<br>
    device_map={"": "cpu"},<br>
    torch_dtype=torch.float16,<br>
)<br>
# 报错：A*B shape mismatch，大概率是get_peft_model错误修改了peft_config里面的<br>
fan_in_fan_out参数，某个peft的revision有这个bug<br>
lora_model = lora_model.merge_and_unload()<br>
lora_model.train(False)<br>
# 报错：大概率peft训练有问题，检查adapter.bin大小<br>
assert not torch.allclose(first_weight_old, first_weight), 'Weight Should Change <br>
after Lora Merge'<br>
# lora模型权重把原模型权重加了prefix，这里移除恢复原始key<br>
deloreanized_sd = {<br>
    k.replace("base_model.model.", ""): v<br>
    for k, v in lora_model.state_dict().items()<br>
</p>

<h2>第 12 页</h2>

<p>七、huggingface大模型如何加载多个LoRA并随时切换？<br>
    if "lora" not in k<br>
}<br>
# 保存合并后的模型权重<br>
lora_model.save_pretrained(output_dir, state_dict=deloreanized_sd)<br>
• requirement<br>
    peft&gt;=0.3.0<br>
• 用法解释<br>
1. 在加载第一个适配器时，可以通过 PeftModel.from_pretrained 方法并指定 adapter_name 参数来给它命名。<br>
否则，将使用默认的适配器名称 default，例如：<br>
    model = PeftModel.from_pretrained(model, "tloen/alpaca-lora-7b", <br>
adapter_name="eng_alpaca")<br>
2. 要加载另一个适配器，请使用 PeftModel 的 load_adapter() 方法，例如：<br>
    model.load_adapter(peft_model_path, adapter_name)<br>
3. 要切换适配器，请使用 PeftModel 的 set_adapter() 方法，例如：<br>
    model.set_adapter(adapter_name)<br>
4. 要禁用适配器，请使用上下文管理器 disable_adapter()，例如：<br>
    with model.disable_adapter()<br>
5. 特别适用于LoRA方法：要合并和卸载当前活动的适配器，以便将LoRA权重添加到基础模型权重中，并将注<br>
入的LoRA模型删除以恢复具有添加了LoRA权重的Transformers基础模型的模型，请使用<br>
merge_and_unload()方法，例如：<br>
    model = model.merge_and_unload()<br>
• 实战案例<br>
from peft import PeftModel<br>
from transformers import LlamaTokenizer, LlamaForCausalLM, GenerationConfig<br>
model_name = "decapoda-research/llama-7b-hf"<br>
tokenizer = LlamaTokenizer.from_pretrained(model_name)<br>
model = LlamaForCausalLM.from_pretrained(<br>
    model_name,<br>
    load_in_8bit=True,<br>
    device_map="auto",<br>
    use_auth_token=True<br>
)<br>
model = PeftModel.from_pretrained(model, "tloen/alpaca-lora-7b", <br>
adapter_name="eng_alpaca")<br>
model.load_adapter("22h/cabrita-lora-v0-1", adapter_name="portuguese_alpaca")<br>
model.set_adapter("eng_alpaca")<br>
</p>

<h2>第 13 页</h2>

<p>instruction = "Tell me about alpacas."<br>
print(evaluate(instruction))<br>
"""output<br>
The alpaca (Vicugna pacos) is a domesticated species of South American camelid. It <br>
resembles a small llama in appearance, but unlike the llama, it is not used as a <br>
beast of burden. It is kept primarily for its fiber, which can be spun into yarn. <br>
Alpaca fiber is warmer, lighter, and softer than sheep's wool, and is highly valued <br>
in the textile industry. The fiber comes in a variety of natural colors, including <br>
white, beige, cream, and fawn. It can also be dyed in a wide range of colors.<br>
Alpaca herds can be found in the highlands of Peru, Bolivia, Chile, Ecuador, and <br>
Colombia. They are also raised in the United States, Canada, Australia, New <br>
Zealand, and Europe. The animals graze on grasses, herbs, and shrubs, and can <br>
survive in temperatures as low as -30°F (-34°C). They are social animals, living <br>
in herds of up to 20 individuals.<br>
The fiber of the alpaka is used to make clothing<br>
"""<br>
model.set_adapter("portuguese_alpaca")<br>
instruction = "Invente uma desculpa criativa pra dizer que não preciso ir à festa."<br>
print(evaluate(instruction))<br>
"""output<br>
"Eu preciso ficar em casa para cuidar de meu gato."<br>
"""<br>
with model.disable_adapter():<br>
    instruction = "Invente uma desculpa criativa pra dizer que não preciso ir à <br>
festa."<br>
    print(evaluate(instruction))<br>
"""output<br>
I'm sorry, but I can't go to the party. I'm sick. I have a cold. I don't feel well. <br>
I need to stay at home and rest.<br>
I have a lot of homework to do. My dog ate my homework. My homework is too hard. I <br>
didn't have time to do it. It's too late. I forgot about it.<br>
My parents won't let me go. My parents are out of town. They're on vacation. They <br>
have to work. They are sick. They need to take care of my brother.<br>
They're not home. They went to the grocery store. They took the car to the <br>
mechanic. They had to go to a meeting. They were in a hurry. They forgot about me.<br>
Their car broke down. Their car ran out of gas. They got a flat tire. They couldn't <br>
find a parking space. They didn' t have enough money. They lost their wallet.<br>
It's raining. The roads are icy. There's a blizzard. There are too many cars on the <br>
road. There was an accident.<br>
"""<br>
</p>

<h2>第 14 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:44</p>
        </div>
    </div>
</body>
</html>