<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>59-怎么让英文大语言模型支持中文？（二） —— 继续预训练篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>59-怎么让英文大语言模型支持中文？（二） —— 继续预训练篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>怎么让英文大语言模型支持中文？（二） —— 继续预训练篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:28<br>
一、为什么需要进行继续预训练？<br>
前面我们已经讲过怎么构建中文领域的tokenization：<br>
接下来我们将介绍继续预训练。<br>
我们新增加了一些中文词汇到词表中，这些词汇是没有得到训练的，因此在进行指令微调之前我们要进行预训<br>
练。预训练的方式一般都是相同的，简单来说，就是根据上一个字预测下一个字是什么。为了方便起见，我们这<br>
里直接使用IDEA-CCNL/Wenzhong2.0-GPT2-110M-BertTokenizer-chinese模型，并且tokenizer也是其自带的。<br>
二、如何对 继续预训练 数据预处理？<br>
同样的，我们使用的数据还是斗破苍穹小说数据。首先我们看看是怎么处理数据的， 数据位于data下，分别为<br>
corpus.txt和test_corpus.txt，每一行为一句或多句话。再看看数据预处理的部分，在test_dataset.py里面：<br>
import os<br>
import logging<br>
import datasets<br>
import transformers<br>
from pprint import pprint<br>
from itertools import chain<br>
from datasets import load_dataset, concatenate_datasets<br>
from transformers.testing_utils import CaptureLogger<br>
from transformers import AutoTokenizer, LlamaTokenizer<br>
tok_logger = <br>
transformers.utils.logging.get_logger("transformers.tokenization_utils_base")<br>
logger = logging.getLogger(__name__)<br>
lm_datasets = []<br>
files = ["data/test_corpus.txt"]<br>
data_cache_dir = "./cache_data"<br>
preprocessing_num_workers = 1<br>
# tokenizer = AutoTokenizer.from_pretrained("hfl/chinese-bert-wwm-ext")<br>
tokenizer = LlamaTokenizer.from_pretrained("ziqingyang/chinese-llama-lora-7b")<br>
tokenizer = AutoTokenizer.from_pretrained("IDEA-CCNL/Wenzhong2.0-GPT2-110M-<br>
BertTokenizer-chinese")<br>
def print_dict(adict):<br>
  for k,v in adict.items():<br>
    print(k, v)<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>def tokenize_function(examples):<br>
        with CaptureLogger(tok_logger) as cl:<br>
            output = tokenizer(examples["text"])<br>
        # clm input could be much much longer than block_size<br>
        if "Token indices sequence length is longer than the" in cl.out:<br>
            tok_logger.warning(<br>
                "^^^^^^^^^^^^^^^^ Please ignore the warning above - this long input <br>
will be chunked into smaller bits"<br>
                " before being passed to the model."<br>
            )<br>
        return output<br>
block_size = 128<br>
# 将所有文本进行拼接<br>
def group_texts(examples):<br>
        # Concatenate all texts.<br>
        concatenated_examples = {k: list(chain(*examples[k])) for k in <br>
examples.keys()}<br>
        total_length = len(concatenated_examples[list(examples.keys())[0]])<br>
        # We drop the small remainder, we could add padding if the model supported <br>
it instead of this drop, you can<br>
        # customize this part to your needs.<br>
        if total_length &gt;= block_size:<br>
            total_length = (total_length // block_size) * block_size<br>
        # Split by chunks of max_len.<br>
        result = {<br>
            k: [t[i : i + block_size] for i in range(0, total_length, block_size)]<br>
            for k, t in concatenated_examples.items()<br>
        }<br>
        result["labels"] = result["input_ids"].copy()<br>
        return result<br>
for idx, file in enumerate(files):<br>
    data_file = file<br>
    filename = ''.join(file.split(".")[:-1])<br>
    cache_path = os.path.join(data_cache_dir, filename)<br>
    os.makedirs(cache_path, exist_ok=True)<br>
    try:<br>
        processed_dataset = datasets.load_from_disk(cache_path, <br>
keep_in_memory=False)<br>
        print(f'training datasets-{filename} has been loaded from disk')<br>
    except Exception:<br>
        cache_dir = os.path.join(data_cache_dir, filename + "_text")<br>
        os.makedirs(cache_dir, exist_ok=True)<br>
</p>

<h2>第 3 页</h2>

<p>输出<br>
        raw_dataset = load_dataset("text", data_files=data_file, <br>
cache_dir=cache_dir, keep_in_memory=False)<br>
        print_dict(raw_dataset["train"][0])<br>
        # 直接进行tokenize，需要注意的是只需要在句子开头加上bos_token<br>
        tokenized_dataset = raw_dataset.map(<br>
            tokenize_function,<br>
            batched=True,<br>
            num_proc=preprocessing_num_workers,<br>
            remove_columns="text",<br>
            load_from_cache_file=True,<br>
            keep_in_memory=False,<br>
            cache_file_names={k: os.path.join(cache_dir, f'tokenized.arrow') for k <br>
in raw_dataset},<br>
            desc="Running tokenizer on dataset",<br>
        )<br>
        print_dict(tokenized_dataset["train"][0])<br>
        grouped_datasets = tokenized_dataset.map(<br>
            group_texts,<br>
            batched=True,<br>
            num_proc=preprocessing_num_workers,<br>
            load_from_cache_file=True,<br>
            keep_in_memory=False,<br>
            cache_file_names={k: os.path.join(cache_dir, f'grouped.arrow') for k in <br>
tokenized_dataset},<br>
            desc=f"Grouping texts in chunks of {block_size}",<br>
        )<br>
        processed_dataset = grouped_datasets<br>
        print_dict(processed_dataset["train"][0])<br>
        processed_dataset.save_to_disk(cache_path)<br>
    if idx == 0:<br>
        lm_datasets = processed_dataset['train']<br>
    else:<br>
        assert lm_datasets.features.type == <br>
processed_dataset["train"].features.type<br>
        lm_datasets = concatenate_datasets([lm_datasets, <br>
processed_dataset["train"]])<br>
lm_datasets = lm_datasets.train_test_split(test_size=0.1)<br>
print_dict(lm_datasets["train"][0])<br>
text 又一次上架了，这次比上次还激动，甚至激动到了上传了章节却不知道发出来的地步。<br>
</p>

<h2>第 4 页</h2>

<p>input_ids [21134, 1348, 671, 3613, 677, 3373, 749, 8024, 6821, 3613, 3683, 677, <br>
3613, 6820, 4080, 1220, 8024, 4493, 5635, 4080, 1220, 1168, 749, 677, 837, 749, <br>
4995, 5688, 1316, 679, 4761, 6887, 1355, 1139, 3341, 4638, 1765, 3635, 511, 21133]<br>
token_type_ids [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]<br>
attention_mask [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]<br>
input_ids [21134, 1348, 671, 3613, 677, 3373, 749, 8024, 6821, 3613, 3683, 677, <br>
3613, 6820, 4080, 1220, 8024, 4493, 5635, 4080, 1220, 1168, 749, 677, 837, 749, <br>
4995, 5688, 1316, 679, 4761, 6887, 1355, 1139, 3341, 4638, 1765, 3635, 511, 21133, <br>
21134, 2219, 2217, 8024, 1068, 754, 3173, 741, 8024, 677, 3373, 1184, 2768, 5327, <br>
1962, 2533, 3300, 763, 1139, 725, 1759, 6486, 4638, 2692, 3160, 8024, 2190, 754, <br>
6821, 819, 1331, 4798, 4638, 2768, 5327, 8024, 1759, 6486, 2552, 7027, 6820, 4696, <br>
3300, 1126, 1146, 2684, 2607, 680, 2558, 2559, 8024, 6006, 6432, 3295, 5307, 3300, <br>
782, 6432, 1759, 6486, 3221, 1170, 1139, 3341, 4638, 3144, 2945, 8024, 2190, 754, <br>
6821, 763, 4522, 6241, 8024, 2769, 738, 2400, 3313, 1922, 6814, 1762, 2692, 8024, <br>
1166, 4638, 2769, 679]<br>
token_type_ids [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]<br>
attention_mask [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]<br>
labels [21134, 1348, 671, 3613, 677, 3373, 749, 8024, 6821, 3613, 3683, 677, 3613, <br>
6820, 4080, 1220, 8024, 4493, 5635, 4080, 1220, 1168, 749, 677, 837, 749, 4995, <br>
5688, 1316, 679, 4761, 6887, 1355, 1139, 3341, 4638, 1765, 3635, 511, 21133, 21134, <br>
2219, 2217, 8024, 1068, 754, 3173, 741, 8024, 677, 3373, 1184, 2768, 5327, 1962, <br>
2533, 3300, 763, 1139, 725, 1759, 6486, 4638, 2692, 3160, 8024, 2190, 754, 6821, <br>
819, 1331, 4798, 4638, 2768, 5327, 8024, 1759, 6486, 2552, 7027, 6820, 4696, 3300, <br>
1126, 1146, 2684, 2607, 680, 2558, 2559, 8024, 6006, 6432, 3295, 5307, 3300, 782, <br>
6432, 1759, 6486, 3221, 1170, 1139, 3341, 4638, 3144, 2945, 8024, 2190, 754, 6821, <br>
763, 4522, 6241, 8024, 2769, 738, 2400, 3313, 1922, 6814, 1762, 2692, 8024, 1166, <br>
4638, 2769, 679]<br>
input_ids [21134, 1348, 671, 3613, 677, 3373, 749, 8024, 6821, 3613, 3683, 677, <br>
3613, 6820, 4080, 1220, 8024, 4493, 5635, 4080, 1220, 1168, 749, 677, 837, 749, <br>
4995, 5688, 1316, 679, 4761, 6887, 1355, 1139, 3341, 4638, 1765, 3635, 511, 21133, <br>
21134, 2219, 2217, 8024, 1068, 754, 3173, 741, 8024, 677, 3373, 1184, 2768, 5327, <br>
1962, 2533, 3300, 763, 1139, 725, 1759, 6486, 4638, 2692, 3160, 8024, 2190, 754, <br>
6821, 819, 1331, 4798, 4638, 2768, 5327, 8024, 1759, 6486, 2552, 7027, 6820, 4696, <br>
3300, 1126, 1146, 2684, 2607, 680, 2558, 2559, 8024, 6006, 6432, 3295, 5307, 3300, <br>
782, 6432, 1759, 6486, 3221, 1170, 1139, 3341, 4638, 3144, 2945, 8024, 2190, 754, <br>
6821, 763, 4522, 6241, 8024, 2769, 738, 2400, 3313, 1922, 6814, 1762, 2692, 8024, <br>
1166, 4638, 2769, 679]<br>
</p>

<h2>第 5 页</h2>

<p>具体是：<br>
三、如何 构建模型？<br>
在test_model.py里面我们可以初步使用预训练的模型看看效果：<br>
token_type_ids [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, <br>
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]<br>
attention_mask [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, <br>
1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]<br>
labels [21134, 1348, 671, 3613, 677, 3373, 749, 8024, 6821, 3613, 3683, 677, 3613, <br>
6820, 4080, 1220, 8024, 4493, 5635, 4080, 1220, 1168, 749, 677, 837, 749, 4995, <br>
5688, 1316, 679, 4761, 6887, 1355, 1139, 3341, 4638, 1765, 3635, 511, 21133, 21134, <br>
2219, 2217, 8024, 1068, 754, 3173, 741, 8024, 677, 3373, 1184, 2768, 5327, 1962, <br>
2533, 3300, 763, 1139, 725, 1759, 6486, 4638, 2692, 3160, 8024, 2190, 754, 6821, <br>
819, 1331, 4798, 4638, 2768, 5327, 8024, 1759, 6486, 2552, 7027, 6820, 4696, 3300, <br>
1126, 1146, 2684, 2607, 680, 2558, 2559, 8024, 6006, 6432, 3295, 5307, 3300, 782, <br>
6432, 1759, 6486, 3221, 1170, 1139, 3341, 4638, 3144, 2945, 8024, 2190, 754, 6821, <br>
763, 4522, 6241, 8024, 2769, 738, 2400, 3313, 1922, 6814, 1762, 2692, 8024, 1166, <br>
4638, 2769, 679]<br>
1. 先使用tokenizer()得到相关的输入，需要注意的是可能会在文本前后添加特殊的标记，比如bos_token_id和<br>
eos_token_id，针对于不同模型的tokneizer可能会不太一样。这里在unput_ids前后添加了21134和21133两<br>
个标记。<br>
2. 然后将所有文本的input_ids、attention_mask, token_type_ids各自拼接起来（展开后拼接，不是二维数组之<br>
间的拼接），再设定一个最大长度block_size，这样得到最终的输入。<br>
from transformers import BertTokenizer,GPT2LMHeadModel, AutoModelForCausalLM<br>
hf_model_path = 'IDEA-CCNL/Wenzhong2.0-GPT2-110M-BertTokenizer-chinese'<br>
tokenizer = BertTokenizer.from_pretrained(hf_model_path)<br>
# model = GPT2LMHeadModel.from_pretrained(hf_model_path)<br>
model = AutoModelForCausalLM.from_pretrained(hf_model_path)<br>
def generate_word_level(input_text,n_return=5,max_length=128,top_p=0.9):<br>
    inputs = <br>
tokenizer(input_text,return_tensors='pt',add_special_tokens=False).to(model.device)<br>
    gen = model.generate(<br>
                            inputs=inputs['input_ids'],<br>
                            max_length=max_length,<br>
                            do_sample=True,<br>
                            top_p=top_p,<br>
                            eos_token_id=21133,<br>
                            pad_token_id=0,<br>
                            num_return_sequences=n_return)<br>
</p>

<h2>第 6 页</h2>

<p>输出<br>
接下来是使用该模型针对我们自己的数据进行继续预训练了。需要注意的几个地方：<br>
    sentences = tokenizer.batch_decode(gen)<br>
    for idx,sentence in enumerate(sentences):<br>
        print(f'sentence {idx}: {sentence}')<br>
        print('*'*20)<br>
    return gen<br>
# 西湖的景色<br>
outputs = generate_word_level('西湖的景色',n_return=5,max_length=128)<br>
print(outputs)<br>
sentence 0: 西 湖 的 景 色 很 美 丽, 古 代 有 个 名 叫 : 西 湖 的 湖 南 和 江 南 的 <br>
一 段 。 湖 面 上 有 一 座 小 小 的 湖 泊, 有 一 片 湖 泊 和 一 座 小 岛, 有 一 处 <br>
小 的 小 镇 。 在 西 湖 里, 每 个 人 都 是 在 湖 边, 你 可 以 在 小 小 湖 里 畅 游 <br>
。 西 湖 上 是 古 代 建 筑, 但 湖 水 不 多 。 西 湖 上 是 一 座 水 库, 古 代 有 个 <br>
名 叫 : 西 湖 的 湖 南 和 江 南 的 一 段 。 湖<br>
********************<br>
sentence 1: 西 湖 的 景 色 美 不 胜 数 。 近 日, 位 于 湖 北 省 湖 北 省 石 家 庄 <br>
市 的 石 家 庄 旅 游 风 景 区 被 命 名 为 " 湖 北 省 国 家 级 森 林 公 园 " 。 园 <br>
内 有 一 座 石 屋, 位 于 石 屋 与 石 屋 的 对 面, 总 面 积 3. 2 平 方 公 里, 其 中 <br>
一 座 石 屋, 由 石 屋 和 石 屋 组 成, 一 栋 大 型 石 屋 由 石 屋 组 成, 三 栋 石 屋 <br>
由 石 屋 组 成 。 石 屋 主 要 是 一 座 石 屋<br>
********************<br>
sentence 2: 西 湖 的 景 色 在 古 城 、 小 镇 和 城 郊 中, 有 大 片 的 湖 泊, 是 古 <br>
典 中 的 佳 肴, 湖 水 清 澈, 湖 中 有 一 大 块 鱼, 在 湖 水 里 散 发 着 浓 郁 的 清 <br>
香 。 湖 水 中, 有 各 种 颜 色 的 鱼 、 蟹 、 贝 壳 类 的 水 产 品 。 湖 边 有 的 <br>
池 塘 也 有 的 水 果 摊 位, 可 供 上 千 家 店 。 在 湖 中 央 的 湖 中 央 有 三 个 <br>
小 水 塘, 水 塘 长 约 三 丈, 两 端 长, 塘 底<br>
********************<br>
sentence 3: 西 湖 的 景 色 也 很 漂 亮, 可 以 说 是 城 市 的 象 征, 而 且 还 有 小 <br>
小 的 山 洞, 看 到 了, 我 们 在 西 湖 的 中 心 也 很 近, 所 以 也 没 有 停 止, 西 <br>
湖 的 风 景 很 秀 美, 我 们 也 不 愿 意 停 留 在 这 样 的 地 方 。 西 湖 是 世 界 <br>
上 最 美 的 湖 泊, 也 是 最 令 人 羡 慕 的 旅 游 区, 西 湖 的 美 丽 不 容 小 视, 是 <br>
我 们 心 中 最 美 的 风 景 。 西 湖 在 西 湖<br>
********************<br>
sentence 4: 西 湖 的 景 色 是 如 此 独 特, 那 水 碧 草 如 黛, 池 水 清 新, 一 池 青 <br>
湖, 游 人 可 以 品 一 小 池 花 。 " " 好 景 如 画, 山 清 水 秀, 碧 草 如 茵, 池 清 <br>
潭 秀 。 " 黄 湖 " 是 西 湖 的 " 绿 色 湖 " 。 西 湖 的 景 色 是 如 此 独 特, 那 水 <br>
碧 草 如 黛, 池 水 清 新, 一 池 青 湖, 游 人 可 以 品 一 小 池 花 。 " " 好 景 如 <br>
画, 山 清 水 秀, 碧 草 如 茵<br>
********************<br>
1. 如果是我们自己定义的tokenizer，需要将模型的嵌入层和lm_head层的词表数目进行重新设置：<br>
model_vocab_size = model.get_output_embeddings().weight.size(0)<br>
model.resize_token_embeddings(len(tokenizer))<br>
2. 这里我们使用参数有效微调方法lora进行微调，我们需要设置额外保存的参数：transformer.wte,lm_head。<br>
这个可以通过find_lora_names.py里面获得。<br>
</p>

<h2>第 7 页</h2>

<p>即：<br>
3. 原始chinsee-llama-alpaca使用lora保存参数有问题，这里进行了修改并只保存一份lora权重。<br>
4. 使用test_pretrained_model.py的时候也要记得先对vocab_size进行重新设置。<br>
    $ torchrun --nnodes 1 --nproc_per_node 1 run_clm_pt_with_peft.py --deepspeed <br>
ds_zero2_no_offload.json --model_name_or_path IDEA-CCNL/Wenzhong2.0-GPT2-110M-<br>
BertTokenizer-chinese --tokenizer_name_or_path IDEA-CCNL/Wenzhong2.0-GPT2-110M-<br>
BertTokenizer-chinese --dataset_dir data --data_cache_dir temp_data_cache_dir --<br>
validation_split_percentage 0.001 --per_device_train_batch_size 32 --<br>
per_device_eval_batch_size 16 --do_train --seed $RANDOM --fp16 --max_steps 2500 --<br>
lr_scheduler_type cosine --learning_rate 2e-4 --warmup_ratio 0.05 --weight_decay <br>
0.01 --logging_strategy steps --logging_steps 10 --save_strategy steps --<br>
save_total_limit 3 --save_steps 50 --gradient_accumulation_steps 1 --<br>
preprocessing_num_workers 8 --block_size 512 --output_dir output_dir --<br>
overwrite_output_dir --ddp_timeout 30000 --logging_first_step True --lora_rank 8 --<br>
lora_alpha 32 --trainable c_attn --modules_to_save transformer.wte,lm_head --<br>
lora_dropout 0.05 --torch_dtype float16 --gradient_checkpointing --<br>
ddp_find_unused_parameters False<br>
torchrun --nnodes 1 --nproc_per_node 1 run_clm_pt_with_peft.py \<br>
--deepspeed ds_zero2_no_offload.json \<br>
--model_name_or_path IDEA-CCNL/Wenzhong2.0-GPT2-110M-BertTokenizer-chinese \<br>
--tokenizer_name_or_path IDEA-CCNL/Wenzhong2.0-GPT2-110M-BertTokenizer-chinese \<br>
--dataset_dir data \<br>
--data_cache_dir temp_data_cache_dir \<br>
--validation_split_percentage 0.001 \<br>
--per_device_train_batch_size 32 \<br>
--per_device_eval_batch_size 16 \<br>
--do_train --seed $RANDOM \<br>
--fp16 \<br>
--max_steps 2500 \<br>
--lr_scheduler_type cosine \<br>
--learning_rate 2e-4 \<br>
--warmup_ratio 0.05 \<br>
--weight_decay 0.01 \<br>
--logging_strategy steps \<br>
--logging_steps 10 \<br>
--save_strategy steps \<br>
--save_total_limit 3 \<br>
--save_steps 50 \<br>
--gradient_accumulation_steps 1 \<br>
--preprocessing_num_workers 8 \<br>
--block_size 512 \<br>
--output_dir output_dir \<br>
--overwrite_output_dir \<br>
--ddp_timeout 30000 \<br>
--logging_first_step True \<br>
--lora_rank 8 \<br>
</p>

<h2>第 8 页</h2>

<p>由于使用了seepspeed中ZeRo，占用的显存会更小。<br>
四、如何 使用模型？<br>
最后我们可以这么使用模型，在test_pretrained_model.py中：<br>
--lora_alpha 32 \<br>
--trainable c_attn \<br>
--modules_to_save transformer.wte,lm_head \<br>
--lora_dropout 0.05 \<br>
--torch_dtype float16 \<br>
--gradient_checkpointing \<br>
--ddp_find_unused_parameters False<br>
import os<br>
import torch<br>
from transformers import BertTokenizer,GPT2LMHeadModel, AutoModelForCausalLM<br>
from peft import PeftModel<br>
hf_model_path = 'IDEA-CCNL/Wenzhong2.0-GPT2-110M-BertTokenizer-chinese'<br>
tokenizer = BertTokenizer.from_pretrained(hf_model_path)<br>
# model = GPT2LMHeadModel.from_pretrained(hf_model_path)<br>
model = AutoModelForCausalLM.from_pretrained(hf_model_path)<br>
model_vocab_size = model.get_output_embeddings().weight.size(0)<br>
model.resize_token_embeddings(len(tokenizer))<br>
model = PeftModel.from_pretrained(model, os.path.join("output_dir", <br>
"adapter_model"), torch_dtype=torch.float32)<br>
model.cuda()<br>
model.eval()<br>
def generate_word_level(input_text,n_return=5,max_length=128,top_p=0.9):<br>
    inputs = <br>
tokenizer(input_text,return_tensors='pt',add_special_tokens=False).to(model.device)<br>
    gen = model.generate(<br>
                            inputs=inputs['input_ids'],<br>
                            max_length=max_length,<br>
                            do_sample=True,<br>
                            top_p=top_p,<br>
                            eos_token_id=21133,<br>
                            pad_token_id=0,<br>
                            num_return_sequences=n_return)<br>
    sentences = tokenizer.batch_decode(gen)<br>
    for idx,sentence in enumerate(sentences):<br>
        print(f'sentence {idx}: {sentence}')<br>
        print('*'*20)<br>
    return gen<br>
</p>

<h2>第 9 页</h2>

<p>output<br>
对于没有经过继续预训练的模型结果：<br>
outputs = generate_word_level('眼角斜瞥着柳翎那略微有些阴沉的脸庞。萧<br>
炎',n_return=5,max_length=128)<br>
print(outputs)<br>
sentence 0: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 淡 淡 的 道 <br>
。 &lt;|endoftext|&gt; [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] <br>
[PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] <br>
[PAD] [PAD] [PAD] [PAD] [PAD]<br>
********************<br>
sentence 1: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 一 怔 。 手 <br>
掌 猛 然 一 僵 。 手 指 一 扯 。 旋 即 在 房 门 内 停 留 。 旋 即 一 口 鲜 血 喷 涌 <br>
而 出 。 &lt;|endoftext|&gt;<br>
********************<br>
sentence 2: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 顿 时 愣 了 <br>
愣 。 他 这 是 何 人 ？ 怎 能 知 道 这 位 灰 袍 老 者 出 手 啊 ？ &lt;|endoftext|&gt; <br>
[PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD]<br>
********************<br>
sentence 3: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 心 中 有 着 <br>
什 么 感 触 ？ &lt;|endoftext|&gt; [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] <br>
[PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] <br>
[PAD] [PAD] [PAD]<br>
********************<br>
sentence 4: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 微 皱 着 眉 <br>
头 。 转 过 身 。 轻 声 道 ： “ 柳 翎 。 是 你 的 人 ？ ” &lt;|endoftext|&gt; [PAD] <br>
[PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD] [PAD]<br>
********************<br>
sentence 0: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎, 男, 1964 <br>
年 生, 河 北 齐 齐 哈 尔 市 人 。 1979 年 毕 业 于 武 汉 工 学 院 中 文 系, 1988 年 <br>
毕 业 于 中 国 人 民 大 学 中 文 系, 历 任 中 国 人 民 大 学 高 级 教 师 、 教 育 <br>
部 大 学 文 学 系 主 任, 中 国 语 言 文 学 会 理 事, 中 国 人 民 大 学 历 史 学 会 <br>
副 会 长, 中 国 作 家 协 会 员, 中 国 作 家 协 会 会<br>
********************<br>
sentence 1: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 的 脸 庞 在 <br>
不 同 时 期 会 发 出 来 ， 这 样 的 眉 目 和 眉 目 能 够 很 容 易 的 在 一 起 ， 能 <br>
够 让 人 看 得 见 的 就 是 这 样 的 眉 目 。 那 一 对 情 侣 还 是 非 常 喜 欢 的 ， <br>
不 过 他 们 的 交 往 方 式 也 是 各 种 多 样 的 ， 最 后 的 交 往 方 式 就 是 让 所 <br>
有 的 人 都 看 到 了 自 己 的 内 心 。 他 们 俩 是 非 常 相<br>
********************<br>
sentence 2: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 眼 睛 看 向 <br>
柳 翎, 眼 眸 里 满 是 伤 痕 。 “ 天 边 来 客 。 ” 柳 翎 那 无 情 的 目 光 中 透 <br>
着 几 分 冷 漠 的 微 笑 。 “ 没 有 你 的 名 字, 你 只 是 名 字 。 ” 柳 翎 在 柳 <br>
翎 眼 前 一 怔, 无 意 中 却 看 出 了 柳 翎 已 经 在 想 要 离 开 了 。 柳 翎 说 这 <br>
些 东 西 有 的 是 一 次 次 的 意 外, 她 还 是 有 意 的,<br>
********************<br>
</p>

<h2>第 10 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>模型确实得到了有效的训练。<br>
知识星球<br>
sentence 3: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 的 脸 上 只 <br>
有 几 分 阴 沉, 但 却 能 够 带 着 微 微 的 怜 惜 之 心 。 萧 炎 眼 角 斜 瞥 着 柳 <br>
翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 <br>
的 脸 庞 。 萧 炎 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 眼 角 <br>
斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 眼 角<br>
********************<br>
sentence 4: 眼 角 斜 瞥 着 柳 翎 那 略 微 有 些 阴 沉 的 脸 庞 。 萧 炎 已 经 是 年 <br>
轻 貌 美 的 人, 在 某 处 留 下 的 是 无 尽 的 光 影 。 她 的 微 笑 也 在 耳 畔 闪 <br>
烁 着 光 影 。 他 不 断 地 伸 出 手 指, 他 在 他 的 微 笑 中 轻 松 地 走 着, 而 柳 <br>
翎 却 始 终 沉 默 。 他 已 经 是 个 女 孩 子, 在 某 处 也 许 你 听 不 见 。 他 轻 <br>
轻 地 接 过 他 的 手, 轻 轻 地 说 道 : " 没 有 人 听<br>
********************<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:48</p>
        </div>
    </div>
</body>
</html>