<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>11-大模型（LLMs）langchain 面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>11-大模型（LLMs）langchain 面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="data:image/png;base64,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" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）langchain 面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月16日 21:19<br>
1. 什么是 LangChain?<br>
LangChain是一个强大的框架，旨在帮助开发人员使用语言模型构建端到端的应用程序。它提供了一套工具、组<br>
件和接口，可简化创建由大型语言模型 (LLM) 和聊天模型提供支持的应用程序的过程。LangChain 可以轻松管理<br>
与语言模型的交互，将多个组件链接在一起，并集成额外的资源，例如 API 和数据库。<br>
2. LangChain 包含哪些 核心概念？<br>
2.1 LangChain 中 Components and Chains 是什么？<br>
注：一个 Chain 可能包括一个 Prompt 模板、一个语言模型和一个输出解析器，它们一起工作以<br>
处理用户输入、生成响应并处理输出。<br>
2.2 LangChain 中 Prompt Templates and Values 是什么？<br>
2.3 LangChain 中 Example Selectors 是什么？<br>
2.4 LangChain 中 Output Parsers 是什么？<br>
2.5 LangChain 中 Indexes and Retrievers 是什么？<br>
Index ：一种组织文档的方式，使语言模型更容易与它们交互；<br>
Retrievers：用于获取相关文档并将它们与语言模型组合的接口；<br>
注：LangChain 提供了用于处理不同类型的索引和检索器的工具和功能，例如矢量数据库和文本拆分器。<br>
2.6 LangChain 中 Chat Message History 是什么？<br>
• Component ：模块化的构建块，可以组合起来创建强大的应用程序；<br>
• Chain ：组合在一起以完成特定任务的一系列 Components（或其他 Chain）；<br>
• Prompt Template 作用：负责创建 PromptValue，这是最终传递给语言模型的内容<br>
• Prompt Template 特点：有助于将用户输入和其他动态信息转换为适合语言模型的格式。PromptValues 是<br>
具有方法的类，这些方法可以转换为每个模型类型期望的确切输入类型（如文本或聊天消息）。<br>
• 作用：当您想要在 Prompts 中动态包含示例时，Example Selectors 很有用。他们接受用户输入并返回一个<br>
示例列表以在提示中使用，使其更强大和特定于上下文。<br>
• 作用： 负责将语言模型响应构建为更有用的格式<br>
• 实现方法：<br>
• 一种用于提供格式化指令<br>
• 另一种用于将语言模型的响应解析为结构化格式<br>
• 特点：使得在您的应用程序中处理输出数据变得更加容易。<br>
• Chat Message History 作用：负责记住所有以前的聊天交互数据，然后可以将这些交互数据传递回模型、汇<br>
总或以其他方式组合；<br>
• 优点：有助于维护上下文并提高模型对对话的理解<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>2.7 LangChain 中 Agents and Toolkits 是什么？<br>
通过理解和利用这些核心概念，您可以利用 LangChain 的强大功能来构建适应性强、高效且能够处理复杂用例<br>
的高级语言模型应用程序。<br>
3. 什么是 LangChain Agent?<br>
4. 如何使用 LangChain ?<br>
要使用 LangChain，开发人员首先要导入必要的组件和工具，例如 LLMs, chat models, agents, chains, 内存功<br>
能。这些组件组合起来创建一个可以理解、处理和响应用户输入的应用程序。<br>
5. LangChain 支持哪些功能?<br>
6. 什么是 LangChain model?<br>
LangChain model 是一种抽象，表示框架中使用的不同类型的模型。LangChain 中的模型主要分为三类：<br>
开发人员可以为他们的用例选择合适的 LangChain 模型，并利用提供的组件来构建他们的应用程序。<br>
7. LangChain 包含哪些特点?<br>
LangChain 旨在为六个主要领域的开发人员提供支持：<br>
• Agent ：在 LangChain 中推动决策制定的实体。他们可以访问一套工具，并可以根据用户输入决定调用哪个<br>
工具；<br>
• Tookits ：一组工具，当它们一起使用时，可以完成特定的任务。代理执行器负责使用适当的工具运行代理。<br>
• 介绍：LangChain Agent 是框架中驱动决策制定的实体。它可以访问一组工具，并可以根据用户的输入决定<br>
调用哪个工具；<br>
• 优点：LangChain Agent 帮助构建复杂的应用程序，这些应用程序需要自适应和特定于上下文的响应。当存<br>
在取决于用户输入和其他因素的未知交互链时，它们特别有用。<br>
• 针对特定文档的问答：根据给定的文档回答问题，使用这些文档中的信息来创建答案。<br>
• 聊天机器人：构建可以利用 LLM 的功能生成文本的聊天机器人。<br>
• Agents：开发可以决定行动、采取这些行动、观察结果并继续执行直到完成的代理。<br>
1. LLM（大型语言模型）：这些模型将文本字符串作为输入并返回文本字符串作为输出。它们是许多语言模型<br>
应用程序的支柱。<br>
2. 聊天模型( Chat Model)：聊天模型由语言模型支持，但具有更结构化的 API。他们将聊天消息列表作为输入<br>
并返回聊天消息。这使得管理对话历史记录和维护上下文变得容易。<br>
3. 文本嵌入模型(Text Embedding Models)：这些模型将文本作为输入并返回表示文本嵌入的浮点列表。这些<br>
嵌入可用于文档检索、聚类和相似性比较等任务。<br>
1. LLM 和提示：LangChain 使管理提示、优化它们以及为所有 LLM 创建通用界面变得容易。此外，它还包括<br>
一些用于处理 LLM 的便捷实用程序。<br>
2. 链(Chain)：这些是对 LLM 或其他实用程序的调用序列。LangChain 为链提供标准接口，与各种工具集成，<br>
为流行应用提供端到端的链。<br>
3. 数据增强生成：LangChain 使链能够与外部数据源交互以收集生成步骤的数据。例如，它可以帮助总结长文<br>
本或使用特定数据源回答问题。<br>
4. Agents：Agents 让 LLM 做出有关行动的决定，采取这些行动，检查结果，并继续前进直到工作完成。<br>
LangChain 提供了代理的标准接口，多种代理可供选择，以及端到端的代理示例。<br>
5. 内存：LangChain 有一个标准的内存接口，有助于维护链或代理调用之间的状态。它还提供了一系列内存实<br>
现和使用内存的链或代理的示例。<br>
6. 评估：很难用传统指标评估生成模型。这就是为什么 LangChain 提供提示和链来帮助开发者自己使用 LLM <br>
评估他们的模型。<br>
</p>

<h2>第 3 页</h2>

<p>8. LangChain 如何使用?<br>
8.1 LangChain 如何调用 LLMs 生成回复？<br>
Models: 指各类训练好大语言模型（eg: chatgpt(未开源)，chatglm，vicuna等）<br>
8.2 LangChain 如何修改 提示模板？<br>
langchain.PromptTemplate: langchain中的提示模板类<br>
根据不同的下游任务设计不同的prompt模板，然后填入内容，生成新的prompt。目的其实就是为了通过设计更准<br>
确的提示词，来引导大模型输出更合理的内容。<br>
# 官方llm使用OPENAI 接口<br>
from langchain.llms import OpenAI<br>
llm = OpenAI(model_name="text-davinci-003")<br>
prompt = "你好"<br>
response = llm(prompt)<br>
# 你好，我是chatGPT,很高兴能够和你聊天。有什么我可以帮助你的吗？<br>
-我们用chatglm来演示该过程，封装一下即可<br>
from transformers import AutoTokenizer, AutoModel<br>
class chatGLM():<br>
    def __init__(self, model_name) -&gt; None:<br>
        self.tokenizer = AutoTokenizer.from_pretrained(model_name, <br>
trust_remote_code=True)<br>
        self.model = AutoModel.from_pretrained(model_name, <br>
trust_remote_code=True).half().cuda().eval()<br>
    def __call__(self, prompt) -&gt; Any:<br>
        response, _ = self.model.chat(self.tokenizer , prompt) # 这里演示未使用流式<br>
接口. stream_chat()<br>
        return response<br>
llm =  chatGLM(model_name="THUDM/chatglm-6b")<br>
prompt = "你好"<br>
response = llm(prompt)<br>
print("response: %s"%response)<br>
“”“<br>
response: 你好 ！我是人工智能助手 ChatGLM-6B，很高兴见到你，欢迎问我任何问题。<br>
”“”<br>
from langchain import PromptTemplate<br>
template = """<br>
Explain the concept of {concept} in couple of lines<br>
"""<br>
prompt = PromptTemplate(input_variables=["concept"], template=template)<br>
prompt = prompt.format(concept="regularization")<br>
print(“prompt=%s”%prompt)<br>
#'\nExplain the concept of regularization in couple of lines\n'<br>
</p>

<h2>第 4 页</h2>

<p>8.3 LangChain 如何链接多个组件处理一个特定的下游任务？<br>
8.4 LangChain 如何Embedding &amp; vector store？<br>
Emebdding这个过程想必大家很熟悉，简单理解就是把现实中的信息通过各类算法编码成一个高维向量，便于计<br>
算机快速计算。<br>
-------------------------<br>
template = "请给我解释一下{concept}的意思"<br>
prompt = PromptTemplate(input_variables=["concept"], template=template)<br>
prompt = prompt.format(concept="人工智能")<br>
print(“prompt=%s”%prompt)<br>
#'\n请给我解释一下人工智能的意思\n'<br>
#chains ---------<br>
from langchain.chains import LLMChain<br>
chain = LLMChain(llm=openAI(), prompt=promptTem)<br>
print(chain.run("你好"))<br>
#chains ---------Chatglm对象不符合LLMChain类llm对象要求，模仿一下<br>
class DemoChain():<br>
    def __init__(self, llm, prompt) -&gt; None:<br>
        self.llm = llm<br>
        self.prompt = prompt<br>
    def run(self, query) -&gt; Any:<br>
        prompt = self.prompt.format(concept=query)<br>
        print("query=%s  -&gt;prompt=%s"%(query, prompt))<br>
        response = self.llm(prompt) <br>
        return response<br>
    <br>
chain = DemoChain(llm=llm, prompt=promptTem)<br>
print(chain.run(query="天道酬勤"))<br>
“”“<br>
query=天道酬勤  -&gt;prompt=请给我解释一下天道酬勤的意思<br>
天道酬勤是指自然界的规律认为只要一个人勤奋努力，就有可能会获得成功。这个成语的意思<br>
是说，尽管一个人可能需要付出很多努力才能取得成功，但只要他/她坚持不懈地努力，就有可<br>
能会得到回报。<br>
”“”<br>
1. DL的语言模型建模一般开头都是word embedding，看情况会加position embedding。比如咱们的LLM的建模<br>
2. 常规检索一般是把refernce数据都先Embedding入库，服务阶段query进来Embedding后再快速在库中查询相<br>
似topk。比如langchain-chatGLM的本地知识库QA系统的入库和检测过程。<br>
3. 多模态的方案：同时把语音，文字，图片用不同的模型做Embedding后，再做多模态的模型建模和多模态交<br>
互。比如这两天的Visual-chatGLM.<br>
#官方示例代码，用的OpenAI的ada的文本Embedding模型<br>
#1） Embeding model<br>
</p>

<h2>第 5 页</h2>

<p>from langchain.embeddings import OpenAIEmbeddings<br>
embeddings = OpenAIEmbeddings(model_name="ada")<br>
query_result = embeddings.embed_query("你好")<br>
#2) 文本切割<br>
from langchain.text_splitter import RecursiveCharacterTextSplitter<br>
text_splitter = RecursiveCharacterTextSplitter(<br>
    chunk_size=100, chunk_overlap=0<br>
)<br>
texts = """天道酬勤”并不是鼓励人们不劳而获，而是提醒人们要遵循自然规律，通过不断的<br>
努力和付出来追求自己的目标。\n这种努力不仅仅是指身体上的劳动，<br>
也包括精神上的努力和思考，以及学习和适应变化的能力。\n只要一个人具备这些能力，他就<br>
有可能会获得成功。"""<br>
texts = text_splitter.create_documents([texts])<br>
print(texts[0].page_content)<br>
# 3)入库检索，官方使用的Pinecone,他提供一个后台管理界面 | 用户需求太大，不好用了已<br>
经，一直加载中....<br>
import pinecone<br>
from langchain.vectorstores import Pinecone<br>
pinecone.init(api_key=os.getenv(""), enviroment=os.getenv(""))<br>
index_name = "demo"<br>
search = Pinecone.from_documents(texts=texts, embeddings, index_name=index_name)<br>
query = "What is magical about an autoencoder?"<br>
result = search.similarity_search(query)<br>
#---------------------------------------------这里参考langchain-chatglm代码<br>
# 1） Embedding model:  text2vec-large-chinese<br>
from langchain.embeddings.huggingface import HuggingFaceEmbeddings<br>
embeddings = HuggingFaceEmbeddings(model_name="GanymedeNil/text2vec-large-chinese",<br>
                                    model_kwargs={'device': "cuda"})<br>
query_result = embeddings.embed_query("你好")<br>
#2)文本分割， 这里仅为了方便快速看流程，实际应用的会复杂一些<br>
texts = """天道酬勤”并不是鼓励人们不劳而获，而是提醒人们要遵循自然规律，通过不断的<br>
努力和付出来追求自己的目标。\n这种努力不仅仅是指身体上的劳动，<br>
也包括精神上的努力和思考，以及学习和适应变化的能力。\n只要一个人具备这些能力，他就<br>
有可能会获得成功。"""<br>
from langchain.text_splitter import CharacterTextSplitter<br>
from langchain.docstore.document import Document<br>
class TextSpliter(CharacterTextSplitter):<br>
    def __init__(self, separator: str = "\n\n", **kwargs: Any):<br>
        super().__init__(separator, **kwargs)<br>
    def split_text(self, text: str) -&gt; List[str]:<br>
        texts = text.split("\n")<br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,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**************************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***************************==" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>LangChain 存在哪些问题及方法方案？<br>
1. LangChain 低效的令牌使用问题<br>
2. LangChain 文档的问题<br>
3. LangChain 太多概念容易混淆，过多的“辅助”函数问题<br>
简单的分割函数：<br>
4. LangChain 行为不一致并且隐藏细节问题<br>
        texts = [Document(page_content=text, metadata={”from“: "知识库.txt"}) for <br>
text in texts]<br>
        return texts<br>
    <br>
text_splitter = TextSpliter()<br>
texts = text_splitter.split_text(texts)<br>
#3) 直接本地存储<br>
vs_path = "./demo-vs"<br>
from langchain.vectorstores import FAISS<br>
docs = embeddings.embed_documents(sentences)<br>
vector_store = FAISS.from_documents(texts, embeddings)<br>
vector_store.save_local(vs_path)<br>
vector_store = FAISS.load_local(vs_path, embeddings)<br>
related_docs_with_score = vector_store.similarity_search_with_score(query, k=2)<br>
• 问题：Langchain的一个重要问题是它的令牌计数功能，对于小数据集来说，它的效率很低。虽然一些开发<br>
人员选择创建自己的令牌计数函数，但也有其他解决方案可以解决这个问题。<br>
• 解决方案：Tiktoken是OpenAI开发的Python库，用于更有效地解决令牌计数问题。它提供了一种简单的方法<br>
来计算文本字符串中的令牌，而不需要使用像Langchain这样的框架来完成这项特定任务。<br>
• 问题：文档是任何框架可用性的基石，而Langchain因其不充分且经常不准确的文档而受到指责。误导性的<br>
文档可能导致开发项目中代价高昂的错误，并且还经常有404错误页面。这可能与Langchain还在快速发展有<br>
关，作为快速的版本迭代，文档的延后性问题<br>
• 问题：Langchain的代码库因很多概念让人混淆而备受批评，这使得开发人员很难理解和使用它。这种问题<br>
的一个方面是存在大量的“helper”函数，仔细检查就会发现它们本质上是标准Python函数的包装器。开发人员<br>
可能更喜欢提供更清晰和直接访问核心功能的框架，而不需要复杂的中间功能。<br>
• 问题：LangChain因隐藏重要细节和行为不一致而受到批评，这可能导致生产系统出现意想不到的问题。<br>
</p>

<h2>第 7 页</h2>

<div class="image-container">
  <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAIAAADZrBkAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABAGlDQ1BJQ0NCYXNlZChSR0IsKQAAeJxjYGA8wAAELAYMDLl5JUVB7k4KEZFRCuwPGBiBEAwSk4sLGHADoKpv1yBqL+viUYcLcKakFicD6Q9ALFMEtBxoJA+QLZIOYSuA2EkQtgmIXV5SUAJke4DYRSFBzkB2DJCtkY7ETkJiJxcUgdS3ANk2uTmlyQh3M/Ck5oUGQ/kyIH9DmPmLGBgsvjIwME9AiCXNZGDY3srAIHELIaaygIGBH2jutvMIMURYFCQWJYKFWICYKS2NgeHTcgYG3kgGBuELDAxc0bAAwOEmkHkyDO4M+UCYzpDDkAoU8WTIY0hm0AOyjBgMGAwZzABbAj2Z08JxjAAAATJJREFUeJyVk8lKw1AUhvNyLgTrsBcX+ggqiPgGCoKgO12qYMW0LhS7cijKjbbVagetVKsJlLpJp7QkGj8IWFtjuDmEcIf/O/+5k+L2R8qsrj7fTGdOIkIdEepC/sL1C+WnpVvN2YfTocv94auDudzZckmD39GLQdidWZvQ4qMitq0XWo79V7dZyeK/VbnvYfjATKaOXtt139wEDIXw72HUNibib1bjP4bAp8+NPSDNnvEYwPisjXVHRKzz5UgCpt01Ok2FvV4sJOV9sJnJJBQqXiun5bGNl1uqC42tl8HU0EUu5ZMg4bak++lgBRLuAKLGE2IQ2eMm3q3GuHaI2JW/XCSdSh8jQ+zKXOW2Y+8aRaYQZOsf3mDQw1kpXc/nzukyyJTnM4h58fuZ8tGgy+CA7BtlBGyf9gVuhgAAAABJRU5ErkJggg==" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>eg: Langchain ConversationRetrievalChain的一个有趣的方面，它涉及到输入问题的重新措辞。这种重复措辞有<br>
时会非常广泛，甚至破坏了对话的自然流畅性，使对话脱离了上下文。<br>
5. LangChain 缺乏标准的可互操作数据类型问题<br>
LangChain 替代方案？<br>
是否有更好的替代方案可以提供更容易使用、可伸缩性、活动性和特性。<br>
知识星球<br>
• 问题：缺乏表示数据的标准方法。这种一致性的缺乏可能会阻碍与其他框架和工具的集成，使其在更广泛的<br>
机器学习工具生态系统中工作具有挑战性。<br>
• LlamaIndex是一个数据框架，它可以很容易地将大型语言模型连接到自定义数据源。它可用于存储、查询和<br>
索引数据，还提供了各种数据可视化和分析工具。<br>
• Deepset Haystack是另外一个开源框架，用于使用大型语言模型构建搜索和问答应用程序。它基于Hugging <br>
Face Transformers，提供了多种查询和理解文本数据的工具。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:32:43</p>
        </div>
    </div>
</body>
</html>