<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>18-大模型（LLMs）RAG 版面分析——文本分块面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>18-大模型（LLMs）RAG 版面分析——文本分块面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="18-大模型（LLMs）RAG 版面分析——文本分块面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="18-大模型（LLMs）RAG 版面分析——文本分块面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）RAG 版面分析——文本分块面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年03月19日 22:30<br>
一、为什么需要对文本分块？<br>
使用大型语言模型（LLM）时，切勿忽略文本分块的重要性，其对处理结果的好坏有重大影响。<br>
考虑以下场景：你面临一个几百页的文档，其中充满了文字，你希望对其进行摘录和问答式处<br>
理。在这个流程中，最初的一步是提取文档的嵌入向量，但这样做会带来几个问题：<br>
因此，恰当地实施文本分块不仅能够提升文本的整体品质和可读性，还能够预防由于信息丢失或不<br>
当分块引起的问题。这就是为何在处理长篇文档时，采用文本分块而非直接处理整个文档至关重要<br>
的原因。<br>
二、能不能介绍一下常见的文本分块方法？<br>
2.1 一般的文本分块方法<br>
如果不借助任何包，直接按限制长度切分方案：<br>
• 大模型（LLMs）RAG 版面分析——文本分块面<br>
• 一、为什么需要对文本分块？<br>
• 二、能不能介绍一下常见的文本分块方法？<br>
• 2.1 一般的文本分块方法<br>
• 2.2 正则拆分的文本分块方法<br>
• 2.3 Spacy Text Splitter 方法<br>
• 2.4 基于 langchain 的 CharacterTextSplitter 方法<br>
• 2.5 基于 langchain 的 递归字符切分 方法<br>
• 2.6 HTML 文本拆分 方法<br>
• 2.7 Mrrkdown 文本拆分 方法<br>
• 2.8 Python代码拆分 方法<br>
• 2.9 LaTex 文本拆分 方法<br>
• 致谢<br>
• 信息丢失的风险：试图一次性提取整个文档的嵌入向量，虽然可以捕捉到整体的上下文，但也<br>
可能会忽略掉许多针对特定主题的重要信息，这可能会导致生成的信息不够精确或者有所缺<br>
失。<br>
• 分块大小的限制：在使用如OpenAI这样的模型时，分块大小是一个关键的限制因素。例如，<br>
GPT-4模型有一个32K的窗口大小限制。尽管这个限制在大多数情况下不是问题，但从一开始<br>
就考虑到分块大小是很重要的。<br>
text = "我是一个名为 ChatGLM3-6B 的人工智能助手，是基于清华大学 KEG 实验室和智谱 AI <br>
公司于 2023 年共同训练的语言模型开发的。我的目标是通过回答用户提出的问题来帮助他们<br>
解决问题。由于我是一个计算机程序，所以我没有实际的存在，只能通过互联网来与用户交<br>
流。"<br>
chunks = []<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>2.2 正则拆分的文本分块方法<br>
chunk_size = 128 <br>
for i in range(0, len(text), chunk_size):<br>
    chunk = text[i:i + chunk_size]<br>
    chunks.append(chunk)<br>
chunks<br>
&gt;&gt;&gt;<br>
[<br>
    '我是一个名为 ChatGLM3-6B 的人工智能助手，是基于清华大学 KEG 实验室和智谱 AI 公<br>
司于 2023 年共同训练的语言模型开发的。我的目标是通过回答用户提出的问题来帮助他们解<br>
决问题。由于我是一个计算机程序，所以我没有实际的存在，只能通过互联网',<br>
    '来与用户交流。'<br>
]<br>
• 动机：【一般的文本分块方法】能够按长度进行分割，但是对于一些长度偏长的句子，容易从<br>
中间切开；<br>
• 方法：在中文文本分块的场景中，正则表达式可以用来识别中文标点符号，从而将文本拆分成<br>
单独的句子。这种方法依赖于中文句号、“问号”、“感叹号”等标点符号作为句子结束的标志。<br>
• 特点：虽然这种基于模式匹配的方法可能不如基于复杂语法和语义分析的方法精确，但它在大<br>
多数情况下足以满足基本的句子分割需求，并且实现起来更为简单直接。<br>
import re<br>
def split_sentences(text):<br>
    # 使用正则表达式匹配中文句子结束的标点符号<br>
    sentence_delimiters = re.compile(u'[。？！；]|\n')<br>
    sentences = sentence_delimiters.split(text)<br>
    # 过滤掉空字符串<br>
    sentences = [s.strip() for s in sentences if s.strip()]<br>
    return sentences<br>
text ="文本分块是自然语言处理（NLP）中的一项关键技术，其作用是将较长的文本切割成更<br>
小、更易于处理的片段。这种分割通常是基于单词的词性和语法结构，例如将文本拆分为名词<br>
短语、动词短语或其他语义单位。这样做有助于更高效地从文本中提取关键信息。"<br>
sentences = split_sentences(text)<br>
print(sentences)<br>
​&gt;&gt;&gt;<br>
#output<br>
[<br>
    '文本分块是自然语言处理（NLP）中的一项关键技术，其作用是将较长的文本切割成更<br>
小、更易于处理的片段', <br>
    '这种分割通常是基于单词的词性和语法结构，例如将文本拆分为名词短语、动词短语或其<br>
他语义单位', <br>
    '这样做有助于更高效地从文本中提取关键信息'<br>
]<br>
</p>

<h2>第 3 页</h2>

<p>在上面例子中，我们并没有采用任何特定的方式来分割句子。另外，还有许多其他的文本分块技术<br>
可以使用，例如词汇化（tokenizing）、词性标注（POS tagging）等。<br>
2.3 Spacy Text Splitter 方法<br>
2.4 基于 langchain 的 CharacterTextSplitter 方法<br>
使用CharacterTextSplitter，一般的设置参数为：chunk_size、 chunk_overlap、separator和<br>
strip_whitespace。<br>
• 介绍：Spacy是一个用于执行自然语言处理（NLP）各种任务的库。它具有文本拆分器功能，<br>
能够在进行文本分割的同时，保留分割结果的上下文信息。<br>
import spacy <br>
input_text = "文本分块是自然语言处理（NLP）中的一项关键技术，其作用是将较长的文本切<br>
割成更小、更易于处理的片段。这种分割通常是基于单词的词性和语法结构，例如将文本拆分<br>
为名词短语、动词短语或其他语义单位。这样做有助于更高效地从文本中提取关键信息。"  <br>
nlp = spacy.load( "zh_core_web_sm" ) <br>
doc = nlp(input_text) <br>
for s in doc.sents: <br>
    print (s)<br>
&gt;&gt;&gt;<br>
[<br>
    '文本分块是自然语言处理（NLP）中的一项关键技术，其作用是将较长的文本切割成更<br>
小、更易于处理的片段。',<br>
    "这种分割通常是基于单词的词性和语法结构，例如将文本拆分为名词短语、动词短语或其<br>
他语义单位。",<br>
    "这样做有助于更高效地从文本中提取关键信息。"<br>
]<br>
from langchain.text_splitter import CharacterTextSplitter<br>
text_splitter = CharacterTextSplitter(chunk_size = 35, chunk_overlap=0, <br>
separator='', strip_whitespace=False)<br>
text_splitter.create_documents([text])<br>
&gt;&gt;&gt;<br>
[<br>
    Document(page_content='我是一个名为 ChatGLM3-6B 的人工智能助手，是基于清华大学 <br>
'),<br>
    Document(page_content='KEG 实验室和智谱 AI 公司于 2023 年共同训练的语言模型开<br>
发'),<br>
    Document(page_content='的。我的目标是通过回答用户提出的问题来帮助他们解决问题。<br>
由于我是一个计'),<br>
    Document(page_content='算机程序，所以我没有实际的存在，只能通过互联网来与用户交<br>
流。')<br>
]<br>
</p>

<h2>第 4 页</h2>

<p>2.5 基于 langchain 的 递归字符切分 方法<br>
使用RecursiveCharacterTextSplitter，一般的设置参数为：chunk_size、 chunk_overlap。<br>
与CharacterTextSplitter不同，RecursiveCharacterTextSplitter不需要设置分隔符，默认的几个分隔<br>
符如下：<br>
拆分器首先查找两个换行符（段落分隔符）。一旦段落被分割，它就会查看块的大小，如果块太<br>
大，那么它会被下一个分隔符分割。如果块仍然太大，那么它将移动到下一个块上，以此类推。<br>
2.6 HTML 文本拆分 方法<br>
#input text<br>
input_text = "文本分块是自然语言处理（NLP）中的一项关键技术，其作用是将较长的文本切<br>
割成更小、更易于处理的片段。这种分割通常是基于单词的词性和语法结构，例如将文本拆分<br>
为名词短语、动词短语或其他语义单位。这样做有助于更高效地从文本中提取关键信息。"  <br>
from langchain.text_splitter import RecursiveCharacterTextSplitter <br>
text_splitter = RecursiveCharacterTextSplitter( <br>
    chunk_size = 100 , #设置所需的文本大小<br>
    chunk_overlap = 20 ) <br>
chunks = text_splitter.create_documents([input_text]) <br>
print (chunks)<br>
​&gt;&gt;&gt;<br>
[<br>
    Document(page_content='文本分块是自然语言处理（NLP）中的一项关键技术，其作用是<br>
将较长的文本切割成更小、更易于处理的片段。这种分割通常是基于单词的词性和语法结构，<br>
例如将文本拆分为名词短语、动词短语或其他语义单位。这样做有助'), <br>
    Document(page_content='短语、动词短语或其他语义单位。这样做有助于更高效地从文本<br>
中提取关键信息。')]<br>
"\n\n" - 两个换行符，一般认为是段落分隔符<br>
"\n" - 换行符<br>
" " - 空格<br>
"" - 字符<br>
• 介绍：HTML文本拆分器是一种结构感知的文本分块工具。它能够在HTML元素级别上进行文本<br>
拆分，并且会为每个分块添加与之相关的标题元数据。<br>
• 特点：对HTML结构的敏感性，能够精准地处理和分析HTML文档中的内容。<br>
#input html string<br>
 html_string = """ <br>
&lt;!DOCTYPE html&gt; <br>
&lt;html&gt; <br>
&lt;body&gt; <br>
    &lt;div&gt; <br>
        &lt;h1&gt;Mobot&lt;/h1&gt; <br>
        &lt;p&gt;一些关于Mobot的介绍文字。&lt;/p&gt; <br>
</p>

<h2>第 5 页</h2>

<p>仅提取在header_to_split_on参数中指定的HTML标题。<br>
        &lt;div&gt; <br>
            &lt;h2&gt;Mobot主要部分&lt;/h2&gt; <br>
            &lt;p&gt;有关Mobot的一些介绍文本。&lt;/p&gt; <br>
            &lt;h3&gt;Mobot第1小节&lt;/h3&gt; <br>
            &lt;p&gt;有关Mobot第一个子主题的一些文本。&lt;/p&gt; <br>
            &lt;h3&gt;Mobot第2小节&lt;/h3&gt; <br>
            &lt;p&gt;关于Mobot的第二个子主题的一些文字。&lt;/p&gt; <br>
        &lt;/div&gt; <br>
        &lt;div&gt; <br>
            &lt;h2&gt;Mobot&lt;/h2&gt; <br>
            &lt;p&gt;关于Mobot的一些文字&lt;/p&gt; <br>
        &lt;/ div&gt; <br>
        &lt;br&gt; <br>
        &lt;p&gt;关于Mobot的一些结论性文字&lt;/p&gt; <br>
    &lt;/div&gt; <br>
&lt;/body&gt; <br>
&lt;/html&gt; <br>
"""<br>
 headers_to_split_on = [ <br>
    ( "h1" , "Header 1" ), <br>
    ( "h2" , "标题 2" ), <br>
    ( "h3" , "标题 3" ), <br>
] <br>
from langchain.text_splitter import HTMLHeaderTextSplitter <br>
html_splitter = HTMLHeaderTextSplitter(headers_to_split_on=headers_to_split_on) <br>
html_header_splits = html_splitter.split_text(html_string) <br>
print(html_header_split)<br>
​&gt;&gt;&gt;<br>
[<br>
    Document(page_content='Mobot'), <br>
    Document(page_content='一些关于Mobot的介绍文字。\nMobot主要部分 Mobot第1小节 <br>
Mobot第2小节', metadata={'Header 1': 'Mobot'}), <br>
    Document(page_content='有关Mobot的一些介绍文本。', metadata={'Header 1': <br>
'Mobot', '标题 2': 'Mobot主要部分'}), <br>
    Document(page_content='有关Mobot第一个子主题的一些文本。', metadata={'Header <br>
1': 'Mobot', '标题 2': 'Mobot主要部分', '标题 3': 'Mobot第1小节'}), <br>
    Document(page_content='关于Mobot的第二个子主题的一些文字。', metadata={'Header <br>
1': 'Mobot', '标题 2': 'Mobot主要部分', '标题 3': 'Mobot第2小节'}), <br>
    Document(page_content='Mobot div&gt;', metadata={'Header 1': 'Mobot'}), <br>
    Document(page_content='关于Mobot的一些文字  \n关于Mobot的一些结论性文字', <br>
metadata={'Header 1': 'Mobot', '标题 2': 'Mobot'})<br>
]<br>
</p>

<h2>第 6 页</h2>

<p>2.7 Mrrkdown 文本拆分 方法<br>
MarkdownHeaderTextSplitter 能够根据设定的 headers_to_split_on 参数，将 Markdown 文本进行<br>
拆分。这一功能使得用户可以便捷地根据指定的标题将 Markdown 文件分割成不同部分，从而提<br>
高编辑和管理的效率。<br>
2.8 Python代码拆分 方法<br>
• 介绍：Markdown文本拆分是一种根据Markdown的语法规则（例如标题、Bash代码块、图片和<br>
列表）进行文本分块的方法。<br>
• 特点：具有对结构的敏感性，能够基于Markdown文档的结构特点进行有效的文本分割。<br>
markdown_text = '# Mobot\n\n ## Stone\n\n这是python  \n这是\n\n ## markdown\n\n 这<br>
是中文文本拆分'<br>
from langchain.text_splitter import MarkdownHeaderTextSplitter<br>
headers_to_split_on = [<br>
    ("#", "Header 1"),<br>
    ("##", "Header 2"),<br>
    ("###", "Header 3"),<br>
]<br>
markdown_splitter = <br>
MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on)<br>
md_header_splits = markdown_splitter.split_text(markdown_text)<br>
print(md_header_splits)<br>
​&gt;&gt;&gt;<br>
[<br>
    Document(page_content='这是python\n这是', metadata={'Header 1': 'Mobot', <br>
'Header 2': 'Stone'}), <br>
    Document(page_content='这是中文文本拆分', metadata={'Header 1': 'Mobot', <br>
'Header 2': 'markdown'})<br>
]<br>
python_text = """<br>
class Person:<br>
  def __init__(self, name, age):<br>
    self.name = name<br>
    self.age = age<br>
p1 = Person("John", 36)<br>
for i in range(10):<br>
    print (i)<br>
​"""<br>
from langchain.text_splitter import PythonCodeTextSplitter<br>
python_splitter = PythonCodeTextSplitter(chunk_size=100, chunk_overlap=0)<br>
</p>

<h2>第 7 页</h2>

<p>2.9 LaTex 文本拆分 方法<br>
LaTex文本拆分工具是一种专用于代码分块的工具。它通过解析LaTex命令来创建各个块，这些块<br>
按照逻辑组织，如章节和小节等。这种方式能够产生更加准确且与上下文相关的分块结果，从而有<br>
效地提升LaTex文档的组织和处理效率。<br>
python_splitter.create_documents([python_text])<br>
&gt;&gt;&gt;<br>
[<br>
    Document(page_content='class Person:\n  def __init__(self, name, age):\n    <br>
self.name = name\n    self.age = age'),<br>
    Document(page_content='p1 = Person("John", 36)\n\nfor i in range(10):\n    <br>
print (i)')<br>
]<br>
#input Latex string<br>
latex_text = """documentclass{article}begin{document}maketitlesection{Introduction}<br>
大型语言模型 (LLM) 是一种机器学习模型，可以在大量文本数据上进行训练，以生成类似人类<br>
的语言。近年来，法学硕士在各种自然语言处理任务中取得了重大进展，包括语言翻译、文本<br>
生成和情感分析。subsection{法学硕士的历史}最早的法学硕士是在 20 世纪 80 年代开发的<br>
和 20 世纪 90 年代，但它们受到可处理的数据量和当时可用的计算能力的限制。然而，在过<br>
去的十年中，硬件和软件的进步使得在海量数据集上训练法学硕士成为可能，从而导致<br>
subsection{LLM 的应用}LLM 在工业界有许多应用，包括聊天机器人、内容创建和虚拟助理。<br>
它们还可以在学术界用于语言学、心理学和计算语言学的研究。end{document}""" <br>
from langchain.text_splitter import LatexTextSplitter <br>
Latex_splitter = LatexTextSplitter(chunk_size= 100 , chunk_overlap= 0 ) <br>
latex_splits = Latex_splitter.create_documents([latex_text]) <br>
print (latex_splits)<br>
&gt;&gt;&gt;<br>
[<br>
    <br>
Document(page_content='documentclass{article}begin{document}maketitlesection{Introd<br>
uction}大型语言模型 (LLM)'), <br>
    Document(page_content='是一种机器学习模型，可以在大量文本数据上进行训练，以生成<br>
类似人类的语言。近年来，法学硕士在各种自然语言处理任务中取得了重大进展，包括语言翻<br>
译、文本生成和情感分析。subsection{法学硕士的历史'), <br>
    Document(page_content='}最早的法学硕士是在'), <br>
    Document(page_content='20 世纪 80 年代开发的和 20 世纪 90'), <br>
    Document(page_content='年代，但它们受到可处理的数据量和当时可用的计算能力的限<br>
制。然而，在过去的十年中，硬件和软件的进步使得在海量数据集上训练法学硕士成为可能，<br>
从而导致subsection{LLM 的应用}LLM'), <br>
    Document(page_content='在工业界有许多应用，包括聊天机器人、内容创建和虚拟助理。<br>
它们还可以在学术界用于语言学、心理学和计算语言学的研究。end{document}')<br>
]<br>
</p>

<h2>第 8 页</h2>

<div class="image-container">
  <img src="18-大模型（LLMs）RAG 版面分析——文本分块面_page8_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>​在上述示例中，我们注意到代码分割时的重叠部分设置为0。这是因为在处理代码分割过程中，任<br>
何重叠的代码都可能完全改变其原有含义。因此，为了保持代码的原始意图和准确性，避免产生误<br>
解或错误，设置重叠部分为0是必要的。<br>
当你决定使用哪种分块器处理数据时，重要的一步是提取数据嵌入并将其存储在向量数据库<br>
（Vector DB）中。上面的例子中使用文本分块器结合 LanceDB 来存储数据块及其对应的嵌入。<br>
LanceDB 是一个无需配置、开源且无服务器的向量数据库，其数据持久化在硬盘驱动器上，允许<br>
用户在不超出预算的情况下实现扩展。此外，LanceDB 与 Python 数据生态系统兼容，因此你可以<br>
将其与现有的数据工具（如 pandas、pyarrow 等）结合使用。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:27</p>
        </div>
    </div>
</body>
</html>