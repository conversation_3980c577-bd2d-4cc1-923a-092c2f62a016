<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>63-LLM（大语言模型）部署加速方法——PagedAttention篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>63-LLM（大语言模型）部署加速方法——PagedAttention篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="63-LLM（大语言模型）部署加速方法——PagedAttention篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="63-LLM（大语言模型）部署加速方法——PagedAttention篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="63-LLM（大语言模型）部署加速方法——PagedAttention篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>LLM（大语言模型）部署加速方法——PagedAttention篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:50<br>
一、vLLM 用于大模型并行推理加速 存在什么问题？<br>
vLLM 用于大模型并行推理加速，其中核心改进是PagedAttention算法，在 vLLM 中，我们发现 LLM 服务的性<br>
能受到内存的瓶颈。在自回归解码过程中，LLM 的所有输入标记都会生成其key和value张量，并且这些张量保<br>
存在 GPU 内存中以生成下一个token。这些缓存的key和value张量通常称为 KV 缓存。KV缓存是：<br>
二、vLLM 如何 优化 大模型并行推理加速？<br>
vllm引入了PagedAttention，这是一种受操作系统中虚拟内存和分页的经典思想启发的注意力算法。<br>
三、什么是 PagedAttention？<br>
与传统的注意力算法不同，PagedAttention 允许在不连续的内存空间中存储连续的key和value。<br>
四、PagedAttention 如何存储 连续的key和value？<br>
具体来说，PagedAttention 将每个序列的 KV 缓存划分为块，每个块包含固定数量token的key和value。在注<br>
意力计算过程中，PagedAttention 内核有效地识别并获取这些块。<br>
图一：PagedAttention<br>
因为块在内存中不需要是连续的，所以我们可以像在操作系统的虚拟内存中一样以更灵活的方式管理key和<br>
value：可以将块视为页面，将token视为字节，将序列视为进程。序列的连续逻辑块通过块表映射到非连续物理<br>
块。当新代币生成时，物理块会按需分配。<br>
五、 PagedAttention 技术细节？<br>
• 占用大： LLaMA-13B 中的单个序列最多占用 1.7GB。<br>
• 动态变化：其大小取决于序列长度，序列长度变化很大且不可预测。因此，有效管理 KV 缓存提出了重大挑<br>
战。我们发现现有系统由于碎片和过度预留而浪费了60% - 80%的内存。<br>
1. 在 PagedAttention 中，内存浪费仅发生在序列的最后一个块中。实际上，这会导致内存使用接近最佳，浪费<br>
率低于 4%。事实证明，内存效率的提高非常有益：它允许系统将更多序列一起批处理，提高 GPU 利用率，<br>
从而显着提高吞吐量，如上面的性能结果所示；<br>
2. PagedAttention 还有另一个关键优势：高效的内存共享。例如，在并行采样中，从同一提示生成多个输出序<br>
列。在这种情况下，提示的计算和内存可以在输出序列之间共享。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="63-LLM（大语言模型）部署加速方法——PagedAttention篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="63-LLM（大语言模型）部署加速方法——PagedAttention篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图二： 采样过程<br>
PagedAttention 自然可以通过其块表实现内存共享。与进程共享物理页的方式类似<br>
六、 PagedAttention 如何 实现安全共享？<br>
图三 对多个输出进行采样的请求的示例生成过程<br>
PageAttention 的内存共享极大地降低了复杂采样算法的内存开销，例如并行采样和波束搜索，将其内存占用降<br>
低高达 55%。这可以将吞吐量提高高达 2.2 倍。<br>
七、 PagedAttention 源码介绍？<br>
PagedAttention 是 vLLM 背后的核心技术，vLLM 是 LLM 推理和服务引擎，支持各种具有高性能和易于使用的<br>
界面的模型。<br>
从vllm的源码中我们可以看出来，vllm是怎么样对于huggingface models上的模型进行推理优化的。<br>
• 动机：PagedAttention 中的不同序列可以通过将其逻辑块映射到同一物理块来共享块。这个时候就 设计到 <br>
如何 安全共享问题；<br>
• 思路：PagedAttention 跟踪物理块的引用计数并实现Copy-on-Write机制<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="63-LLM（大语言模型）部署加速方法——PagedAttention篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="63-LLM（大语言模型）部署加速方法——PagedAttention篇_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:25</p>
        </div>
    </div>
</body>
</html>