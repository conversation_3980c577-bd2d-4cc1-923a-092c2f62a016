<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>88-文本分类常见面试篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>88-文本分类常见面试篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="88-文本分类常见面试篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="88-文本分类常见面试篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="88-文本分类常见面试篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>文本分类常见面试篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月12日 06:39<br>
一、文本分类任务有哪些应用场景？<br>
文本分类时机器学习汇总常见的监督学习任务质疑，常见的应用场景如情感分类、新闻分类、主题<br>
分类、问答匹配、意图识别、推断等等。分类任务根据具体的数据集的标签情况，还可以分为二分<br>
类、多分类、多标签分类等。<br>
二、文本分类的具体流程？<br>
文本分类的流程一般包括文本预处理、特征提取、文本表示、最后分类输出。<br>
文本处理通常需要做分词及去除停用词等操作，常会使用一些分词工具，如hanlp、jieba、哈工大<br>
LTP、北大pkuseg等。<br>
三、fastText的分类过程？fastText的优点？<br>
fastText首先把输入转化为词向量，取平均，再经过线性分类器得到类别。输入的词向量可以是预<br>
先训练好的，也可以随机初始化，跟着分类任务一起训练。<br>
fastText是一个快速文本分类算法，与基于神经网络的分类算法相比有两大优点： 1、fastText在保<br>
持高精度的情况下加快了训练速度和测试速度 2、fastText不需要预训练好的词向量，fastText会自<br>
己训练词向量 3、fastText两个重要的优化：使用层级 Softmax提升效率、采用了char-level的n-<br>
gram作为附加特征。<br>
四、TextCNN进行文本分类的过程?<br>
• 文本分类常见面试篇<br>
• 一、文本分类任务有哪些应用场景？<br>
• 二、文本分类的具体流程？<br>
• 三、fastText的分类过程？fastText的优点？<br>
• 四、TextCNN进行文本分类的过程?<br>
• 五、TextCNN可以调整哪些参数？<br>
• 六、文本分类任务使用的评估指标有哪些？<br>
• 致谢<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="88-文本分类常见面试篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>卷积神经网络的核心思想是捕捉局部特征，对于文本来说，局部特征就是由若干单词组成的滑动窗<br>
口，类似于N-gram。卷积神经网络的优势在于能够自动地对N-gram特征进行组合和筛选，获得不<br>
同抽象层次的语义信息。因此文本分类任务中可以利用CNN来提取句子中类似 n-gram 的关键信<br>
息。<br>
第一层为输入层。将最左边的7乘5的句子矩阵，每行是词向量，维度=5，这个可以类比为图像中<br>
的原始像素点了。<br>
图中的输入层实际采用了双通道的形式，即有两个 n × k<br>
的输入矩阵，其中一个用预训练好的词嵌入表达，并且在训练过程中不再发生变化；另外一个也由<br>
同样的方式初始化，但是会作为参数，随着网络的训练过程发生改变。<br>
第二层为卷积层。然后经过有 filter_size=(2,3,4) 的一维卷积层，每个filter_size 有两个输出 <br>
channel。第三层是一个1-max pooling层，这样不同长度句子经过pooling层之后都能变成定长的表<br>
示了。<br>
最后接一层全连接的 softmax 层，输出每个类别的概率。<br>
每个词向量可以是预先在其他语料库中训练好的，也可以作为未知的参数由网络训练得到。<br>
五、TextCNN可以调整哪些参数？<br>
• 输入词向量表征：词向量表征的选取(如选word2vec还是GloVe)<br>
• 卷积核大小：一个合理的值范围在1~10。若语料中的句子较长，可以考虑使用更大的卷积核。<br>
另外，可以在寻找到了最佳的单个filter的大小后，尝试在该filter的尺寸值附近寻找其他合适值<br>
来进行组合。实践证明这样的组合效果往往比单个最佳filter表现更出色<br>
• feature map 特征图个数：主要考虑的是当增加特征图个数时，训练时间也会加长，因此需要权<br>
衡好。这个参数会影响最终特征的维度，维度太大的话训练速度就会变慢。这里在100-600之<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="88-文本分类常见面试篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="88-文本分类常见面试篇_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>六、文本分类任务使用的评估指标有哪些？<br>
准确率、召回率、ROC，AUC，F1、混淆矩阵<br>
知识星球<br>
间调参即可。当特征图数量增加到将性能降低时，可以加强正则化效果，如将dropout率提高过<br>
0.5<br>
• 激活函数：ReLU和tanh<br>
• 池化策略：1-max pooling表现最佳，复杂任务选择k-max<br>
• 正则化项(dropout/L2)：指对CNN参数的正则化，可以使用dropout或L2，但能起的作用很小，<br>
可以试下小的dropout率(&lt;0.5)，L2限制大一点<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:28</p>
        </div>
    </div>
</body>
</html>