<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>53-大模型分布式训练故障恢复篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>53-大模型分布式训练故障恢复篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="53-大模型分布式训练故障恢复篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="53-大模型分布式训练故障恢复篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型分布式训练故障恢复篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年12月24日 00:35<br>
一、为什么 大模型分布式训练 需要 故障恢复？<br>
大规模分布式训练场景由于集群规模过大，芯片设备、主机、网络等均会不定期出现故障，如果此<br>
时想要继续训练，那么就需要从上次存储的ckpt进行恢复，然后resume training。这个过程中产<br>
生的时间间隔就是集群故障带来的开销，虽然不可避免，但是我们可以尽可能的减少故障带来的影<br>
响。<br>
二、如何获取最优的ckpt存储间隔？<br>
假设我们均匀的同步存储ckpt，那么这个时候我们就需要根据集群环境获取最优的ckpt interval，<br>
首先集群时间损失可以做如下定义，故障随机发生在ckpt interval区间：<br>
通过导数为0，可以根据集群环境，得到对应最优的ckpt interval，当然ckpt interval肯定是远大于1<br>
的。<br>
三、ckpt存储能否实现异步或者部分掩盖？<br>
异步存储ckpt的最大问题：设备内存踩踏，如果在另外一个stream里做D2H数据拷贝，同时模型训<br>
练过程继续运行，那么就会有一个非常尴尬的问题，就是所有参数的D2H操作还没有完成，这时候<br>
下一个step已经开始更新参数或优化器状态，那么后面没完成的操作就会拷贝错误的数据。<br>
由于ckpt存储时间不可控，不能确定是否小于下一个step的执行时间，所以内存踩踏的问题不可避<br>
免，即完全异步的方案是不可行的。如果要想做到部分掩盖，本人认为可以有如下两个方案供选<br>
择：<br>
四、断点续训/临终遗言是否真实可行？<br>
绝对可行，但有一点受限。大模型训练场景多是DP/TP/PP多维并行场景，任意一个节点出现故障<br>
的可能性都是存在的。如果任何一个PP stage都存在一个完整的TP Group，就是该rank对应的节<br>
点没发生故障，那么整网参数就是完整的，可以在框架侧捕获分布式error做临终参数存储，这样<br>
ckpt interval就趋近于0。如果不满足整网参数完整这个条件，那是做不到临终存储整网参数和优化<br>
器状态。根据经验，框架侧开发并不会很难，需要结合rank编排做定制研发。当然如果故障发生在<br>
参数或存储器状态更新的时候，那也是无法保证整网参数完整性的，这种情况也不能做临终处理。<br>
    集群时间损失 = ckpt存储耗时 + 故障期望次数 * 恢复训练耗时（ckpt interval/2+恢复<br>
训练耗时）<br>
• 在训练脚本侧修改，在下一次更新参数或优化器状态之前，强制等待ckpt存储完成，这样可以<br>
尽可能的overlap；<br>
• 随便yy一下，在框架侧修改，比如H2D non-blocking操作在后续有数据依赖的时候，会强制加<br>
sync point，框架侧也可以新增一个D2H拷贝，在后续有数据写操作的时候，强制添加sync <br>
point。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="53-大模型分布式训练故障恢复篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>对于临终遗言/断点续训，基于训练框架对深度学习框架做深度定制是比较好的出路。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:26</p>
        </div>
    </div>
</body>
</html>