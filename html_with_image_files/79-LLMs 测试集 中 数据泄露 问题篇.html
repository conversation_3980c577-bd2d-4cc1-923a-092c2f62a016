<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>79-LLMs 测试集 中 数据泄露 问题篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>79-LLMs 测试集 中 数据泄露 问题篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="79-LLMs 测试集 中 数据泄露 问题篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="79-LLMs 测试集 中 数据泄露 问题篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>LLMs 测试集 中 数据泄露 问题篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:14<br>
一、什么是 LLMs 测试集数据泄露 问题？<br>
数据泄露（data contamination）是指模型测试集的数据被无意地(!)包含在了训练集中。（如果是<br>
故意的，比如train on测试集，那就是另一个话题了）。<br>
这种情况在大模型时代是很难避免的。其实在Common Crawl刚开始被用作训练集时就有不少人意<br>
识到了这个问题。<br>
比如这篇论文发现，在T5所用的C4数据集中，包含了2-50%不等的GLUE benchmark的原题。导<br>
致T5在GLUE极亮眼的数据在当时遭到了不小质疑。<br>
在此之后基本所有的LLMs在论文或者report中都会有单独的一章Data contamination analysis来证<br>
明自己评测的可信性。这里我附上几个具有代表性的例子：GPT-3，GPT-4，Llama-2.<br>
二、如何解决 LLMs 测试集数据泄露 问题？<br>
处理数据泄露最成熟的方法是识别测试集中的已泄露样本和未泄露样本，分别构建dirty set和<br>
clean set，然后比较模型在这两个数据集上的性能差异。<br>
注：GPT-3在De-&gt;En WMT16翻译任务上获得了43 bleu score的优秀总体成绩。但如果区分dirty<br>
和clean set的结果，则GPT-3在未泄露样本（clean）上的分数只有40.8，而在泄露样本（dirty）<br>
上获得了47.4的超高分，这说明GPT-3通过强大的记忆里在评测集上取得了额外的优势。其真实<br>
的翻译水平应接近40.3，而不是43分的总分。<br>
然而，在我们实际研究或开发过程中，这种方法是很难复刻的。原因在于，这种方式需要获取<br>
base model完整的训练集，从而识别测试集里的干净和泄漏样本。<br>
我们大部分常用的基座模型，包括一众中文大模型和Llama-2，都没有开源其训练数据。<br>
即使拿到训练数据，其庞大的数据量也会使整个处理过程非常耗时。例如，在Llama-2中为了识别<br>
测试集的数据泄露，在PySpark 1500核cluster运行了超过7个小时。<br>
• LLMs 测试集 中 数据泄露 问题篇<br>
• 一、什么是 LLMs 测试集数据泄露 问题？<br>
• 二、如何解决 LLMs 测试集数据泄露 问题？<br>
• 三、是否可以 避开训练集来处理 LLMs 测试集数据泄露 问题？<br>
• 3.1 如何 判断 网络上是否有原题？<br>
• 3.2 如何 判断答案是否存在？<br>
• 3.3 性能差异对比<br>
• 四、常见测试集有多少比例的数据泄露？<br>
• 致谢<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="79-LLMs 测试集 中 数据泄露 问题篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>三、是否可以 避开训练集来处理 LLMs 测试集数据泄露 问题？<br>
针对该问题，我们做出了一个假设：任何在网络上能够找到的测试集题目，都有很大的风险被包含<br>
在LLMs的训练数据中。<br>
可以直接使用搜索引擎来区分测试集中的样例。把所有测试样例分为三类：<br>
3.1 如何 判断 网络上是否有原题？<br>
判断网络上是否有原题的标准是：有80%以上的字符与测试样例完全重叠（用meteor来测量）。<br>
3.2 如何 判断答案是否存在？<br>
判断答案是否存在的标准是：使用完整的字符串匹配。<br>
3.3 性能差异对比<br>
比较模型在以上三个类别的性能差异。以C-Eval为例：<br>
这里Average是模型的总分，All Dirty包含了所有在网上能找到原题的测试样例，而Input-and-<br>
Label Contaminated则是网上能同时找到原题和答案的样例。<br>
如果只看总分（Average），那么Qwen-7B在C-Eval上超越了Baichuan整整3%。<br>
然而，模型能力的实际差距可能并没有那么大。结果显示Qwen在处理网上有原题的样本时性能格<br>
外出色，其准确率超越了clean set 整整5.5%。如此差距很可能说明Qwen在C-Eval上有潜在的过<br>
拟合现象。<br>
相比之下，Baichuan在clean set和泄露样例两者之间的差距则小的多，只有1.41%。<br>
如果只关注Qwen和Baichuan在clean set上的性能，那么两个模型实际上的差距只有1.1%。<br>
四、常见测试集有多少比例的数据泄露？<br>
仔细观察下来，常见的LLMs测试集均有很严重的数据泄露现象。<br>
例如C-Eval有超过46.14%的测试样例能够直接在Common Crawl里找到原题。MMLU也有接近<br>
37%的测试样例完整地出现在Common Crawl里。<br>
1. 干净样例：网络上找不到对应测试样例的题目或答案；<br>
2. 题目泄漏样例：网络上能够找到原题，但答案并没有一起出现；<br>
3. 题目-答案同时泄漏样例：测试样例的原题和答案同时出现在同一网页上。<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="79-LLMs 测试集 中 数据泄露 问题篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:23</p>
        </div>
    </div>
</body>
</html>