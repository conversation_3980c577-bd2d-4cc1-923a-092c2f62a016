<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>48-图解分布式训练（四） —— torch.multiprocessing 详细解析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>48-图解分布式训练（四） —— torch.multiprocessing 详细解析</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="48-图解分布式训练（四） —— torch.multiprocessing 详细解析_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="48-图解分布式训练（四） —— torch.multiprocessing 详细解析_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>图解分布式训练（四） —— torch.multiprocessing 详细解析<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 11:27<br>
一、torch.multiprocessing 函数介绍一下？<br>
torch.multiprocessing是本机multiprocessing模块的封装。封装了multiprocessing模块。它注册自定义的<br>
reducer，它使用共享内存为不同进程中的相同数据提供视图共享。一旦张量/存储被移动到shared_memory（参<br>
见sharememory()），就可以将其发送到其他进程而不进行其它任何操作。<br>
这个API与原始模块100％兼容，将import multiprocessing改为import torch.multiprocessing就可以将所有张量通<br>
过队列发送或通过其他机制共享转移到共享内存中<br>
由于API的相似性，我们没有记录这个软件包的大部分内容，我们建议您参考Python multiprocessing原始模块的<br>
文档。<br>
警告： 如果主进程突然退出（例如因为传入的信号），Python multiprocessing有时无法清理其子进程。这是一<br>
个已知的警告，所以如果您在中断解释器后看到任何资源泄漏，这可能意味着这刚刚发生在您身上。<br>
二、torch.multiprocessing 函数如何使用？<br>
返回一组当前系统支持的共享策略。<br>
返回共享CPU张量的当前策略<br>
设置共享CPU张量的策略<br>
参数: new_strategy(str)- 所选策略的名称。应当是上面get_all_sharing_strategies()中系统支持的共享策略之一。<br>
三、介绍一下 共享CUDA张量？<br>
只支持在Python 3中使用spawn或forkserver启动方法才支持在进程之间共享CUDA张量。<br>
Python2中的multiprocessing只能使用fork创建子进程，并且不支持CUDA运行时。<br>
警告： CUDA API要求导出到其他进程的分配一直保持有效，只要它们被使用。您应该小心，确保您共享的<br>
CUDA张力不要超出范围，只要有必要。这不应该是共享模型参数的问题，但传递其他类型的数据应该小心。请<br>
注意，此限制不适用于共享CPU内存。<br>
四、介绍一下 共享策略？<br>
本节简要概述了不同的共享策略如何工作。请注意，它只适用于CPU张量 - CUDA张量将始终使用CUDA API，<br>
因为它们是唯一的共享方式。<br>
4.1 介绍一下文件描述符 -file_descripor？<br>
注意： 这是默认策略（除了不支持的MacOS和OS X）。<br>
此策略将使用文件描述符作为共享内存句柄。当存储器被移动到共享存储器时，每当存储器被移动到共享内存中<br>
时，从shm_open对象获取的文件描述符被缓存，并且当它被发送到其他进程时，文件描述符也将被传送（例如<br>
通过UNIX套接字）。接收器还将缓存文件描述符并且mmap它，以获得对存储数据的共享视图。<br>
请注意，如果要共享很多张量，则此策略将保留大量文件描述符需要很多时间才能打开。如果您的系统对打开的<br>
文件描述符数量有限制，并且无法提高，你应该使用file_system策略。<br>
4.2 文件系统 -file_system<br>
torch.multiprocessing.get_all_sharing_strategies()<br>
torch.multiprocessing.get_sharing_strategy()<br>
torch.multiprocessing.set_sharing_strategy(new_strategy)<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="48-图解分布式训练（四） —— torch.multiprocessing 详细解析_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>该策略将使用给定的文件名shm_open来标识共享内存区域。这具有不需要实现缓存从其获得的文件描述符的优<br>
点，但是同时容易发生共享内存泄漏。该文件创建后不能被删除，因为其他进程需要访问它以打开其视图。如果<br>
进程死机或死机，并且不调用存储析构函数，则文件将保留在系统中。这是非常严重的，因为它们在系统重新启<br>
动之前不断使用内存，或者手动释放它们。<br>
为了解决共享内存文件泄漏的问题，torch.multiprocessing将产生一个守护程序torch_shm_manager，它将自己<br>
与当前进程组隔离，并且将跟踪所有共享内存分配。一旦连接到它的所有进程退出，它将等待一会儿，以确保不<br>
会有新的连接，并且将遍历该组分配的所有共享内存文件。如果发现它们中的任何一个仍然存在，就释放掉它。<br>
我们已经测试了这种方法，并且证明对于各种故障是稳健的。不过，如果您的系统具有足够的限制，并且支持<br>
file_descriptor策略，我们不建议切换到该策略。<br>
五、torch.multiprocessing 函数使用<br>
下面展示一个采用多进程训练模型的例子：<br>
知识星球<br>
# Training a model using multiple processes:<br>
import torch.multiprocessing as mp<br>
def train(model):<br>
    for data, labels in data_loader:<br>
        optimizer.zero_grad()<br>
        loss_fn(model(data), labels).backward()<br>
        optimizer.step()  # This will update the shared parameters<br>
        <br>
model = nn.Sequential(<br>
    nn.Linear(n_in, n_h1),<br>
    nn.ReLU(),<br>
    nn.Linear(n_h1, n_out)<br>
)<br>
model.share_memory() # Required for 'fork' method to work<br>
processes = []<br>
for i in range(4): # No. of processes<br>
    p = mp.Process(target=train, args=(model,))<br>
    p.start()<br>
    processes.append(p)<br>
for p in processes: <br>
    p.join()<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:26</p>
        </div>
    </div>
</body>
</html>