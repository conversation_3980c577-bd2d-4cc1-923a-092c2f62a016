<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>56-LLMs 位置编码篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>56-LLMs 位置编码篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>LLMs 位置编码篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:44<br>
动机<br>
由于计算资源限制，目前的大模型大多在较小的上下文长度中进行训练，在推理中，若超出预训练<br>
的长度，模型的性能将会显著降低。于是涌现出了许多基于RoPE的长度外推的工作，旨在让大模<br>
型能够在预训练长度之外，取得更好的效果。<br>
一、什么是位置编码？<br>
句子中不同词语之前的位置信息十分重要，但是self-attention框架无法直接利用位置信息，因此研<br>
究者提出了许多方法将位置信息编码到学习过程中。<br>
• LLMs 位置编码篇<br>
• 动机<br>
• 一、什么是位置编码？<br>
• 二、为什么需要位置编码？<br>
• 三、什么是绝对位置编码？<br>
• 3.1 训练式位置编码篇<br>
• 3.1.1 什么是 训练式位置编码？<br>
• 3.1.2 如何为每个位置的词向量注入位置信息呢？<br>
• 3.1.3 训练式位置编码篇 应用场景？<br>
• 3.1.4 训练式位置编码篇 存在哪些问题？<br>
• 3.2 Sinusoidal位置编码篇<br>
• 3.2.1 什么是 Sinusoidal位置编码？<br>
• 3.2.2 Sinusoidal位置编码 有哪些优点？<br>
• 四、什么是相对位置编码？<br>
• 五、旋转位置编码 RoPE篇<br>
• 5.1 旋转位置编码 RoPE 思路是什么？<br>
• 5.2 推导一下 旋转位置编码 RoPE ？<br>
• 5.3 旋转位置编码 RoPE 有什么优点？<br>
• 5.4 旋转位置编码 RoPE 被哪些 LLMs 应用？<br>
• 六、长度外推问题篇<br>
• 6.1 什么是 长度外推问题？<br>
• 6.2 长度外推问题 的 解决方法 有哪些？<br>
• 七、 ALiBi (Attention with Linear Biases)篇<br>
• 7.1 ALiBi (Attention with Linear Biases) 思路是什么？<br>
• 7.2 ALiBi (Attention with Linear Biases) 的偏置矩阵是什么？有什么作用？<br>
• 7.3 ALiBi (Attention with Linear Biases) 有什么优点？<br>
• 7.4 ALiBi (Attention with Linear Biases) 被哪些 LLMs 应用？<br>
• 致谢<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page2_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page2_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<p>二、为什么需要位置编码？<br>
众所周知，transformer模型之所以能够取得如此卓越的效果，其中的Attention机制功不可没，它的<br>
本质是计算输入序列中的每个token与整个序列的注意力权重。假设 qm 和 kn 分别表示词向量 q 位<br>
于位置 m 和词向量 k 位于位置 n ，在未添加位置信息的时候，<br>
则两者的注意力权重计算如下:<br>
我们会发现，在未加入位置信息的情况下，无论 q 和 k 所处的位置如何变化，它们之间的注意力<br>
权重 a(m,n) 均不会发生变化，也就是位置无关，这显然与我们的直觉不符。对于两个词向量，如<br>
果它们之间的距离较近，我们希望它们之间的的注意力权重更大，当距离较远时，注意力权重更<br>
小。<br>
为了解决这个问题，我们需要为模型引入位置编码，让每个词向量都能够感知到它在输入序列中所<br>
处的位置信息。我们定义如下函数，该函数表示对词向量 q 注入位置信息 m ，得到 qm ：<br>
则 qm 与 kn 之间的注意力权重可表示为：<br>
三、什么是绝对位置编码？<br>
绝对位置编码比较简单，研究者一般会将绝对位置信息加到输入中：在输入的第 k 个向量xk​中加入<br>
位置向量pk​得到pk​+xk​，其中pk​仅与 k 相关。计算pk​的方法一般有两种：训练式位置编码与<br>
Sinusoidal位置编码。<br>
3.1 训练式位置编码篇<br>
3.1.1 什么是 训练式位置编码？<br>
训练式位置编码，顾名思义就是每个位置的位置向量会随着模型一起训练。假设模型最大输入长度<br>
为512，向量维度为768，我们可初始化一个512*768的位置编码矩阵，该矩阵将参与模型的训练，<br>
从而学习得到每个位置所对应的向量表示。<br>
3.1.2 如何为每个位置的词向量注入位置信息呢？<br>
1. 绝对位置编码方法：将位置信息直接加入到输入中；<br>
2. 相对位置编码方法：研究者通过微调attention的结构，使它具有识别token位置信息的能力。<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page3_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>答案是相加，如以下公式所示，其中 pm 表示第 m 个位置的位置向量：<br>
3.1.3 训练式位置编码篇 应用场景？<br>
训练式位置编码广泛应用于早期的transformer类型的模型，如BERT、GPT、ALBERT等。<br>
3.1.4 训练式位置编码篇 存在哪些问题？<br>
模型不具有长度外推性，因为位置编码矩阵的大小是预设的，若对其进行扩展，将会破坏模型在预<br>
训练阶段学习到的位置信息。例如将512768扩展为1024768，新拓展的512个位置向量缺乏训练，<br>
无法正确表示512~1023的位置信息。但早期大家对长文本输入的需求并不如现在迫切。<br>
3.2 Sinusoidal位置编码篇<br>
3.2.1 什么是 Sinusoidal位置编码？<br>
Sinusoidal位置编码是谷歌在Transformer模型中提出的一种绝对位置编码，它的形式如下，其中 d <br>
表示词向量的维度，k 表示位置索引， 2i 和 2i+1 表示位置向量的分量索引，例如pk,2i​和pk,2i+1​分<br>
别表示位置 k 的位置向量的第 2i 和第 2i+1 个分量：<br>
3.2.2 Sinusoidal位置编码 有哪些优点？<br>
Sinusoidal位置编码的每个分量都是正弦或余弦函数，所有每个分量的数值都具有周期性。如下图<br>
所示，每个分量都具有周期性，并且越靠后的分量，波长越长，频率越低。<br>
Sinusoidal位置编码还具有远程衰减的性质，具体表现为：对于两个相同的词向量，如果它们之间<br>
的距离越近，则他们的内积分数越高，反之则越低。如下图所示，我们随机初始化两个向量 q 和 <br>
1. 周期性<br>
1. 远程衰减性<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page4_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page4_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page4_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page4_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page4_img6.png" alt="图片 6" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 6</p>
</div>

<p>k，将 q 固定在位置0上，k 的位置从 0 开始逐步变大，依次计算 q 和 k 之间的内积。我们发现随<br>
着 q 和 k 的相对距离的增加，它们之间的内积分数震荡衰减。<br>
四、什么是相对位置编码？<br>
前面讲到相对位置编码是微调Attention矩阵的计算方式，先看看绝对位置编码怎样计算Attention矩<br>
阵：<br>
可以看到计算attention矩阵的过程如公式(1)所示，其中第一项和位置信息无关，第二至四项和位置<br>
信息相关。因此研究者通常是直接修改第二至四项的内容，直接在attention矩阵中添加相对位置信<br>
息。常见的有以下几种方法：<br>
XLNET式：如(2)所示，xlnet将(1)中的二至四项都做了改变，具体的将pn​替换为了Sinusoidal生成<br>
式编码Rn−m​，将pm​换成了两个可以训练的向量u,v。<br>
T5式：如(3)所示，它的作者认为输入和位置间不应过多的交互，因此将第二、三项删除，将第四<br>
项都替换为一个可学习的偏执bm,n​，这仅仅是在Attention矩阵的基础上加一个可训练的偏置项而<br>
已，十分简单。<br>
DeBerta式：和T5的构造相反，它舍弃了公式(1)中第四项，保留了第二、三项并将位置信息替换为<br>
了相对位置向量 <br>
。<br>
五、旋转位置编码 RoPE篇<br>
5.1 旋转位置编码 RoPE 思路是什么？<br>
</p>

<h2>第 5 页</h2>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page5_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page5_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page5_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page5_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page5_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page5_img6.png" alt="图片 6" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 6</p>
</div>

<p>作用在每个 transformer 层的 self-attention 块，在计算完 Q/K 之后，旋转位置编码作用在 Q/K <br>
上，再计算attention score。<br>
5.2 推导一下 旋转位置编码 RoPE ？<br>
Attention的核心运算是内积，所以我们希望经过内积的结果能够带有相对信息。那么我们希望qm​<br>
和kn​的内积仅与输入xm​,xn​和他们的相对位置 m-n 有关，那么我们可以假设存在函数 g ,使得：<br>
为了方便理解我们可以先考虑二维形式，然后借助复数的运算法则来理解。首先分别用复数的指数<br>
形式表示各个向量变化,即有：<br>
PS1. 向量内积与复数乘积的关系为内积<br>
，其中 Re 表示复数的实部。<br>
PS2. 这个形式证明过程可以参考论文的3.4.1节。但是要注意的是向量内积是标量，而<br>
 是向量，所以其公式(21) 应改为<br>
,这样公式(24)才好理解。<br>
5.3 旋转位置编码 RoPE 有什么优点？<br>
旋转位置编码通过绝对位置编码的方式实现了相对位置编码，有良好的外推性。<br>
5.4 旋转位置编码 RoPE 被哪些 LLMs 应用？<br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page6_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="56-LLMs 位置编码篇_page6_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>LLaMA、GLM-130B、PaLM等大语言模型就采用了旋转位置编码ROPE。<br>
六、长度外推问题篇<br>
6.1 什么是 长度外推问题？<br>
6.2 长度外推问题 的 解决方法 有哪些？<br>
七、 ALiBi (Attention with Linear Biases)篇<br>
7.1 ALiBi (Attention with Linear Biases) 思路是什么？<br>
在计算完attention score后，直接为attention score矩阵加上一个预设好的偏置矩阵<br>
7.2 ALiBi (Attention with Linear Biases) 的偏置矩阵是什么？有什么作用？<br>
7.3 ALiBi (Attention with Linear Biases) 有什么优点？<br>
ALiBi位置编码有良好的外推性<br>
7.4 ALiBi (Attention with Linear Biases) 被哪些 LLMs 应用？<br>
BLOOM就采用了这种位置编码<br>
知识星球<br>
• 长度外推问题：训练、推理的长度不一致问题，主要体现在以下两方面：<br>
• 问题一：位置编码不一致（推理的时候有训练没见过的位置编码）；<br>
• 问题二：attention span大小不一致（推理的时候attention span更大，导致墒增）；<br>
• 问题一解决方法：ALIBI、KERPLE、Sandwich、XPOS、PI、NTK-RoPE(目前看起来这个最<br>
强，不用finetune)；<br>
• 问题二解决方法：softmax的时候加一个log512​n系数；<br>
• ALiBi的偏置矩阵：根据q和k的相对距离来惩罚attention score<br>
• 作用：相对距离越大，惩罚项越大相当于两个token的距离越远，相互贡献就越小。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:27</p>
        </div>
    </div>
</body>
</html>