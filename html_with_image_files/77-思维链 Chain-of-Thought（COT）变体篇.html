<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>77-思维链 Chain-of-Thought（COT）变体篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>77-思维链 Chain-of-Thought（COT）变体篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>思维链 Chain-of-Thought（COT）变体篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:14<br>
思维链 Chain-of-Thought（COT）：思维链的启蒙<br>
论文名称：Chain-of-Thought Prompting Elicits Reasoning in Large Language Models<br>
论文地址：<br>
https://proceedings.neurips.cc//paper_files/paper/2022/hash/9d5609613524ecf4f15af0f7b31abc<br>
a4-Abstract-Conference.html<br>
1. 什么是 思维链 Chain-of-Thought（COT）？<br>
思维链（Chain-of-thought）提示使大型语言模型能够处理复杂的算术、常识和符号推理任务。强<br>
调了Chain-of-thought推理过程。<br>
• 思维链 Chain-of-Thought（COT）变体篇<br>
• 思维链 Chain-of-Thought（COT）：思维链的启蒙<br>
• 1. 什么是 思维链 Chain-of-Thought（COT）？<br>
• 2. 思维链 Chain-of-Thought（COT）是思路是什么？<br>
• 3. 思维链 Chain-of-Thought（COT）存在问题？<br>
• 思维树 Tree of Thoughts（TOT）：一种用树结构解决复杂问题的方法<br>
• 1. 为什么需要 思维树 Tree of Thoughts（TOT）？<br>
• 2. 什么是 思维树 Tree of Thoughts（TOT）？<br>
• 3. 思维树 Tree of Thoughts（TOT）涉及问题有哪些？<br>
• 思维树 Tree of Thoughts（TOT）解决复杂任务实例<br>
• example 1：LLMs to Game of 24<br>
• example 2：LLMs to Create Writing<br>
• example 3：LLMs to 5x5个迷你填字游戏<br>
• 思维图 Graph of Thoughts（GOT）：一种把思维链过程建模层图结构的方法<br>
• 1. 为什么 需要 思维图 Graph of Thoughts（GOT）？<br>
• 2. 什么是 思维图 Graph of Thoughts（GOT） ？<br>
• 3. 思维图 Graph of Thoughts（GOT）核心思想是什么 ？<br>
• 思维算法 Algorithm of Thoughts（AOT）：一种用DFS/BFS示例解决问题的方法<br>
• 1. 为什么 需要 思维算法 Algorithm of Thoughts（AOT）？<br>
• 2. 思维算法 Algorithm of Thoughts（AOT）思路是什么？<br>
• 3. 思维算法 Algorithm of Thoughts（AOT） vs 其他 COT 的 区别？<br>
• 思维链 Chain-of-Thought（COT） 有哪些 应用场景？<br>
• Task 1: 复杂任务求解<br>
• Task 2: 增强特定任务可靠性<br>
• 思维链 Chain-of-Thought（COT） 有哪些 局限性？<br>
• 致谢<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>如图：该模型产生了一个思想链来解决一个数学问题，否则它会变得不正确。<br>
2. 思维链 Chain-of-Thought（COT）是思路是什么？<br>
思维链 Chain-of-Thought（COT）类似于一个解决方案，它模仿了一个逐步思考的过程来得出答<br>
案（或者solutions/explanations通常在final answer之后）。<br>
3. 思维链 Chain-of-Thought（COT）存在问题？<br>
思维链 Chain-of-Thought（COT） 需要 LLM的参数量级必须大<br>
eg: 论文使用效果最好的是PaLM 540B的模型，看来模型越大对思维链的能力就越强<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>思维树 Tree of Thoughts（TOT）：一种用树结构解决复杂问题<br>
的方法<br>
论文名称：Tree of Thoughts: Deliberate Problem Solving with Large Language Models<br>
论文地址：https://arxiv.org/abs/2305.10601<br>
论文 Github：https://github.com/princeton-nlp/tree-of-thought-llm<br>
1. 为什么需要 思维树 Tree of Thoughts（TOT）？<br>
CoT通常只有一条解决问题的路径，但是 有一些复杂问题的答案 并不止 有一条解决问题的路径，<br>
而是由 多条 解决问题的路径 组成。<br>
2. 什么是 思维树 Tree of Thoughts（TOT）？<br>
思维树 Tree of Thoughts（TOT）把一条reasoning路径拓展至多条reasong paths，这样模型可<br>
以综合多条reasoning path的结果得到最终的结论。<br>
注：LLM解决问题的各种方法。每个矩形框代表一个thought，它是一个连贯的语言序列，是解决<br>
问题的中间步骤。ToT将任何问题定义为在树上的搜索，其中每个节点都是一个状态s=[x；z1i]，<br>
表示到目前为止具有输入和thought序列的部分解决方案<br>
3. 思维树 Tree of Thoughts（TOT）涉及问题有哪些？<br>
1. Thought Decomposition<br>
a. 问题描述：如何将中间过程分解为思维步骤；<br>
2. Thought Generator<br>
a. 问题描述：如何从每种状态中产生潜在的thought；<br>
b. 任务定义：给定一个树的状态，从k个候选项中决定下一个step<br>
c. 实现方法：采用采样和投票的方法<br>
3. State Evaluator<br>
a. 问题描述：如何启发式地评估状态；<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page4_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>思维树 Tree of Thoughts（TOT）解决复杂任务实例<br>
example 1：LLMs to Game of 24<br>
example 2：LLMs to Create Writing<br>
example 3：LLMs to 5x5个迷你填字游戏<br>
b. 任务定义：给定不同状态的边界，状态评估器评估他们在解决问题方面取得的进展，并<br>
作为搜索算法的启发式算法，以确定要继续探索哪些状态以及以何种顺序进行探索<br>
c. 实现方法：<br>
i. value类型的分类或者打分<br>
ii. 投票的形式<br>
4. Search algorithms<br>
a. 问题描述：使用什么搜索算法。<br>
b. 实现方法：可拔插式的使用不同的搜索算法<br>
• 任务介绍：Game of 24 游戏是数学推理游戏，目标是通过4个数，然后加减乘除得到24<br>
• LLMs to Game of 24 思路：输入是“4 9 10 13”，最终得到的答案是“(10 - 4) * (13 - 9) = 24”，产<br>
生了3个intermediate thoughts可以是：“13 - 9 = 4 (left: 4 4 10); 10 - 4 = 6 (left: 4 6); 4 * 6 = 24 <br>
(left: 24)”，ToT会产生很多的这样的itermediate steps，然后我们采用BFS来执行这些steps。<br>
• 任务介绍：给定四个句子，然后产生4个paragraph，分别以这4个句子结尾<br>
• LLMs to Create Writing 思路：<br>
• 首先产生k=5个候选的plan，投票选择最佳的passage，基于最佳的plan，产生5个<br>
passage；<br>
• 然后选择其中产生的最佳passage，投票的过程是一个zero-shot的过程<br>
</p>

<h2>第 5 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page5_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>思维图 Graph of Thoughts（GOT）：一种把思维链过程建模层<br>
图结构的方法<br>
论文名称：Tree of Thoughts: Deliberate Problem Solving with Large Language Models<br>
论文地址：https://arxiv.org/abs/2308.09687<br>
论文 Github：https://github.com/spcl/graph-of-thoughts<br>
1. 为什么 需要 思维图 Graph of Thoughts（GOT）？<br>
但是 有时候 解决问题 的 父级 节点 不只有一个，而是 类似 拓扑图 的 形式。<br>
2. 什么是 思维图 Graph of Thoughts（GOT） ？<br>
思维图 Graph of Thoughts（GOT）通过 构建了一个有向图来解决问题。<br>
3. 思维图 Graph of Thoughts（GOT）核心思想是什么 ？<br>
GoT系统结构包含一系列的交互模块：<br>
• 任务介绍：<br>
• LLMs to 5x5个迷你填字游戏 思路：探索LM作为一个探索自己思想的一般问题解决者的极限并<br>
以深思熟虑的推理作为启发法来指导自己的探索。<br>
• CoT通常只有一条解决问题的路径；<br>
• TOT 解决问题的路径类似一颗树；<br>
• Prompter：为LLM准备消息；<br>
• Parser：从LLM的回复中提取信息；<br>
• Scoring module：验证LLM回复并对其进行评分；<br>
• Controller：<br>
• 介绍：协调整个推理过程，并决定如何进行推理<br>
• 重要元素：<br>
• Graph of Operations（GoO）：一种静态结构，它指定了给定任务的图分解，<br>
它规定了应用于LLM思想的转换，以及它们的顺序和依赖关系<br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page6_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page6_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>注：图中蓝色部分包含架构概述，绿色部分列出API<br>
注：红色部分包含示例提示以及GRS和相关操作，具体是归并排序（先把list array，分解成sub <br>
array，sort后进行合并）任务的prompt的示例<br>
思维算法 Algorithm of Thoughts（AOT）：一种用DFS/BFS示<br>
例解决问题的方法<br>
论文名称：Algorithm of Thoughts: Enhancing Exploration of Ideas in Large Language Models<br>
论文地址：https://arxiv.org/abs/2308.10379<br>
论文 Github：https://github.com/kyegomez/Algorithm-Of-Thoughts<br>
• Graph Reasoning State（GRS）：一个动态结构，它保持正在进行的LLM推理<br>
过程的状态（其思想及其状态的历史）<br>
</p>

<h2>第 7 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page7_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page7_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>1. 为什么 需要 思维算法 Algorithm of Thoughts（AOT）？<br>
2. 思维算法 Algorithm of Thoughts（AOT）思路是什么？<br>
AoT的上下文示例与CoT不同，它集成了搜索过程，由标记“1”、…、“突出显示3’作为指导问题集子<br>
树探索的“第一次运算”8 6 4 4’。为了清楚起见，只显示了一个上下文中的示例，重点放在第三子树<br>
探索上。AoT产生预期的搜索步骤（例如子树探索’5。11+1'），并评估任何进展的潜在后续步骤找<br>
到一个解决方案或返回到另一个可行的子树。<br>
3. 思维算法 Algorithm of Thoughts（AOT） vs 其他 COT 的 区别？<br>
• Standard Prompting 旨在直接回答<br>
• CoT 给出了最终解决方案的连续步骤<br>
• AOT vs COT:<br>
• COT：为链式结构，也就是 问题答案 只能由 一条固定路径得到；<br>
• AOT：为有向图结构中寻找最优路径的过程；<br>
• AOT vs TOT:<br>
• TOT：对节点进行剪枝，然后推理过程是一个树的结构；<br>
• AOT：利用示例的方式模仿DFS或者BFS，能够激发LLM的能力（论文说在GPT4上有很<br>
好的效果，估计对LLM本身的能力要求比较高），得到更好的结果；<br>
</p>

<h2>第 8 页</h2>

<div class="image-container">
  <img src="77-思维链 Chain-of-Thought（COT）变体篇_page8_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>思维链 Chain-of-Thought（COT） 有哪些 应用场景？<br>
Task 1: 复杂任务求解<br>
从论文给的一些数学和算法的求解示例可以看出，这些复杂的思维链的prompts工程是为了增强模<br>
型在复杂推理任务的准确性，所以显而易见，后续的一个很大的应用方向是解决复杂的数学问题，<br>
组合优化问题等等，至于能不能求解NP-hard问题，找到一些启发式的算法，还有待探索。<br>
Task 2: 增强特定任务可靠性<br>
除了解决LLM在复杂任务上的性能问题外，还有一个方向就是提升已有任务的准确性，比如现在检<br>
索+生成的问答方式只能达到80%的准确率了，通过应用思维链的方式+限制条件，一下子能够提<br>
升准确率到90%。这对于一些追求LLM稳定性结果输出的场景具有重要的作用。<br>
思维链 Chain-of-Thought（COT） 有哪些 局限性？<br>
CoT，AoT，ToT都需要你自己写instructions和in-context examples，这些都属于prompt工程的范<br>
畴，但是人工的寻找某些任务的最佳prompts就比较费脑子，因此自动化prompts工程会是未来一<br>
个潜在的方向，来弥补现在对复杂任务需要这种细粒度的prompt 工程的弊端。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:26</p>
        </div>
    </div>
</body>
</html>