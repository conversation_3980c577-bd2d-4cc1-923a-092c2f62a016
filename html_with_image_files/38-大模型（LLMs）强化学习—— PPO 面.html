<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>38-大模型（LLMs）强化学习—— PPO 面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>38-大模型（LLMs）强化学习—— PPO 面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="38-大模型（LLMs）强化学习—— PPO 面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="38-大模型（LLMs）强化学习—— PPO 面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）强化学习—— PPO 面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 20:47<br>
一、大语言模型RLHF中的PPO主要分哪些步骤？<br>
大语言模型RLHF中的PPO 分为：<br>
对应的实现逻辑如下：<br>
二、举例描述一下 大语言模型的RLHF？<br>
大语言模型的RLHF，实际上是模型先试错再学习的过程。<br>
大语言模型的RLHF 好比是：老师与学生的角色<br>
• 大模型（LLMs）强化学习—— PPO 面<br>
• 一、大语言模型RLHF中的PPO主要分哪些步骤？<br>
• 二、举例描述一下 大语言模型的RLHF？<br>
• 三、大语言模型RLHF 采样篇<br>
• 3.1 什么是 PPO 中 采样过程？<br>
• 3.2 介绍一下 PPO 中 采样策略？<br>
• 3.3 PPO 中 采样策略中，如何评估“收益”？<br>
• 参考<br>
1. 采样<br>
2. 反馈<br>
3. 学习<br>
policy_model = load_model()<br>
for k in range(20000):<br>
    # 采样（生成答案）<br>
    prompts = sample_prompt()<br>
    data = respond(policy_model, prompts)<br>
    <br>
    # 反馈（计算奖励）<br>
    rewards = reward_func(reward_model, data)<br>
    <br>
    # 学习（更新参数）<br>
    for epoch in range(4):<br>
        policy_model = train(policy_model, prompts, data, rewards)<br>
• 我们扮演着老师的角色，给出有趣的问题。模型则会像小学生一样，不断尝试给出答案。<br>
• 模型会根据我们给出的问题，写出它觉得正确的答案，但是这些答案不一定是真的答案，需要我们结合正确<br>
答案进行打分。如果它表现得好，就会给予它高声赞扬；如果它表现不佳，我们则会给予它耐心的指导和反<br>
馈，帮助它不断改进，直到达到令人满意的水平。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="38-大模型（LLMs）强化学习—— PPO 面_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="38-大模型（LLMs）强化学习—— PPO 面_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>三、大语言模型RLHF 采样篇<br>
3.1 什么是 PPO 中 采样过程？<br>
PPO 中 采样过程：学生回答问题的过程，是模型根据提示（prompt）输出回答（response）的过程，或者说是<br>
模型自行生产训练数据的过程。<br>
eg:<br>
3.2 介绍一下 PPO 中 采样策略？<br>
PPO 中 采样工作 通过一种策略（policy）：policy由两个模型组成，一个叫做演员模型（Actor），另一个叫<br>
做评论家模型（Critic）。它们就像是学生大脑中的两种意识，一个负责决策，一个负责总结得失。<br>
演员：我们想要训练出来的大模型。在用PPO训练它之前，它就是RLHF的第一步训练出来的<br>
SFT（Supervised Fine-Tuning）model。输入一段上下文，它将输出下一个token的概率分布。<br>
评论家：强化学习的辅助模型，输入一段上下文，它将输出下一个token的“收益”。<br>
3.3 PPO 中 采样策略中，如何评估“收益”？<br>
从下一个token开始，模型能够获得的总奖励（浮点数标量）。这里说的奖励包括Reward Model给出的奖励。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:26</p>
        </div>
    </div>
</body>
</html>