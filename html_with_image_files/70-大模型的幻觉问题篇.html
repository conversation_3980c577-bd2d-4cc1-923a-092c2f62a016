<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>70-大模型的幻觉问题篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>70-大模型的幻觉问题篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="70-大模型的幻觉问题篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="70-大模型的幻觉问题篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型的幻觉问题篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 13:34<br>
一、什么是 大模型幻觉问题？<br>
1.1 大模型幻觉问题定义<br>
1.2 何为 Faithfulness and Factualness？<br>
1.3 针对不同任务，幻觉的定义有何差异？<br>
eg：摘要的数据源是document，data-to-text的数据源是data table，对话的数据源是对话历史，而开放域对话的<br>
数据源可以是世界知识；<br>
在摘要、data-to-text任务中，非常看重response的Faithfulness，因此这些任务对幻觉的容忍程度很低；<br>
而像开发域对话任务中，只需要response符合事实即可，容忍程度较高；<br>
1.4 传统任务中的模型幻觉 vs LLMs 中模型幻觉<br>
二、为什么 会 出现 大模型幻觉问题？<br>
2.1 从 数据角度 进行分析<br>
在 数据构建过程中，由于以下问题，导致 模型幻觉 的 发生：<br>
引用至 [3] Deduplicating training data makes language models better<br>
2.2 从 模型角度 进行分析<br>
不止是 数据角度问题，大模型幻觉问题 出现的原因 还 表现在 模型角度。<br>
• 定义：当模型生成的文本不遵循原文（Faithfulness）或者不符合事实（Factualness），我们就可以认为<br>
模型出现了幻觉的问题。<br>
• Faithfulness：是否遵循input content；<br>
• Factualness：是否符合世界知识；<br>
1. 数据源（source）不一致问题<br>
1. 容忍幻觉的程度不一致问题<br>
• 在传统任务里，幻觉大都是指的是Faithfulness：<br>
• Intrinsic Hallucination（信息冲突）: LMs在生成回复时，与输入信息产生了冲突，例如摘要问题<br>
里，abstract和document的信息不一致；<br>
• Extrinsic Hallucination（无中生有）: LMs在生成回复时，输出一些并没有体现在输入中的额外信<br>
息，比如邮箱地址、电话号码、住址，并且难以验证其真假。（PS: 按照此定义，Extrinsic <br>
Hallucination有可能是真的信息，只是需要外部信息源进行认证）<br>
• 而面向LLMs，我们通常考虑的幻觉则是Factualness：<br>
• 因为我们应用LLM的形式是open-domain Chat，而不是局限于特定任务，所以数据源可以看做任意<br>
的世界知识。LLMs如果生成了不在input source里的额外信息，但是符合事实的，这种情况也可能是<br>
对我们有帮助的。<br>
1. 训练数据可信度问题。由于 大模型 的 训练数据 都是 通过 众包/爬虫检索 方式 收集得到的，这种数据构建方<br>
式的优点是量比较大，但是缺点是 包含 大量虚假信息。这种虚假信息 直接导致的问题就是使 模型出现错误<br>
认知；<br>
2. 重复数据问题。过多的重复信息也可能导致模型的知识记忆出现bias，从而导致幻觉；<br>
• 模型结构：如果是较弱的backbone（比如RNN）可能导致比较严重的幻觉问题，但在LLMs时代应该不太可<br>
能存在这一问题；<br>
• 解码算法：研究表明，如果使用不确定性较高的采样算法（e.g.，top-p）会诱导LMs出现更严重的幻觉问<br>
题。甚至可以故意在解码算法中加入一些随机性，进一步让LMs胡编乱造（可以用该方法生成一些negative <br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>引用至 [4] Factuality enhanced language models for open-ended text generation<br>
引用至 [5] On exposure bias, hallucination and domain shift in neural machine translation<br>
引用至 [6] Entity-based knowledge conflicts in question answering<br>
三、如何 评估 大模型幻觉问题？<br>
现有的传统幻觉评估指标和人类结果的相关性往往较低，同时大都是task-specific的 [7]。<br>
3.1 Reference-based<br>
Reference-based的指标有两类：<br>
基于Reference的评价指标，基本上只能评价Faithfulness，而无法评价Factualness，因此通常不适用于<br>
LLMs。<br>
3.2 Reference-Free<br>
3.2.1 基于IE<br>
3.2.2 基于QA<br>
引用至 [8] FEQA: A question answering evaluation framework for faithfulness assessment in abstractive <br>
summarization<br>
3.2.3 基于NLI<br>
引用至 [9] Evaluating groundedness in dialogue systems: The BEGIN benchmark.<br>
引用至 [10] Evaluating factuality in generation with dependency-level entailment.<br>
3.2.4 基于Factualness Classification Metric<br>
samples）<br>
• 暴露偏差：训练和测试阶段不匹配的exposure bias问题可能导致LLMs出现幻觉，特别是生成long-form <br>
response的时候。<br>
• 参数知识：LMs在预训练阶段记忆的错误的知识，将会严重导致幻觉问题。<br>
1. 基于Source Information和Target Reference：利用一些统计学指标，比如ROUGE、BLEU来评估输出结<br>
果和Source/Target信息的重叠度;<br>
2. 基于Source Information：由于NLG任务里，Target输出往往是多种多样的，因此许多工作只基于Source信<br>
息进行幻觉的评估。比如Knowledge F1。<br>
• 介绍：将知识限定于可以用三元组形式表示的关系和事件，基于额外的IE模型进行抽取，接着使用额外模型<br>
进行验证；<br>
• 缺点：<br>
• 可能存在IE模型的错误传播问题；<br>
• 知识被限定在三元组形式。<br>
• 介绍：<br>
1. 第一步先基于LM生成的回复，使用一个QG(question generation)模型生成一系列QA pairs；<br>
2. 第二步给定Source Information，让QA模型对上一步生成的Question进行回复；<br>
3. 第三步则是通过对比第一步的answers和第二步的answers，计算匹配指标，衡量模型的幻觉问题；<br>
• 缺点：<br>
• 可能存在IE模型的错误传播问题；<br>
• 难以评估Factualness，因为上述第二步里面，Source Information不可能包含全部的世界知识，因此<br>
对于一些问题难以生成可靠的回复。<br>
• 介绍：基于NLI的方法通过NLI模型评估是否Source Information可以蕴含Generated Text，从而评估是否出<br>
现了幻觉现象。<br>
• 缺点：<br>
1. Off-the-shelf NLI模型用于核查事实效果不是很好<br>
1. 无法评估需要世界知识的幻觉问题：仅能依赖于Source进行核查；<br>
2. 都是sentence-level的，无法支撑更细粒度的幻觉检查；<br>
1. 幻觉问题和蕴含问题实际并不等价：<br>
a. 例子：Putin is president. -&gt; Putin is U.S. president (可以蕴含，但是是幻觉)<br>
</p>

<h2>第 3 页</h2>

<p>引用至 [11] Knowledge-powered conversational agents<br>
3.2.5 人工评估<br>
四、如何 缓解 大模型幻觉问题？<br>
4.1 基于数据的工作<br>
4.1.1 构建高质量数据集<br>
引用至 [12] GO FIGURE: A meta evaluation of factuality in summarization<br>
引用至 [13] Q2: Evaluating factual consistency in knowledge-grounded dialogues via question generation and <br>
question answering<br>
引用至 [14] Vectara：让你的LLM应用告别幻觉！<br>
4.2 模型层面的工作<br>
4.2.1 模型结构<br>
引用至 [15] Factuality enhanced language models for open-ended text generation<br>
引用至 [16] Check your facts and try again: Improving large language models with external knowledge and <br>
automated feedback.<br>
4.2.2 训练方式<br>
引用至 [17] Increasing faithfulness in knowledgegrounded dialogue with controllable features<br>
引用至 [18] A controllable model of grounded response generation<br>
引用至 [19] Data-to-text generation with content selection and planning<br>
引用至 [20] Slot-consistent NLG for task-oriented dialogue systems with iterative rectification network<br>
引用至 [21] improving factual consistency between a response and persona facts<br>
引用至 [22] Improving faithfulness in abstractive summarization with contrast candidate generation and <br>
selection<br>
4.3 可能的后续方向<br>
• 介绍：标注/构造一批和幻觉/事实有关的数据，训练检测模型，利用该模型评估新生成文本的幻觉/事实问<br>
题。<br>
• 介绍：目前为止最靠谱的，此外还可以依靠LLM打分（比如利用GPT4，但是GPT4也存在着严重的幻觉问<br>
题，即使经过retrival-augment，检索回来的信息也有可能是错误的）<br>
1. 人工标注<br>
• 训练数据：LLM上不可行，只适用于task-specific的幻觉问题；<br>
• 评测数据：构建细粒度的幻觉评估benchmark用于分析幻觉的严重程度和原因<br>
1. 自动筛选：<br>
• 利用模型筛选出可能导致幻觉的数据并剔除；<br>
• 预训练时给更faithful的数据加权（wiki vs. fake news），或者不使用可靠来源的数据（比如只选用经过人工<br>
审查的数据源，如wiki或者教科书，预训练）<br>
• 模型结构层面的工作往往focus在设计更能充分编码利用source information的方法，比如融入一些人类偏<br>
置，如GNN网络。<br>
• 在解码时减少模型的生成随机性，因为diversity和Faithfulness往往是一个trade-off的关系，减少<br>
diversity/randomness可以变相提升Faithfulness/Factuality。<br>
• 检索增强被证明可以显著减少幻觉问题，e.g., LLaMA-index。<br>
• 可控文本生成：将幻觉的程度作为一个可控的属性，利用可控文本生成技术进行控制。<br>
• 提前规划骨架，再生成：sketch to content<br>
• 强化学习：假设是基于word的MLE训练目标，只优化唯一的reference，可能导致暴露偏差问题。现有工作将<br>
减轻幻觉的指标作为强化学习的reward函数，从而减轻幻觉现象。<br>
• 多任务学习: 通过设计合适的额外任务，可以达到减轻幻觉的效果。<br>
• 后处理：设计一个小模型专门用于fix幻觉错误。<br>
1. 更细粒度的幻觉评估：<br>
a. token/phrase level instead of sentence level<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="70-大模型的幻觉问题篇_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
b. 更精细的幻觉分类体系：<br>
i. Intrinsic<br>
ii. Extrinsic<br>
iii. 其他类别：<br>
1. 按幻觉产生的原因分类（调用知识出错，还是缺少相应知识）<br>
2. 主观/客观幻觉<br>
3. 幻觉可能和时间（temporal）有关<br>
2. 知识的定义和诱导：<br>
a. 怎么知道模型是否具备某一类知识，只是没有调用好？<br>
b. 知识的定义：<br>
i. 传统工作大都将wikipedia视作知识库，但它仅仅是世界知识的很小一部分<br>
ii. 如果将整个互联网当做世界知识，又不可避免的会有虚假信息的问题<br>
3. 幻觉消除：<br>
a. 检索增强：互联网/外挂知识库(LLaMA Index)<br>
b. 强化学习（RLHF）<br>
c. 知识诱导/注入<br>
d. 直接修改LLM中错误记忆的知识：Model Editing工作，如ROME，MEMIT等<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:27</p>
        </div>
    </div>
</body>
</html>