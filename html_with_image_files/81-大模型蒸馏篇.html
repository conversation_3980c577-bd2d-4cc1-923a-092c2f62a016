<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>81-大模型蒸馏篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>81-大模型蒸馏篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="81-大模型蒸馏篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="81-大模型蒸馏篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型蒸馏篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:14<br>
一、知识蒸馏和无监督样本训练？<br>
知识蒸馏是利用大模型把一个大模型的知识压缩到一个小模型上。具体来说你在一个训练集上得到了一个非常好<br>
的较大的模型，<br>
然后你把这个模型冻结，作为Teacher模型也叫监督模型，然后你再造一个较小参数的模型叫做Student模型，我<br>
们的目标就是利用冻结的Teacher模型去训练Student模型。<br>
A.离线蒸馏：Student在训练集上的loss和与Teacher模型的loss作为总的loss，一起优化。<br>
B.半监督蒸馏：向Teacher模型输入一些input得到标签，然后把input和标签传给Student模型<br>
还有个自监督蒸馏，直接不要Teacher模型，在最后几轮epoch，把前面训练好的模型作为Teacher进行监督。<br>
目前知识蒸馏的一个常见应用就是对齐ChatGPT。<br>
然后这个无监督样本训练，我看不懂意思。如果是传统的无监督学习，那就是聚类，主成分分析等操作。如果是<br>
指知识蒸馏的话，就是离线蒸馏的方式，只不过损失只有和Teacher的loss。<br>
二、对知识蒸馏知道多少，有哪些改进用到了？<br>
知识蒸馏是一种通过将一个复杂模型的知识转移到一个简单模型来提高简单模型性能的方法。这种方法已经被广<br>
泛应用于各种深度学习任务中。其中一些改进包括：<br>
三、谈一下对模型量化的了解？<br>
模型量化是一种将浮点型参数转换为定点型参数的技术，以减少模型的存储和计算复杂度。常见的模型量化方法<br>
包括：<br>
模型量化可以减少模型的存储空间和内存占用，同时也可以加速模型的推理速度。但是，模型量化可能会对模型<br>
的精度造成一定的影响，因此需要仔细权衡精度和计算效率之间的平衡。<br>
四、模型压缩和加速的方法有哪些？<br>
• 大模型蒸馏篇<br>
• 一、知识蒸馏和无监督样本训练？<br>
• 二、对知识蒸馏知道多少，有哪些改进用到了？<br>
• 三、谈一下对模型量化的了解？<br>
• 四、模型压缩和加速的方法有哪些？<br>
• 五、你了解的知识蒸馏模型有哪些？<br>
• 使用不同类型的损失函数和温度参数来获得更好的知识蒸馏效果。<br>
• 引入额外的信息来提高蒸馏的效果，例如将相似性约束添加到模型训练中。<br>
• 将蒸馏方法与其他技术结合使用，例如使用多任务学习和迁移学习来进一步改进知识蒸馏的效果。<br>
• 量化权重和激活值，将它们转换为整数或小数。<br>
• 使用更小的数据类型，例如8位整数、16位浮点数等。<br>
• 使用压缩算法，例如Huffman编码、可逆压缩算法等。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="81-大模型蒸馏篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>参数剪枝（Parameter Pruning）：删除模型中冗余的参数，减少模型的大小。通常情况下，只有很少一部分参<br>
数对模型的性能贡献较大，其余参数对性能的贡献较小或没有贡献，因此可以删除这些冗余参数。<br>
量化（Quantization）：将浮点型参数转换为更小的整数或定点数，从而减小模型大小和内存占用，提高计算效<br>
率。<br>
知识蒸馏（Knowledge Distillation）：利用一个较大、较准确的模型的预测结果来指导一个较小、较简单的模型<br>
学习。这种方法可以减小模型的复杂度，提高模型的泛化能力和推理速度。<br>
网络剪枝（Network Pruning）：删除模型中冗余的神经元，从而减小模型的大小。与参数剪枝不同，网络剪枝<br>
可以删除神经元而不会删除对应的参数。<br>
蒸馏对抗网络（Distillation Adversarial Networks）：在知识蒸馏的基础上，通过对抗训练来提高模型的鲁棒性和<br>
抗干扰能力。<br>
模型量化（Model Quantization）：将模型的权重和激活函数的精度从32位浮点数减少到更小的位数，从而减小<br>
模型的大小和计算开销。<br>
层次化剪枝（Layer-wise Pruning）：对模型的不同层进行不同程度的剪枝，以实现更高效的模型压缩和加速。<br>
低秩分解（Low-Rank Decomposition）：通过将一个较大的权重矩阵分解为几个较小的权重矩阵，从而减少计<br>
算开销。<br>
卷积分解（Convolution Decomposition）：将卷积层分解成几个更小的卷积层或全连接层，以减小计算开销。<br>
网络剪裁（Network Trimming）：通过对模型中一些不重要的连接进行剪裁，从而减小计算开销。<br>
五、你了解的知识蒸馏模型有哪些？<br>
FitNets：使用一个大型模型作为教师模型来指导一个小型模型的训练。<br>
Hinton蒸馏：使用一个大型模型的输出作为标签来指导一个小型模型的训练。<br>
Born-Again Network（BAN）：使用一个已经训练好的模型来初始化一个新模型，然后使用少量的数据重新训练<br>
模型。<br>
TinyBERT：使用一个大型BERT模型作为教师模型来指导一个小型BERT模型的训练。<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:23</p>
        </div>
    </div>
</body>
</html>