<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>13-基于langchain RAG问答应用实战</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>13-基于langchain RAG问答应用实战</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="13-基于langchain RAG问答应用实战_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="13-基于langchain RAG问答应用实战_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>基于langchain RAG问答应用实战<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年02月08日 10:04<br>
一、前言<br>
1.1 介绍<br>
本次选用百度百科——藜麦数据（https://baike.baidu.com/item/藜麦/5843874）模拟个人或企业私域数<br>
据，并基于langchain开发框架，实现一种简单的RAG问答应用示例。<br>
1.2 软件资源<br>
二、环境搭建<br>
2.1 下载代码<br>
2.2 构建环境<br>
2.3 安装依赖<br>
三、RAG问答应用实战<br>
3.1 数据构建<br>
藜麦数据（https://baike.baidu.com/item/藜麦/5843874）保存到 藜.txt 文件中。<br>
3.2 本地数据加载<br>
• CUDA 11.7<br>
• Python 3.10<br>
• pytorch 1.13.1+cu117<br>
• langchain<br>
    $ <br>
    $ conda create -n py310_chat python=3.10       # 创建新环境<br>
    $ source activate py310_chat                   # 激活环境<br>
    $ pip install datasets langchain sentence_transformers tqdm chromadb <br>
langchain_wenxin<br>
from langchain.document_loaders import TextLoader<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>3.3 文档分割<br>
文档分割，借助langchain的字符分割器，这里采用固定字符长度分割chunk_size=128<br>
loader = TextLoader("./藜.txt")<br>
documents = loader.load()<br>
documents<br>
&gt;&gt;&gt;<br>
[Document(page_content='藜（读音lí）麦（Chenopodium\xa0quinoa\xa0Willd.）是藜科藜属<br>
植物。穗部可呈红、紫、黄，植株形状类似灰灰菜，成熟后穗部类似高粱穗。植株大小受环境<br>
及遗传因素影响较大，从0.3-3米不等，茎部质地较硬，可分枝可不分。单叶互生，叶片呈鸭掌<br>
状，叶缘分为全缘型与锯齿缘型。藜麦花两性，花序呈伞状、穗状、圆锥状，藜麦种子较小，<br>
呈小圆药片状，直径1.5-2毫米，千粒重1.4-3克。\xa0[1]\xa0\n原产于南美洲安第斯山脉的哥<br>
伦比亚、厄瓜多尔、秘鲁等中高海拔山区。具有一定的耐旱、耐寒、耐盐性，生长范围约为海<br>
平面到海拔4500米左右的高原上，最适的高度为海拔3000-4000米的高原或山地地区。<br>
\xa0[1]\xa0\n藜麦富含的维生素、多酚、类黄酮类、皂苷和植物甾醇类物质具有多种健康功<br>
效。...<br>
# 文档分割<br>
from langchain.text_splitter import CharacterTextSplitter<br>
# 创建拆分器<br>
text_splitter = CharacterTextSplitter(chunk_size=128, chunk_overlap=0)<br>
# 拆分文档<br>
documents = text_splitter.split_documents(documents)<br>
documents<br>
[Document(page_content='藜（读音lí）麦（Chenopodium\xa0quinoa\xa0Willd.）是藜科藜属<br>
植物。穗部可呈红、紫、黄，植株形状类似灰灰菜，成熟后穗部类似高粱穗。植株大小受环境<br>
及遗传因素影响较大，从0.3-3米不等，茎部质地较硬，可分枝可不分。单叶互生，叶片呈鸭掌<br>
状，叶缘分为全缘型与锯齿缘型。藜麦花两性，花序呈伞状、穗状、圆锥状，藜麦种子较小，<br>
呈小圆药片状，直径1.5-2毫米，千粒重1.4-3克。\xa0[1]\xa0\n原产于南美洲安第斯山脉的哥<br>
伦比亚、厄瓜多尔、秘鲁等中高海拔山区。具有一定的耐旱、耐寒、耐盐性，生长范围约为海<br>
平面到海拔4500米左右的高原上，最适的高度为海拔3000-4000米的高原或山地地区。<br>
\xa0[1]\xa0\n藜麦富含的维生素、多酚、类黄酮类、皂苷和植物甾醇类物质具有多种健康功<br>
效。藜麦具有高蛋白，其所含脂肪中不饱和脂肪酸占83%，还是一种低果糖低葡萄糖的食物，能<br>
在糖脂代谢过程中发挥有益功效。\xa0[1]\xa0\xa0[5]\xa0\n国内藜麦产品的销售以电商为主,<br>
缺乏实体店销售,藜麦市场有待进一步完善。藜麦国际市场需求强劲,发展前景十分广阔。通过<br>
加快品种培育和生产加工设备研发,丰富产品种类,藜麦必将在“调结构,转方式,保增收”的农<br>
业政策落实中发挥重要作用。\xa0[5]\xa0\n2022年5月，“超级谷物”藜麦在宁洱县试种成<br>
功。', metadata={'source': './藜.txt'}),<br>
 Document(page_content='藜麦是印第安人的传统主食，几乎和水稻同时被驯服有着6000多年<br>
的种植和食用历史。藜麦具有相当全面营养成分，并且藜麦的口感口味都容易被人接受。在藜<br>
麦这种营养丰富的粮食滋养下南美洲的印第安人创造了伟大的印加文明，印加人将藜麦尊为粮<br>
食之母。美国人早在80年代就将藜麦引入NASA，作为宇航员的日常口粮，FAO认定藜麦是唯一一<br>
种单作物即可满足人类所需的全部营养的粮食，并进行藜麦的推广和宣传。2013年是联合国钦<br>
定的国际藜麦年。以此呼吁人们注意粮食安全和营养均衡。', metadata={'source': './<br>
藜.txt'}),<br>
</p>

<h2>第 3 页</h2>

<p>3.4 向量化&amp;数据入库<br>
接下来对分割后的数据进行embedding，并写入数据库。这里选用<br>
m3e-base作为embedding模型，向量数据库选用Chroma<br>
3.5 Prompt设计<br>
prompt设计，这里只是一个prompt的简单示意，在实际业务场景中需要针对场景特点针对性调优。<br>
 Document(page_content='繁殖\n地块选择：应选择地势较高、阳光充足、通风条件好及肥力<br>
较好的地块种植。藜麦不宜重茬，忌连作，应合理轮作倒茬。前茬以大豆、薯类最好，其次是<br>
玉米、高粱等。\xa0[4]\xa0\n施肥整地：早春土壤刚解冻，趁气温尚低、土壤水分蒸发慢的时<br>
候，施足底肥，达到土肥融合，壮伐蓄水。播种前每降1次雨及时耙耱1次，做到上虚下实，干<br>
旱时只耙不耕，并进行压实处理。一般每亩（667平方米/亩，下同）施腐熟农家肥1000-2000千<br>
克、硫酸钾型复合肥20-30千克。如果土壤比较贫瘠，可适当增加复合肥的施用量。\xa0[4]', <br>
metadata={'source': './藜.txt'}),<br>
...]<br>
from langchain.embeddings import HuggingFaceBgeEmbeddings<br>
from langchain.vectorstores import Chroma<br>
# embedding model: m3e-base<br>
model_name = "moka-ai/m3e-base"<br>
model_kwargs = {'device': 'cpu'}<br>
encode_kwargs = {'normalize_embeddings': True}<br>
embedding = HuggingFaceBgeEmbeddings(<br>
                model_name=model_name,<br>
                model_kwargs=model_kwargs,<br>
                encode_kwargs=encode_kwargs,<br>
                query_instruction="为文本生成向量表示用于文本检索"<br>
            )<br>
# load data to Chroma db<br>
db = Chroma.from_documents(documents, embedding)<br>
# similarity search<br>
db.similarity_search("藜一般在几月播种？")<br>
template = '''<br>
        【任务描述】<br>
        请根据用户输入的上下文回答问题，并遵守回答要求。<br>
        【背景知识】<br>
        {{context}}<br>
        【回答要求】<br>
        - 你需要严格根据背景知识的内容回答，禁止根据常识和已知信息回答问题。<br>
        - 对于不知道的信息，直接回答“未找到相关答案”<br>
</p>

<h2>第 4 页</h2>

<p>3.6 RetrievalqaChain构建<br>
这里采用ConversationalRetrievalChain，ConversationalRetrievalQA chain 是建立在 RetrievalQAChain <br>
之上，提供历史聊天记录组件。如下面定义了memory来追踪聊天记录，在流程上，先将历史问题和当前<br>
输入问题融合为一个新的独立问题，然后再进行检索，获取问题相关知识，最后将获取的知识和生成的新<br>
问题注入Prompt让大模型生成回答。<br>
3.7 高级用法<br>
针对多轮对话场景，增加 question_generator对历史对话记录进行压缩生成新的question，增加<br>
combine_docs_chain对检索得到的文本进一步融合<br>
        -----------<br>
        {question}<br>
        '''<br>
from langchain import LLMChain<br>
from langchain_wenxin.llms import Wenxin<br>
from langchain.prompts import PromptTemplate<br>
from langchain.memory import ConversationBufferMemory<br>
from langchain.chains import ConversationalRetrievalChain<br>
from langchain.prompts.chat import ChatPromptTemplate, SystemMessagePromptTemplate, <br>
HumanMessagePromptTemplate<br>
# LLM选型<br>
llm = Wenxin(model="ernie-bot", baidu_api_key="baidu_api_key", <br>
baidu_secret_key="baidu_secret_key")<br>
retriever = db.as_retriever()<br>
memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)<br>
qa = ConversationalRetrievalChain.from_llm(llm, retriever, memory=memory)<br>
qa({"question": "藜怎么防治虫害？"})<br>
&gt;&gt;&gt;<br>
{'question': '藜怎么防治虫害？',<br>
 'chat_history': [HumanMessage(content='藜怎么防治虫害？'),<br>
  AIMessage(content='藜麦常见虫害有象甲虫、金针虫、蝼蛄、黄条跳甲、横纹菜蝽、萹蓄齿<br>
胫叶甲、潜叶蝇、蚜虫、夜蛾等。防治方法：可每亩用3%的辛硫磷颗粒剂2-2.5千克于耕地前均<br>
匀撒施，随耕地翻入土中。也可以每亩用40%的辛硫磷乳油250毫升，加水1-2千克，拌细土20-<br>
25千克配成毒土，撒施地面翻入土中，防治地下害虫。')],<br>
 'answer': '藜麦常见虫害有象甲虫、金针虫、蝼蛄、黄条跳甲、横纹菜蝽、萹蓄齿胫叶甲、<br>
潜叶蝇、蚜虫、夜蛾等。防治方法：可每亩用3%的辛硫磷颗粒剂2-2.5千克于耕地前均匀撒施，<br>
随耕地翻入土中。也可以每亩用40%的辛硫磷乳油250毫升，加水1-2千克，拌细土20-25千克配<br>
成毒土，撒施地面翻入土中，防治地下害虫。'}<br>
from langchain import LLMChain<br>
from langchain.prompts import PromptTemplate<br>
from langchain.memory import ConversationBufferMemory<br>
</p>

<h2>第 5 页</h2>

<p>from langchain.chains import ConversationalRetrievalChain, StuffDocumentsChain<br>
from langchain.chains.qa_with_sources import load_qa_with_sources_chain<br>
from langchain.prompts.chat import ChatPromptTemplate, SystemMessagePromptTemplate, <br>
HumanMessagePromptTemplate<br>
# 构建初始 messages 列表，这里可以理解为是 openai 传入的 messages 参数<br>
messages = [<br>
  SystemMessagePromptTemplate.from_template(qa_template),<br>
  HumanMessagePromptTemplate.from_template('{question}')<br>
]<br>
# 初始化 prompt 对象<br>
prompt = ChatPromptTemplate.from_messages(messages)<br>
llm_chain = LLMChain(llm=llm, prompt=prompt)<br>
combine_docs_chain = StuffDocumentsChain(<br>
    llm_chain=llm_chain,<br>
    document_separator="\n\n",<br>
    document_variable_name="context",<br>
)<br>
q_gen_chain = LLMChain(llm=llm, <br>
prompt=PromptTemplate.from_template(qa_condense_template))<br>
qa = ConversationalRetrievalChain(combine_docs_chain=combine_docs_chain,<br>
                                  question_generator=q_gen_chain,<br>
                                  return_source_documents=True,<br>
                                  return_generated_question=True,<br>
                                  retriever=retriever)<br>
print(qa({'question': "藜麦怎么防治虫害？", "chat_history": []}))<br>
&gt;&gt;&gt;<br>
{'question': '藜怎么防治虫害？',<br>
'chat_history': [],<br>
'answer': '根据背景知识，藜麦常见虫害有象甲虫、金针虫、蝼蛄、黄条跳甲、横纹菜蝽、萹<br>
蓄齿胫叶甲、潜叶蝇、蚜虫、夜蛾等。防治方法如下：\n\n1. 可每亩用3%的辛硫磷颗粒剂2-<br>
2.5千克于耕地前均匀撒施，随耕地翻入土中。\n2. 也可以每亩用40%的辛硫磷乳油250毫升，<br>
加水1-2千克，拌细土20-25千克配成毒土，撒施地面翻入土中，防治地下害虫。\n\n以上内容<br>
仅供参考，如果需要更多信息，可以阅读农业相关书籍或请教农业专家。',<br>
'source_documents': [<br>
Document(page_content='病害：主要防治叶斑病，使用12.5%的烯唑醇可湿性粉剂3000-4000倍<br>
液喷雾防治，一般防治1-2次即可收到效果。\xa0[4]\xa0\n虫害：藜麦常见虫害有象甲虫、金<br>
针虫、蝼蛄、黄条跳甲、横纹菜蝽、萹蓄齿胫叶甲、潜叶蝇、蚜虫、夜蛾等。防治方法：可每<br>
亩用3%的辛硫磷颗粒剂2-2.5千克于耕地前均匀撒施，随耕地翻入土中。也可以每亩用40%的辛<br>
硫磷乳油250毫升，加水1-2千克，拌细土20-25千克配成毒土，撒施地面翻入土中，防治地下害<br>
虫', metadata={'source': './藜.txt'}),<br>
Document(page_content='中期管理\n在藜麦8叶龄时，将行中杂草、病株及残株拔掉，提高整<br>
齐度，增加通风透光，同时，进行根部培土，防止后期倒伏。\xa0[4]', metadata={'source': <br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="13-基于langchain RAG问答应用实战_page6_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>知识星球<br>
'./藜.txt'})], 'generated_question': '藜怎么防治虫害？'}<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:27</p>
        </div>
    </div>
</body>
</html>