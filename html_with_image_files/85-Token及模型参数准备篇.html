<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>85-Token及模型参数准备篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>85-Token及模型参数准备篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="85-Token及模型参数准备篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="85-Token及模型参数准备篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="85-Token及模型参数准备篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>Token及模型参数准备篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年09月29日 12:06<br>
1. 预训练数据 Token 重复 是否影响 模型性能？<br>
2. SFT需要训练Token数？<br>
知识星球<br>
• 多轮epoch的训练会降低模型性能；<br>
• 更大规模的数据集会缓解重复epochs对模型性能下降的影响；<br>
• 提高数据集的质量也无法挽救重复训练带来的过拟合；<br>
• 小计算量模型的过拟合趋势与大计算量的差不多；<br>
• 多样的训练目标不一定减轻多Epoch的性能下降；<br>
• Dropout是一个被大语言模型忽视的正则技术，虽然慢，但是可以降低多epochs的影响；<br>
• 在训练过程中逐渐使用dropout是有效的策略；<br>
• 少量高质量、多样性的数据，也可以训练出效果优秀的SFT模型<br>
扫码加<br>
查看更多<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:28</p>
        </div>
    </div>
</body>
</html>