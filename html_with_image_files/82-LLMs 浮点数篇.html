<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>82-LLMs 浮点数篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>82-LLMs 浮点数篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="82-LLMs 浮点数篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="82-LLMs 浮点数篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="82-LLMs 浮点数篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>LLMs 浮点数篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 19:14<br>
一、fp32和fp16的区别，混合精度的原理<br>
二、半精度是什么？<br>
半精度是指使用16位二进制浮点数（half-precision floating point）来表示数字的数据类型，可以加速计算和减小<br>
内存占用。<br>
三、半精度的理论原理是什么？<br>
半精度使用16位二进制浮点数来表示数字，其中1位表示符号位，5位表示指数，10位表示尾数。相比于单精度<br>
（32位）和双精度（64位）的浮点数，半精度的表示范围和精度更小，但可以通过降低内存占用和加速计算来实<br>
现高效的运算。<br>
知识星球<br>
• LLMs 浮点数篇<br>
• 一、fp32和fp16的区别，混合精度的原理<br>
• 二、半精度是什么？<br>
• 三、半精度的理论原理是什么？<br>
• fp32：32位浮点数，占用4字节，提供高精度的数值表示。<br>
• fp16：16位浮点数，占用2字节，提供相对较低的数值表示精度，但在许多情况下仍足够。<br>
• 混合精度指的是在模型训练中同时使用fp16和fp32，以在计算中提高效率。通常，模型参数使用fp16表示，<br>
而梯度和累积值使用fp32表示。这样可以减少内存占用和计算开销，加速训练过程。<br>
扫码加<br>
查看更多<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:27</p>
        </div>
    </div>
</body>
</html>