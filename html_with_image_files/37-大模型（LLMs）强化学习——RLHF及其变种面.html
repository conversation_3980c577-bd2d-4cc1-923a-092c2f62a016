<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>37-大模型（LLMs）强化学习——RLHF及其变种面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>37-大模型（LLMs）强化学习——RLHF及其变种面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）强化学习——RLHF及其变种面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 20:47<br>
一、介绍一下 LLM的经典预训练Pipeline？<br>
• 大模型（LLMs）强化学习——RLHF及其变种面<br>
• 一、介绍一下 LLM的经典预训练Pipeline？<br>
• 二、预训练（Pre-training）篇<br>
• 2.1 具体介绍一下 预训练（Pre-training）？<br>
• 三、有监督微调（Supervised Tinetuning）篇<br>
• 3.1 具体介绍一下 有监督微调（Supervised Tinetuning）？<br>
• 3.2 有监督微调（Supervised Tinetuning）的训练数据格式是什么样？<br>
• 3.3 预训练（Pre-training） vs 有监督微调（Supervised Tinetuning）区别？<br>
• 四、对齐（Alignment）篇<br>
• 4.1 简单介绍一下 对齐（Alignment）？<br>
• 五、Reinforcement Learning with Human Feedback (RLHF)篇<br>
• 5.1 简单介绍一下 RLHF 流程？<br>
• 5.2 如何在在预训练好的模型上进行有监督微调？<br>
• 5.3 如何在有监督微调模型基础上创建一个RM模型？<br>
• 5.4 如何基于RM模型使用PPO算法微调SFT模型？<br>
• 5.5 instructGPT的原理，讲讲rlhf和reward？<br>
• 六、LLaMA 2 的 RLHF 篇<br>
• 6.1 介绍一下 LLaMA 2 的 RLHF？<br>
• 6.2 LLaMA 2 中 Margin Loss 的 实现逻辑？<br>
• 6.3 LLaMA 2 中 两个RM模型 的 实现逻辑？<br>
• 6.4 LLaMA 2 中 拒绝采样 逻辑？<br>
• 七、 RLHF 替代方案篇<br>
• 7.1 为什么需要 RLHF 替代方案？<br>
• 7.2 RLHF 有哪些替代方案？<br>
• 替代方案 1：Constitutional AI: Harmlessness from AI Feedback<br>
• 替代方案 2：The Wisdom of Hindsight Makes Language Models Better Instruction <br>
Followers<br>
• 替代方案 3：Direct Preference Optimization: Your Language Model is Secretly a <br>
Reward Model<br>
• 替代方案 4：Reinforced Self-Training (ReST) for Language Modeling<br>
• 替代方案 5：RLAIF: Scaling Reinforcement Learning from Human Feedback with AI <br>
Feedback<br>
• 八、 RLHF 实践篇<br>
• 8.1 RLHF 训练过程，怎么选取最优 checkpoint？<br>
• 参考<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>目前基于Transformer decoder的LLM，比如ChatGPT、LLaMA、baichuan等，通常都会有基于预训练的base模<br>
型和在base模型至少使用RLHF微调的Chat模型，Chat模型的训练一般都包括如下三个步骤：预训练，有监督微<br>
调和对齐。<br>
二、预训练（Pre-training）篇<br>
2.1 具体介绍一下 预训练（Pre-training）？<br>
预训练（Pre-training）：利用数十亿到数万亿个token的庞大文本语料库 对模型继续 预训练，使 模型 能够 根据<br>
提供的文本来预测「下一个单词」。<br>
三、有监督微调（Supervised Tinetuning）篇<br>
3.1 具体介绍一下 有监督微调（Supervised Tinetuning）？<br>
有监督微调（Supervised Tinetuning）:虽然 SFT 训练目标和 预训练（Pre-training）类似，也是 需要模型 预测<br>
「下一个单词」，但是需要人工标注的指令数据集，其中模型的输入是一个指令（根据任务的不同，也可能包含<br>
一段输入文本），输出为模型的预期回复内容。<br>
1. 在预训练阶段，模型会从大量无标注文本数据集中学习通用知识；<br>
2. 使用「有监督微调」（SFT）优化模型以更好地遵守特定指令；<br>
3. 使用对齐技术使LLM可以更有用且更安全地响应用户提示。<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>3.2 有监督微调（Supervised Tinetuning）的训练数据格式是什么样？<br>
Instruction: "Write a limerick about a pelican."<br>
指令：“写一首关于鹈鹕的打油诗。“<br>
Output: "There once was a pelican so fine..."<br>
输出：“从前有一只鹈鹕很好...“<br>
模型会把“Write a limerick about a pelican”作为输入，逐个token进行预测，输出“There once was a pelican so <br>
fine...”<br>
3.3 预训练（Pre-training） vs 有监督微调（Supervised Tinetuning）区别？<br>
四、对齐（Alignment）篇<br>
4.1 简单介绍一下 对齐（Alignment）？<br>
对齐（Alignment）：通过微调的方式，将语言模型与人类的偏好、价值观进行对齐，这也是RLHF机制发挥的地<br>
方。<br>
• 相同点：<br>
• 训练目标相同：模型需要根据提供的文本来预测「下一个单词」；<br>
• 不同点：<br>
• 训练数据量不同：有监督微调（Supervised Tinetuning）需要训练数据量比 预训练（Pre-training） <br>
小很多；<br>
• 训练数据格式不同：有监督微调（Supervised Tinetuning）需要人工标注的训练数据，预训练（Pre-<br>
training） 不需要；<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page4_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>五、Reinforcement Learning with Human Feedback (RLHF)篇<br>
5.1 简单介绍一下 RLHF 流程？<br>
5.2 如何在在预训练好的模型上进行有监督微调？<br>
先收集一个Prompts集合，并要求标注人员写出高质量的回复，然后使用该数据集以监督的方式微调预训练的基<br>
础模型。<br>
5.3 如何在有监督微调模型基础上创建一个RM模型？<br>
对于每个Prompt，要求有监督微调后的LLM生成四到九个回复，再由标注人员根据个人偏好对所有回复进行排<br>
序。虽然排序过程很耗时，但工作量还是比第一步的有监督数据集构建要少一些。<br>
1. 在预训练好的模型上进行「有监督微调」（SFT）；<br>
2. 在有监督微调模型基础上创建一个reward model（RM）模型；<br>
3. 基于RM模型使用PPO算法微调SFT模型；<br>
</p>

<h2>第 5 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page5_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page5_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>在处理排序数据时，使用了一个奖励模型RM，RM来自RLHF第一步的「有监督微调语言模型」（SFT），SFT<br>
的输出通过一个回归层（单个输出节点）转换为奖励分数，即可称为RM模型。<br>
5.4 如何基于RM模型使用PPO算法微调SFT模型？<br>
基于RM模型使用proximal policy optimization (PPO)算法微调SFT模型<br>
5.5 instructGPT的原理，讲讲rlhf和reward？<br>
instructGPT是一种基于强化学习的文本生成模型，其核心原理涉及两个概念：RLHF（Reinforcement Learning <br>
from Human Feedback）和reward shaping（奖励塑造）。<br>
通过RLHF和reward shaping的结合，instructGPT能够通过人类评估者的反馈指导模型的生成过程，并逐步提升<br>
生成文本的质量和一致性。<br>
• RLHF：在训练instructGPT时，首先使用有人类生成的示例对模型进行预训练。然后，通过与人类评估者进<br>
行交互，收集评估结果，以创建一个用于强化学习的数据集。该数据集包含了人类评估者对生成结果的评分<br>
或反馈，用于指导模型的强化学习训练。<br>
• Reward shaping：为了更好地引导模型的训练，reward shaping用于调整模型的奖励信号。通过将人类评估<br>
者的反馈与模型生成的文本进行比较，可以计算出一个差异度量，用作奖励信号的一部分。这样，模型可以<br>
根据这个奖励信号进行训练，并进行强化学习的训练。模型根据当前的状态（对话历史）生成文本，并通过<br>
奖励信号来评估生成文本的质量。模型的目标是最大化预期累积奖励，从而生成更高质量的文本。<br>
</p>

<h2>第 6 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page6_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page6_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>六、LLaMA 2 的 RLHF 篇<br>
6.1 介绍一下 LLaMA 2 的 RLHF？<br>
Llama-2-chat在第一步RLHF微调上使用相同的指令数据，但在第二步使用了两个奖励模型；通过多个阶段的不<br>
断进化，奖励模型也会根据Llama-2-chat模型出现的错误进行更新；并且增加了拒绝采样（rejection <br>
sampling）步骤。<br>
6.2 LLaMA 2 中 Margin Loss 的 实现逻辑？<br>
eg：四个回复的排序结果为A&lt;C&lt; D&lt;B，那么就可以得到六个对比结果：A &lt; C，A &lt; D ，A &lt; B，C &lt; D，C &lt; <br>
B，D &lt; B<br>
在排序训练时中，Llama 2相比InstructGPT增加了边际损失：<br>
其中，rθ（x，y）是提示x和生成的回复y的标量分数输出; θ为模型权重; σ是将层输出转换为范围<br>
从0到1的分数的逻辑S形函数; yc是由标注人员选择的更优回复; yr是较差的回复。m(r)可以调节<br>
两个回复之间的差值，如果对比结果为「显著更好」，则会增加梯度值，加快更新速度。<br>
6.3 LLaMA 2 中 两个RM模型 的 实现逻辑？<br>
Llama 2中的两个奖励模型：<br>
用于模型优化的最终奖励函数会将两个分数进行线性组合。<br>
• 标准InstructGPT 中 RLHF PPO方法 思路：对同一个提示下的4-9个模型输出并进行排序。<br>
• Llama 2 的 Margin Loss：每次只能看到两个（而非4-9个）回复并进行对比，但新增了一个边际（margin）<br>
标签，对比结果可以为「显著更好」（significantly better）和「好的不明显」（negligibly better）<br>
• 侧重「有用性」（helpfulness）<br>
• 「安全性」（safety）<br>
</p>

<h2>第 7 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page7_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page7_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>6.4 LLaMA 2 中 拒绝采样 逻辑？<br>
Llama 2 使用了一个训练流水线，同时使用PPO和拒绝采样算法，迭代地产生多个RLHF模型（从RLHF-V1到<br>
RLHF-V5），模型在拒绝采样时会得到K个输出，并使用最高奖励的输出更新梯度，而PPO每次只基于单样本进<br>
行更新。<br>
在监督微调的初始阶段之后，模型只使用拒绝采样进行训练，然后再结合拒绝采样和PPO。<br>
七、 RLHF 替代方案篇<br>
7.1 为什么需要 RLHF 替代方案？<br>
虽然 RLHF在InstructGPT和Llama 2论文中被证明是有效的，但是RLHF的过程是比较复杂的。<br>
</p>

<h2>第 8 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page8_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>7.2 RLHF 有哪些替代方案？<br>
替代方案 1：Constitutional AI: Harmlessness from AI Feedback<br>
论文名称：Constitutional AI: Harmlessness from AI Feedback<br>
论文链接：https://arxiv.org/abs/2212.08073<br>
论文 提出了一种基于人类提供的规则列表的自我训练机制。与前面提到的InstructGPT论文类似，也使用了强化<br>
学习方法。<br>
上图中的「红队」（Red Team）指的是测试目标系统的防御能力，即外部或内部专家模拟潜在对手的过程，通<br>
过模仿现实世界攻击者的战术、技术和程序来挑战、测试并最终改进系统。<br>
替代方案 2：The Wisdom of Hindsight Makes Language Models Better Instruction Followers<br>
论文名称：The Wisdom of Hindsight Makes Language Models Better Instruction Followers<br>
论文链接：https://arxiv.org/abs/2302.05206<br>
论文提出了一种基于重新标记的监督微调方法HIR，该方法在12个BigBench任务上优于RLHF。<br>
HIR是如何工作的？简而言之，HIR方法包括两个步骤，即采样和训练。在采样步骤中，Prompt和指令输入给<br>
LLM来获取答案，根据对齐得分，在训练阶段适当的地方重新标注指令；然后，重新标记的指令和原始的Prompt<br>
用于微调LLM。使用这种重新标记的方法，研究人员有效地将失败案例（LLM创建的输出与原始指令不匹配的案<br>
例）转化为有用的训练数据，用于监督学习。<br>
</p>

<h2>第 9 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page9_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>替代方案 3：Direct Preference Optimization: Your Language Model is Secretly a Reward Model<br>
论文名称：Direct Preference Optimization: Your Language Model is Secretly a Reward Model<br>
论文链接：https://arxiv.org/abs/2305.18290<br>
直接偏好优化（DPO）是具有PPO的RLHF的替代方案，其中研究人员表明，在RLHF中拟合奖励模型的交叉熵<br>
损失可以直接用于微调LLM。根据他们的基准，使用DPO更有效，而且在响应质量方面通常也优于RLHF/PPO。<br>
</p>

<h2>第 10 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page10_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page10_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>替代方案 4：Reinforced Self-Training (ReST) for Language Modeling<br>
论文名称：Reinforced Self-Training (ReST) for Language Modeling<br>
论文链接：https://arxiv.org/abs/2308.08998<br>
ReST是人类反馈强化学习（RLHF）的一种替代方案，它使LLM与人类偏好保持一致。ReST使用采样方法创建<br>
改进的数据集，在质量越来越高的子集上迭代训练，以完善其奖励函数。根据作者的说法，与标准的在线RLHF<br>
方法（如具有近端策略优化的RLHF，PPO）相比，ReST通过离线生成训练数据集实现了更高的效率，但缺少与<br>
InstructGPT或Llama 2中使用的标准RLHF PPO方法的全面比较。<br>
替代方案 5：RLAIF: Scaling Reinforcement Learning from Human Feedback with AI Feedback<br>
</p>

<h2>第 11 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page11_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>论文名称：RLAIF: Scaling Reinforcement Learning from Human Feedback with AI Feedback<br>
论文链接：https://arxiv.org/abs/2309.00267<br>
最近的人工智能反馈强化学习（RLAIF）研究表明，RLHF中奖励模型训练的评级不一定必须由人类提供，而是<br>
可以由LLM生成（此处：PaLM 2）。标注人员在一半的案例中更喜欢RLAIF模型，也就意味着两个模型的差距并<br>
不大，RLHF和RLAIF都大大优于纯通过监督指令微调训练的模型。<br>
这项研究的结果非常有用和有趣，因为它基本上意味着我们可能能够使基于RLHF的训练更加高效和容易。然<br>
而，这些RLAIF模型在专注于信息内容的安全性和真实性的定性研究中的表现还有待观察，而人类偏好研究仅部<br>
分捕捉到了这一点。<br>
八、 RLHF 实践篇<br>
8.1 RLHF 训练过程，怎么选取最优 checkpoint？<br>
RLHF 训练过程，因为 Reward Model 输出的只是一个近似奖励（Proxy Reward），<br>
导致不能完全相信训练过程中的 Reward 变化，“更高” 的 Reward 不一定意味着 “更好” 的效果。<br>
可以看这一张图片：<br>
• 动机<br>
</p>

<h2>第 12 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page12_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page12_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>注：横轴为训练模型 &amp; 初始模型之间的KL，纵轴为 reward 分数；虚线是近似 reward（RM 打出<br>
的分数），实线是真实的 reward（大多数情况下无法直接获得）<br>
从上图可以看到：随着「训练模型」和「初始模型」之间的 KL（可简单理解为差异）越大，模型的「真实分<br>
数」会先逐步提升，到达某个峰值后逐渐减小（图中实线），但「近似分数」（由 Reward Model 打出来的分<br>
数）却一直在稳步上升（图中虚线），显然，在「真实分数」曲线的「最高点」就是我们所期望得到「最优模<br>
型」的时间点。<br>
但，现在的问题是：根本无法获得「真实分数」，我们该如何找到这个「最高点」呢？<br>
我们假定：真实 reward 曲线与「当前模型和初始模型」之间的 KL 存在某种关系。<br>
由于 KL 是一个可以被实时计算的数值，如果我们能够找出这种存在的「关系」，那我们就能找出真实 reward <br>
的最高点对应的 KL 值是多少，从而找出最优模型。<br>
OpenAI 帮我们找到了这个计算公式：<br>
不同训练方法对应的公式稍有不同<br>
其中，BON（best of n）也叫 reject sampling，RL 使用 PPO，我们发现不同的训练方式对应的<br>
公式也稍有不同。<br>
BON 指先让模型生成一堆 response，再利用 RM 从中挑出最好的几个回复用于后续模型训练。<br>
公式里最关键的就是 3 个参数：α 、β 和 d。<br>
d 被定义为初始模型和当前模型的 KL 开根号，这个比较好算；<br>
剩下的就是 α 和 β 该等于多少。<br>
• 真实 Reward 的估算公式<br>
</p>

<h2>第 13 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page13_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page13_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page13_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>论文中表明：α 和 β 这 2 个值跟「Reward Model 大小」和「Reward Model 训练数据规模」等<br>
因素有关。<br>
制变量法，为了探究 RM 的大小和 α、β之间的关系，<br>
实验中固定了 actor 模型的大小（1.2B）、训练 RM 所用的数据集大小（9w条），<br>
下图是使用 BON 作为训练方法，不同 RM 大小的实验结果：<br>
不同 RM 规模对应的 α 和 β 的值<br>
根据图中给的点，挑选 1e7、1e8 和 1e9 这 3 个规模对应的 α 和 β 值，<br>
将上述参数代入 R_bon(d) 公式，并尝试绘制 reward 曲线图，结果如下：<br>
3 种 RM 规模在 0 到 3.5 KL 区间内对应的真实 reward 曲线图<br>
曲线图的走势和论文中大致相同，证明该公式有效。<br>
从图中我们大致可以得出以下几个结论：<br>
• α 和 β 的值<br>
1. 相同训练数据下，Reward Model 越大 actor 模型能够获得更高的真实 reward。<br>
2. Reward Mode 越大，能够支持模型在「不偏离真实奖励的路途上走更远」，即在更大的 KL 处发生下降转<br>
折。<br>
</p>

<h2>第 14 页</h2>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page14_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page14_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="37-大模型（LLMs）强化学习——RLHF及其变种面_page14_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>当然，论文中的数据存在一定的局限性，不一定在所有的任务、所有的规模下都适用，<br>
不过这种研究 scaling law 的思路，以及提出用 KL 来作为一种可能衡量「学习程度」的指标是非常有意义的。<br>
除了上述这 2 个 R 和 KL 之间的计算公式外，论文中还提了一些其他有借鉴意义的经验性结论。<br>
为了探究 RM Dataset 的规模对最终模型的影响，实验中固定在 12M 的 RM 下进行实验，结果如下：<br>
从上图中可以看到，RM 数据集越大对最终的提升就越大（这很直觉），但数据集最少也需要超过 2000。<br>
因为如果训练数据量低于 2k，无论 RM 在哪个规模、无论使用 BON 还是 RL，对模型最终的提升都非常小。<br>
当然，论文中 2k 这个数字只是在 3M~3B 大小的模型下得出的结论，<br>
至于更大的模型大小是否还符合 2k 这个下限我们就不得而知。<br>
探究完了 RM 的 Scaling Law，论文中还对 Policy Model 的大小做了对比实验。<br>
文中选用 1.2B 和 6B 这 2 个大小的模型进行对比，固定 RM 大小为 12M，结果如下：<br>
1.2B 和 6B 在 2 种不同训练方式下的对比实验<br>
从上图可以得出 2 个结论：<br>
知识星球<br>
• Reward Model 训练数据集的 Scaling Law<br>
• Policy Model 的 Scaling Law<br>
1. Policy Model 越大，利用 RM 做提升的收益就越小：在 BON 下，1.2B 模型提升大约为 0.7 分（0 -&gt; <br>
0.7），6B 模型提升大约为 0.35 分（0.4 -&gt; 0.75），不过这是因为越大的模型初始分就较高导致提升没有那<br>
么大，绝对分数上来看还是模型越大越好的;<br>
2. 无论模型规模如何，最优 Reward 对应的 KL 值是一样的：这一点比较反直觉，我们通常会认为较大的模型<br>
应该能够更快的 hacking 掉 reward model，应该在更小的 KL 处就达到最高的 reward 峰值，但实验结果并<br>
非如此（在 RL 实验中 2 个峰值对应的 KL 几乎重合）。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:28</p>
        </div>
    </div>
</body>
</html>