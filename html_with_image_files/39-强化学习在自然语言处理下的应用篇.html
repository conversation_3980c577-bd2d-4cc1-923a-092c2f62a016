<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>39-强化学习在自然语言处理下的应用篇</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>39-强化学习在自然语言处理下的应用篇</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page1_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page1_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page1_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<p>强化学习在自然语言处理下的应用篇<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2024年01月27日 20:47<br>
一、强化学习基础面<br>
1.1 介绍一下强化学习？<br>
强化学习（Reinforcement Learning）是一种时序决策学习框架，通过智能体和环境交互<br>
得到的奖励<br>
从而来优化策略 π，使其能够在环境中自主学习。<br>
1.2 介绍一下强化学习 的 状态（States） 和 观测（Observations）？<br>
• 强化学习在自然语言处理下的应用篇<br>
• 一、强化学习基础面<br>
• 1.1 介绍一下强化学习？<br>
• 1.2 介绍一下强化学习 的 状态（States） 和 观测（Observations）？<br>
• 1.3 强化学习 有哪些 动作空间（Action Spaces），他们之间的区别是什么？<br>
• 1.4 强化学习 有哪些 Policy策略？<br>
• 1.5 介绍一下 强化学习 的 轨迹？<br>
• 1.6 介绍一下 强化学习 的 奖赏函数？<br>
• 1.7 介绍一下 强化学习问题？<br>
• 二、RL发展路径（至PPO）<br>
• 2.1 介绍一下 强化学习 中 优化方法 Value-based？<br>
• 2.2 介绍一下 强化学习 中 贝尔曼方程？<br>
• 2.3 介绍一下 强化学习 中 优势函数Advantage Functions？<br>
• 致谢<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img6.png" alt="图片 6" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 6</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img7.png" alt="图片 7" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 7</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page2_img8.png" alt="图片 8" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 8</p>
</div>

<p>1.3 强化学习 有哪些 动作空间（Action Spaces），他们之间的区别是什么？<br>
其区别会影响policy网络的实现方式。<br>
1.4 强化学习 有哪些 Policy策略？<br>
1.5 介绍一下 强化学习 的 轨迹？<br>
1.6 介绍一下 强化学习 的 奖赏函数？<br>
智能体的目标是最大化行动轨迹的累计奖励：<br>
1.7 介绍一下 强化学习问题？<br>
二、RL发展路径（至PPO）<br>
• 状态（States）：对于世界状态的完整描述<br>
• 观测（Observations）：对于一个状态的部分描述，可能会缺失一些信息。当O=S时，称O为完美信息/fully <br>
observed；O&lt;S时，称O为非完美信息/partially observed。<br>
• 离散动作空间：当智能体只能采取有限的动作，如下棋/文本生成<br>
• 连续动作空间：当智能体的动作是实数向量，如机械臂转动角度<br>
• 确定性策略Deterministic Policy： at = u(st)，连续动作空间<br>
• 随机性策略Stochastic Policy： at ~ π(·|st) ，离散动作空间<br>
• 轨迹：指的是状态和行动的序列<br>
1. 状态转换函数（transition function）：<br>
1. 初始状态是从初始状态分布中采样的，一般表示为<br>
• 核心问题：选择一种策略从而最大化预期收益<br>
1. 假设环境转换和策略都是随机的，则T步行动轨迹概率：<br>
1. 预期收益：<br>
1. 核心优化问题：找到最优策略<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page3_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page3_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page3_img4.png" alt="图片 4" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 4</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page3_img5.png" alt="图片 5" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 5</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page3_img6.png" alt="图片 6" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 6</p>
</div>

<p>2.1 介绍一下 强化学习 中 优化方法 Value-based？<br>
最优动作：<br>
2.2 介绍一下 强化学习 中 贝尔曼方程？<br>
所以，最优值函数的贝尔曼公式为：<br>
• value-based：状态的值 V(s) 或者 状态行动对(state-action pair) 的值Q(s,a) ，作为一种累积奖赏的估计，可<br>
以通过最大化值函数来优化得到最优策略<br>
1. 最优值函数（Optimal Value Function）：<br>
1. 最优动作-值函数（Optimal Action-Value Function）：<br>
1. 两者的关系：<br>
• 中心思想：当前值估计=当前奖赏+未来值估计<br>
</p>

<h2>第 4 页</h2>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page4_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page4_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<div class="image-container">
  <img src="39-强化学习在自然语言处理下的应用篇_page4_img3.png" alt="图片 3" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 3</p>
</div>

<p>2.3 介绍一下 强化学习 中 优势函数Advantage Functions？<br>
强化学习中，有时不需要知道一个行动的绝对好坏，而只需要知道它相对于其他action的相对优势。即<br>
知识星球<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:25</p>
        </div>
    </div>
</body>
</html>