<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>40-大模型（LLMs）训练集面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #007acc;
            padding-left: 15px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #444;
        }
        .image-container {
            margin: 20px 0;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-caption {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>40-大模型（LLMs）训练集面</h1>
        <h2>第 1 页</h2>

<div class="image-container">
  <img src="40-大模型（LLMs）训练集面_page1_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<div class="image-container">
  <img src="40-大模型（LLMs）训练集面_page1_img2.png" alt="图片 2" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 2</p>
</div>

<p>大模型（LLMs）训练集面<br>
来自： AiGC面试宝典<br>
宁静致远<br>
2023年12月24日 00:33<br>
1. SFT（有监督微调）的数据集格式？<br>
一问一答<br>
2. RM（奖励模型）的数据格式？<br>
一个问题 + 一条好回答样例 + 一条差回答样例<br>
3. PPO（强化学习）的数据格式？<br>
理论上来说，不需要新增数据。需要提供一些prompt，可以直接用sft阶段的问。另外，需要限制<br>
模型不要偏离原模型太远（ptx loss），也可以直接用sft的数据。<br>
4. 找数据集哪里找？<br>
推荐Alpaca-COT，数据集整理的非常全，眼花缭乱。<br>
5. 微调需要多少条数据？<br>
取决于预训练数据和微调任务的数据分布是否一致，分布一致，100条就够，分布差异大就需要多<br>
些数据，千条或者万条以上为佳。<br>
自己的任务复杂或者下游任务行业比较冷门，如药品名称识别任务，则需要较多监督数据。还有微<br>
调大模型时，一遍是记不住的。100条的微调数据，epochs=20才能稳定拟合任务要求。<br>
6. 有哪些大模型的训练集？<br>
预训练数据集togethercomputer/RedPajama-Data-1T「红睡衣」开源计划总共包括三部分：<br>
预训练数据集RedPajama-Data-1T已开源，包括七个子集，经过预处理后得到的token数量大致可<br>
以匹配Meta在原始LLaMA论文中报告的数量，并且数据预处理相关脚本也已开源。<br>
完整的RedPajama-Data-1T数据集需要的存储容量为压缩后3TB，解压后5TB。<br>
CoT微调数据集：Alpaca-CoT 里面包括常用的alpaca，CoT等数据集，有中文的。<br>
7. 进行领域大模型预训练应用哪些数据集比较好？<br>
通过分析发现现有的开源大模型进行预训练的过程中会加入数据、论文等数据。主要是因为这些数<br>
据的数据质量较高，领域相关性比较强，知识覆盖率（密度）较大，可以让模型更适应考试。给我<br>
• 高质量、大规模、高覆盖度的预训练数据集；<br>
• 在预训练数据集上训练出的基础模型；<br>
• 指令调优数据集和模型，比基本模型更安全、可靠。<br>
扫码加<br>
查看更多<br>
</p>

<h2>第 2 页</h2>

<p>们自己进行大模型预训练的时候提供了一个参考。同时领域相关的网站内容、新闻内容也是比较重<br>
要的数据。<br>
8. 如何选取和构建大模型微调数据？<br>
一般情况下我们数据的分布都是符合一个长尾分布的。主要的几个类别数据占据了90%的数据量，<br>
剩下的90%的类别只有10%的数据量。<br>
举个栗子：小红书上，query的意图识别里，美食，穿搭，旅游攻略类非常多，但是还有一些同学<br>
去搜大模型微调的数据技巧。<br>
如果说我们直接采样一批线上的图文文本，直接送给标注的话，会存在一个严重的问题：他们标注<br>
的数据大部分都是攻略类，技术类比较少，标了3个月才攒了几千条大模型技术文本，但是攻略类<br>
已经成几万了。<br>
这样搞肯定是不行的，人力成本方面的消耗是在是太大了，并且模型因为数据平衡的问题也没有特<br>
别好<br>
主动学习有两个基本原则，在监督训练的时候，注意主动发现数据的两个方面，一个是数据多样<br>
性，另外一个是数据的不确定性。这样讲是比较抽象的概念，那我们在大模型实践中如何体现呢？<br>
第一，数据的多样性。<br>
多样性即为数据的去重，去重这件事的核心是相似度度量，现在的相似度度量方法大家用的比较多<br>
的是基于对比学习构造的语义向量这套思路，当然简单的基于词袋或者tfidf的方案也是可以的。有<br>
了核心的相似度度量方法后，我们可以使用简单的onepass聚类方法进行过滤，考虑复杂一点的<br>
话，我们可以使用带优化目标的聚类：比如K-Center-Greedy算法，其约束条件是在最大化多样性<br>
的情况下，使指令数据集最小。<br>
另外，如果我们已经有了一批已经去重的人工处理过的高质量数据，那么我们如何寻找与这批数据<br>
不一样的数据呢？<br>
这里有一个非常简单实用的方案，并且这个方案可以用在很多其他的地方。<br>
我们简单地把已有的数据全部当成正样本打上1，然后待筛选的数据全部当成负样本打上0，我们使<br>
用deberta等构建二分类模型，并进行K-fold的交叉验证，在交叉验证过程中，选出每一个fold过程<br>
中的测试集合里概率接近于0的样本。<br>
通过这样的操作，就能把长得与已有数据不一样的数据给选出来了，并且这个过程是半监督的。<br>
这套方案也可以用在很多其他地方，比如数据质量选择，只要我们有一批已经确定标签/结果/标注<br>
的种子数据，就能通过这样的方法选出与种子数据长得比较像的，长得不像的。<br>
第二，数据的不确定性。<br>
数据的不确定性主要体现数据的质量筛选上，选取模型学的不那好的数据，模型没有把握的数据。<br>
最简单的，我们可以选出模型对应PPL值比较差的那批数据。如果是指令数据的话，比如大模型做<br>
题和对应的答案。我们可以把所有选项对应的概率之和计算出来，然后过滤出概率和比较低的那一<br>
批数据，这批数据就是模型“不太肯定”的样本，我们需要加强针对性的训练。<br>
当然这样可能有一个副作用，就是这批数据是质量比较差而不是模型学的不太好的。<br>
• 动机：在 微调大模型时，首先需要解决的问题是“选取和构建大模型微调数据”，那如何选择<br>
呢？<br>
• 问题一：什么样的 数据 才是 最优的 大模型微调数据？<br>
1. 数据的多样性：<br>
1. 数据的标注质量；<br>
2. 数据的不确定性；<br>
• 问题二：如何构建 大模型微调数据？<br>
3. 方法一：“self-instruct”的框架，通过自我生成来提升指令跟随能力。文章的流程是从语言模型<br>
中生成指令、输入和输出样本，然后在使用这些数据微调原始模型之前进行清洗。<br>
4. 方法二：“主动学习”<br>
</p>

<h2>第 3 页</h2>

<div class="image-container">
  <img src="40-大模型（LLMs）训练集面_page3_img1.png" alt="图片 1" style="max-width: 100%; height: auto;" />
  <p class="image-caption">图片 1</p>
</div>

<p>为此，我们还要借助reward model，这个reward model是广义的，他是一个质量的二分类模型。可<br>
以祭出我们的deberta，继续用标注数据进行做二分类，进行数据质量的判断。<br>
有了质量打分模型后，我们就可以判断一些指令数据的质量高低，并且据此选出模型真正不确定的<br>
数据。<br>
这个过程类似于手动的拒绝采样，核心是选择“模型不确定”+“数据质量达标”的那部分数据。<br>
知识星球<br>
• 总结一下:监督学习中主动学习的两个基本原则是寻找多样性的数据，模型不确定性的数据，在<br>
寻找的过程中，我们使用了一些小技巧，比如聚类去重，对抗半监督过滤，自建reward二分类<br>
等方法。这几个小技巧，学术上没有什么高深莫测的东西，都是实践中总结出来的好用的方<br>
法。<br>
</p>

        <div class="footer">
            <p>此文档由PDF自动转换生成</p>
            <p>转换时间: 2025-06-11 20:33:23</p>
        </div>
    </div>
</body>
</html>