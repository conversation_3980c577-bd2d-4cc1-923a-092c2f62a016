#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转HTML转换器使用示例
演示如何使用脚本进行各种转换操作
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 执行成功!")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print("❌ 执行失败!")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
    except Exception as e:
        print(f"❌ 执行异常: {str(e)}")


def main():
    """主函数 - 演示各种使用方式"""
    print("PDF转HTML转换器 - 使用示例")
    print("="*60)
    
    # 检查脚本是否存在
    script_path = "pdf_to_html_converter.py"
    if not Path(script_path).exists():
        print(f"❌ 脚本文件不存在: {script_path}")
        return
    
    # 示例1: 显示帮助信息
    run_command(
        f"python3 {script_path} --help",
        "显示帮助信息"
    )
    
    # 示例2: 转换当前目录的PDF文件（不递归）
    run_command(
        f"python3 {script_path} . --no-recursive -o ./demo_output",
        "转换当前目录的PDF文件到demo_output目录（不递归）"
    )
    
    # 示例3: 转换指定目录的PDF文件（递归）
    if Path("./test_pdfs").exists():
        run_command(
            f"python3 {script_path} ./test_pdfs -o ./recursive_output -v",
            "递归转换test_pdfs目录的所有PDF文件（详细模式）"
        )
    
    # 示例4: 在原目录生成HTML文件
    if Path("./sample_pdfs").exists():
        run_command(
            f"python3 {script_path} ./sample_pdfs --no-recursive",
            "在原目录生成HTML文件（不指定输出目录）"
        )
    
    print(f"\n{'='*60}")
    print("示例演示完成!")
    print("="*60)
    
    # 显示生成的文件
    output_dirs = ["./demo_output", "./recursive_output"]
    for output_dir in output_dirs:
        if Path(output_dir).exists():
            html_files = list(Path(output_dir).glob("*.html"))
            if html_files:
                print(f"\n在 {output_dir} 中生成了 {len(html_files)} 个HTML文件:")
                for html_file in html_files[:5]:  # 只显示前5个
                    print(f"  - {html_file.name}")
                if len(html_files) > 5:
                    print(f"  ... 还有 {len(html_files) - 5} 个文件")


if __name__ == "__main__":
    main()
