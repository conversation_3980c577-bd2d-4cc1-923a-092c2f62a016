#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转HTML图片功能测试脚本
验证图片提取和转换功能是否正常工作
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path


def run_test(description, command, expected_files=None):
    """运行测试并验证结果"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 命令执行成功!")
            
            # 检查预期文件是否存在
            if expected_files:
                for file_path in expected_files:
                    if Path(file_path).exists():
                        print(f"✅ 文件存在: {file_path}")
                    else:
                        print(f"❌ 文件不存在: {file_path}")
                        return False
            
            return True
        else:
            print("❌ 命令执行失败!")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行异常: {str(e)}")
        return False


def check_html_images(html_file, mode="file"):
    """检查HTML文件中的图片"""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if mode == "file":
            # 检查文件模式的图片引用
            if 'src="images/' in content:
                print(f"✅ {html_file} 包含文件模式图片引用")
                return True
            else:
                print(f"❌ {html_file} 不包含文件模式图片引用")
                return False
        elif mode == "embed":
            # 检查嵌入模式的图片
            if 'data:image/' in content:
                print(f"✅ {html_file} 包含嵌入模式图片")
                return True
            else:
                print(f"❌ {html_file} 不包含嵌入模式图片")
                return False
        
    except Exception as e:
        print(f"❌ 检查HTML文件失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("PDF转HTML图片功能测试")
    print("="*60)
    
    # 检查脚本是否存在
    script_path = "pdf_to_html_converter.py"
    if not Path(script_path).exists():
        print(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    # 查找测试用的PDF文件
    current_dir = Path.cwd()
    pdf_files = list(current_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 当前目录没有找到PDF文件，无法进行测试")
        return False
    
    # 选择一个可能包含图片的PDF文件进行测试
    test_pdf = None
    for pdf in pdf_files:
        if "图解" in pdf.name or "分布式" in pdf.name:
            test_pdf = pdf
            break
    
    if not test_pdf:
        test_pdf = pdf_files[0]  # 使用第一个PDF文件
    
    print(f"使用测试文件: {test_pdf.name}")
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 复制测试PDF到临时目录
        test_pdf_copy = temp_path / test_pdf.name
        shutil.copy2(test_pdf, test_pdf_copy)
        
        # 测试1: 独立文件模式
        output1 = temp_path / "output_files"
        success1 = run_test(
            "图片独立文件模式",
            f"python3 {script_path} {temp_path} -o {output1}",
            [output1 / f"{test_pdf.stem}.html", output1 / "images"]
        )
        
        if success1:
            # 检查HTML中的图片引用
            html_file = output1 / f"{test_pdf.stem}.html"
            check_html_images(html_file, "file")
            
            # 检查images目录中的图片文件
            images_dir = output1 / "images"
            if images_dir.exists():
                image_files = list(images_dir.glob("*.png"))
                print(f"✅ 提取了 {len(image_files)} 张图片")
                if image_files:
                    print(f"示例图片: {image_files[0].name}")
            else:
                print("❌ images目录不存在")
        
        # 测试2: 嵌入模式
        output2 = temp_path / "output_embed"
        success2 = run_test(
            "图片嵌入模式",
            f"python3 {script_path} {temp_path} -o {output2} --embed-images",
            [output2 / f"{test_pdf.stem}.html"]
        )
        
        if success2:
            # 检查HTML中的嵌入图片
            html_file = output2 / f"{test_pdf.stem}.html"
            check_html_images(html_file, "embed")
        
        # 测试3: 禁用图片模式
        output3 = temp_path / "output_no_images"
        success3 = run_test(
            "禁用图片模式",
            f"python3 {script_path} {temp_path} -o {output3} --no-images",
            [output3 / f"{test_pdf.stem}.html"]
        )
        
        if success3:
            # 检查是否没有图片
            html_file = output3 / f"{test_pdf.stem}.html"
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '<img' not in content:
                print(f"✅ {html_file} 正确地不包含图片")
            else:
                print(f"❌ {html_file} 意外地包含了图片")
        
        # 测试结果总结
        print(f"\n{'='*60}")
        print("测试结果总结:")
        print('='*60)
        
        total_tests = 3
        passed_tests = sum([success1, success2, success3])
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        
        if passed_tests == total_tests:
            print("🎉 所有图片功能测试通过！")
            return True
        else:
            print("❌ 部分测试失败，请检查问题")
            return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
