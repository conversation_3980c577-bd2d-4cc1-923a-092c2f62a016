# PDF批量转换为HTML工具

这是一个功能完整的Python脚本，可以将指定目录中的所有PDF文件批量转换为HTML格式。

## 功能特性

- ✅ 递归扫描目录查找PDF文件
- ✅ 批量转换PDF为HTML格式
- ✅ 保持原文件名，仅更改扩展名
- ✅ 支持指定输出目录或在原目录生成
- ✅ 完善的错误处理机制
- ✅ 实时进度显示
- ✅ 详细的转换结果摘要
- ✅ 命令行参数支持
- ✅ 生成美观的HTML格式

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install PyMuPDF tqdm
```

## 使用方法

### 基本用法

```bash
# 转换当前目录的所有PDF文件（递归）
python pdf_to_html_converter.py .

# 转换指定目录的PDF文件
python pdf_to_html_converter.py /path/to/pdf/directory
```

### 高级用法

```bash
# 指定输出目录
python pdf_to_html_converter.py /path/to/pdfs -o /path/to/output

# 不递归处理子目录
python pdf_to_html_converter.py /path/to/pdfs --no-recursive

# 显示详细日志
python pdf_to_html_converter.py /path/to/pdfs -v

# 组合使用
python pdf_to_html_converter.py /path/to/pdfs -o /output --no-recursive -v
```

## 命令行参数

| 参数 | 说明 |
|------|------|
| `input_dir` | 包含PDF文件的输入目录路径（必需） |
| `-o, --output` | 输出目录路径（可选，默认在原文件目录生成HTML） |
| `--no-recursive` | 不递归处理子目录 |
| `-v, --verbose` | 显示详细日志信息 |
| `-h, --help` | 显示帮助信息 |

## 输出格式

转换后的HTML文件具有以下特性：

- 响应式设计，适配不同屏幕尺寸
- 美观的CSS样式
- 按页面分割内容
- 包含转换时间戳
- 支持中文字体显示

## 错误处理

脚本会处理以下常见错误：

- 损坏的PDF文件
- 权限不足
- 磁盘空间不足
- 无效的文件路径
- 空的PDF文件

## 示例输出

```
============================================================
PDF批量转换为HTML工具
============================================================
2024-01-01 10:00:00 - INFO - 找到 5 个PDF文件
转换进度: 100%|██████████| 5/5 [00:30<00:00,  6.00s/文件]

============================================================
转换完成！结果摘要:
============================================================
总文件数: 5
成功转换: 4
转换失败: 1
耗时: 30.25 秒

失败的文件:
  - /path/to/corrupted.pdf
============================================================
```

## 技术实现

- **PDF处理**: 使用PyMuPDF库提取PDF文本内容
- **HTML生成**: 自定义HTML模板，包含CSS样式
- **进度显示**: 使用tqdm库显示转换进度
- **错误处理**: 完善的异常捕获和日志记录
- **文件操作**: 使用pathlib进行跨平台文件路径处理

## 注意事项

1. 转换质量取决于PDF文件的质量和格式
2. 图片和复杂布局可能无法完美转换
3. 大文件转换可能需要较长时间
4. 确保有足够的磁盘空间存储HTML文件

## 快速开始

### 方法一：使用便捷脚本（推荐）

**Windows用户：**
```bash
# 双击运行 convert_pdfs.bat 文件
# 或在命令行中运行：
convert_pdfs.bat
```

**Linux/Mac用户：**
```bash
# 运行Shell脚本
./convert_pdfs.sh
```

### 方法二：直接使用Python脚本

```bash
# 基本用法
python3 pdf_to_html_converter.py /path/to/pdfs

# 指定输出目录
python3 pdf_to_html_converter.py /path/to/pdfs -o /path/to/output
```

## 转换效果展示

转换后的HTML文件具有以下特点：

- 📱 **响应式设计**：自适应不同屏幕尺寸
- 🎨 **美观样式**：专业的CSS样式设计
- 📄 **分页显示**：按PDF原始页面分割内容
- 🕒 **时间戳**：包含转换时间信息
- 🔤 **中文支持**：完美支持中文字体显示

## 故障排除

### 常见问题

**Q: 提示"请安装PyMuPDF库"**
A: 运行 `pip install PyMuPDF` 或 `python3 -m pip install PyMuPDF`

**Q: 转换后的HTML文件为空**
A: 可能是PDF文件损坏或为扫描版PDF，建议使用OCR工具先处理

**Q: 权限错误**
A: 确保对输入和输出目录有读写权限

**Q: 中文显示乱码**
A: 脚本已设置UTF-8编码，如仍有问题请检查系统编码设置

**Q: 转换速度慢**
A: 大文件转换需要时间，可以使用 `-v` 参数查看详细进度

**Q: 内存不足**
A: 处理大量或大型PDF文件时可能需要更多内存，建议分批处理
